module.exports = {
  root: true,
  env: {
    es6: true,
    node: true,
    jest: true,
  },
  extends: ["eslint:recommended", "plugin:import/recommended", "plugin:import/typescript", "prettier"],
  plugins: ["unused-imports"],
  settings: {
    "import/resolver": {
      typescript: {
        alwaysTryTypes: true,
        project: ["tsconfig.json", "tsconfig.dev.json"],
      },
      node: true,
    },
    // Cache import resolution for better performance
    "import/cache": {
      lifetime: "Infinity"
    },
  },
  parser: "@typescript-eslint/parser",
  parserOptions: {
    tsconfigRootDir: __dirname,
    allowImportExportEverywhere: true,
    ecmaVersion: 2020,
    project: ["tsconfig.json", "tsconfig.dev.json"],
    sourceType: "module",
  },
  ignorePatterns: [
    "node_modules/*",
    "**/node_modules/*",
    "/lib/**/*",
    "/coverage/**/*",
    "*.js.map",
    "*.d.ts",
    "__mocks__/**/*",
    "scripts/**/*"
  ],
  overrides: [
    {
      files: ["**/*.ts"],
      parser: "@typescript-eslint/parser",
      extends: ["eslint:recommended", "plugin:@typescript-eslint/recommended", "prettier"],
      rules: {
        "@typescript-eslint/no-unused-vars": ["warn", { argsIgnorePattern: "^_" }],
        "@typescript-eslint/explicit-function-return-type": "off",
        "@typescript-eslint/explicit-module-boundary-types": "off",
        "@typescript-eslint/no-namespace": "off",
        "@typescript-eslint/no-empty-function": "off",
        "@typescript-eslint/no-non-null-assertion": "off",
        "@typescript-eslint/ban-ts-comment": "off",
        "@typescript-eslint/no-explicit-any": 1,
        // Disabled for performance and existing code compatibility
        "@typescript-eslint/naming-convention": "off",
        quotes: ["error", "double", { avoidEscape: true }],
        // Simplified import/order for better performance
        "import/order": [
          "warn",
          {
            groups: ["builtin", "external", "internal", "parent", "sibling", "index"],
            "newlines-between": "always",
          },
        ],
        "no-unused-vars": "off",
        "unused-imports/no-unused-imports": "error",
        "unused-imports/no-unused-vars": [
          "warn",
          { vars: "all", varsIgnorePattern: "^_", args: "after-used", argsIgnorePattern: "^_" },
        ],
        // Disable expensive import rules
        "import/namespace": "off",
        "import/default": "off",
        "import/no-named-as-default-member": "off",
        "import/no-named-as-default": "off",
      },
    },
    // Separate config for test files with relaxed rules
    {
      files: ["**/__tests__/**/*.ts", "**/__tests_*/**/*.ts", "**/*.spec.ts", "**/*.test.ts"],
      rules: {
        "@typescript-eslint/naming-convention": "off",
        "import/order": "off",
        "@typescript-eslint/no-explicit-any": "off",
      },
    },
  ],
};
