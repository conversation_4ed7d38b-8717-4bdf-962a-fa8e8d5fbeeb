# Compiled JavaScript files
lib/

# TypeScript v1 declaration files
typings/

# Node.js dependency directory
node_modules/

# db 
db-export/

# emulator log
*.log

# Used to return the /info endpoint, this file is generated on build so we can ignore it
src/static/info.json

src/__local_tests

log/

audit.jsonl

# Docker PostgreSQL data
.pgdata

# Firebase Emulator data
.data

# debug logs
firebase-debug.log
firestore-debug.log
pubsub-debug.log
ui-debug.log

# vscode
.vscode

# mac
.DS_Store

# Local setup
.emulator-data

.github/hooks

/coverage
# Sentry Config File
.sentryclirc

# Virtual environment
.venv/
.python-version

# IDE
.idea/*

# ESLint cache
.eslintcache

.env.local