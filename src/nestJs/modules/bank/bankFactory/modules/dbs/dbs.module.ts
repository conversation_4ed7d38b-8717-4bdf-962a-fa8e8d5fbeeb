import { Module } from "@nestjs/common";

import { AppDatabaseModule } from "@nest/modules/appDatabase/appDatabase.module";

import { PaymentTxRepository } from "../../../../database/repositories/paymentTx.repository";
import { TxRepository } from "../../../../database/repositories/tx.repository";

import { DbsService } from "./dbs.service";

@Module({
  imports: [DbsModule, AppDatabaseModule],
  providers: [DbsService, TxRepository, PaymentTxRepository],
  exports: [DbsService],
})
export class DbsModule {}
