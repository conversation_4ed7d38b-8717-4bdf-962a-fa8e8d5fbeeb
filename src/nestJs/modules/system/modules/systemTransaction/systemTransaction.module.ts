import { Modu<PERSON> } from "@nestjs/common";

import { TransactionModule } from "../../../transaction/transaction.module";

import { SystemTransactionController } from "./systemTransaction.controller";
import { SystemTransactionService } from "./systemTransaction.service";

@Module({
  providers: [SystemTransactionService],
  imports: [TransactionModule],
  controllers: [SystemTransactionController],
  exports: [SystemTransactionService],
})
export class SystemTransactionModule {}
