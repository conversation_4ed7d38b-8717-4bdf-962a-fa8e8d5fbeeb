import { Injectable } from "@nestjs/common";

import TxEvent from "../../../database/entities/txEvent.entity";
import { TransactionService } from "../../../transaction/transaction.service";

import { AddEventBodySystem } from "./dto/addEvent.dto";

@Injectable()
export class SystemTransactionService {
  constructor(private readonly transactionService: TransactionService) {}

  async addEvent(transactionId: string, addEventBody: AddEventBodySystem): Promise<TxEvent> {
    return this.transactionService.addEvent(transactionId, "SYSTEM", addEventBody);
  }
}
