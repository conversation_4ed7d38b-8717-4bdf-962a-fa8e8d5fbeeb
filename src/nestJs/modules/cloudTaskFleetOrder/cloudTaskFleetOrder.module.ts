import { Module } from "@nestjs/common";

import { SyncabApiModule } from "../syncabApi/syncabApi.module";
import { AppDatabaseModule } from "../appDatabase/appDatabase.module";
import { CloudTaskClientModule } from "../cloud-task-client/cloud-task-client.module";
import { FleetOrderRepository } from "../database/repositories/fleetOrder.repository";
import { FleetOrderTimelineRepository } from "../database/repositories/fleetOrderTimeline.repository";
import { FleetPartnerRepository } from "../database/repositories/fleetPartner.repository";
import { FleetQuoteRepository } from "../database/repositories/fleetQuote.repository";
import { MerchantRepository } from "../database/repositories/merchant.repository";
import { UserRepository } from "../database/repositories/user.repository";
import { HailingApiModule } from "../hailingApi/hailingApi.module";
import { DriverModule } from "../merchant/merchantDriver/merchantDriver.module";
import { PubSubModule } from "../pubsub/pubsub.module";
import { TransactionEventModule } from "../transaction/modules/transactionEvent.module";

import { UpdateFleetOrderDelegatee } from "./delegatees/UpdateFleetOrderDelegatee";
import { SyncabUpdateFleetOrderDelegatee } from "./delegatees/SyncabUpdateFleetOrderDelegatee";
import { CloudTaskFleetOrderController } from "./cloudTaskFleetOrder.controller";

@Module({
  imports: [
    SyncabApiModule,
    AppDatabaseModule,
    TransactionEventModule,
    CloudTaskClientModule,
    DriverModule,
    HailingApiModule,
    PubSubModule,
  ],
  providers: [
    UpdateFleetOrderDelegatee,
    SyncabUpdateFleetOrderDelegatee,
    FleetQuoteRepository,
    FleetOrderRepository,
    FleetPartnerRepository,
    MerchantRepository,
    FleetOrderTimelineRepository,
    UserRepository,
  ],
  controllers: [CloudTaskFleetOrderController],
  exports: [],
})
export class CloudTaskFleetOrderModule {}
