import FleetOrderEntity, { FleetOrderStatus } from "../database/entities/fleetOrder.entity";
import { BookingReceiptSnapshot } from "../me/modules/meFleetTaxi/meFleetTaxi.interface";

import { UpdateFleetOrderBody } from "./dto/fleetOrder.dto";

export interface IUpdateFleetOrderDelegatee {
  execute(body: UpdateFleetOrderBody, fleetOrder: FleetOrderEntity): Promise<IUpdateFleetOrderResponse>;
  getBookingReceiptSnapshot(fleetOrder: FleetOrderEntity): Promise<BookingReceiptSnapshot>;
}

export interface IUpdateFleetOrderResponse {
  status: FleetOrderStatus | null;
  thirdPartyStatus: string;
  snapshot: any;
  driverPhoneNumber: string | null;
  driverLicensePlate: string | null;
  driverName: string | null;
  driverLocation: {
    lat: number | null;
    lng: number | null;
  };
}

export interface HailUpdateResponse {
  id: string;
  createdAt: string;
  updatedAt: string;
  userId: string;
  userPhoneNumber: string;
  language: string;
  tripEstimation: {
    distanceMeters: number;
    durationSeconds: number;
  };
  itinerary: Array<{
    lat: number;
    lng: number;
    index: number;
    placeId: string;
    i18n: {
      en: {
        displayName: string;
        formattedAddress: string;
      };
      zhHK: {
        displayName: string;
        formattedAddress: string;
      };
    };
  }>;
  tripId: string;
  time: string;
  paymentDetails: {
    cardPrefix: string;
    cardSuffix: string;
    cardType: string;
  };
  minMaxFareCalculations: Array<{
    estimatedFareFee: number;
    estimatedTunnelFee: number;
    fleetBookingFee: number;
    additionalBookingFee: number;
    dashBookingFee: number;
    subTotal: number;
    dashTransactionFee: number;
    total: number;
    discount: number;
    discountedTotal: number;
    transactionFees: {
      dashFeeConstant: number;
      dashFeeRate: number;
    };
  }>;
  preferenceMatrix: Record<string, string[]>;
  filters: {
    isAssistant: boolean;
    isPetFriendly: boolean;
  };
  type: string;
  status: string;
  matchedDriver?: {
    driverId: string;
    name: string;
    nameTc: string;
    phoneNumber: string;
    licensePlate: string;
    vehicleMake: string;
    vehicleModel: string;
    vehicleClass: string;
    operatingArea: string;
    isDashMeter: boolean;
    fareCalculation: {
      estimatedFareFee: number;
      estimatedTunnelFee: number;
      fleetBookingFee: number;
      additionalBookingFee: number;
      dashBookingFee: number;
      subTotal: number;
      dashTransactionFee: number;
      total: number;
      discount: number;
      discountedTotal: number;
      transactionFees: {
        dashFeeConstant: number;
        dashFeeRate: number;
      };
    };
    feeSettings: {
      additionalBookingFeeRule: string | null;
      fleetBookingFeeRule: string | null;
    };
    heartBeatOnAccepted: any;
  };
  charges: any;
  prioritizeFavoriteDrivers: boolean;
  doubleTunnelFee: boolean;
  numFavoriteDriversOnline: number;
  operatingAreas: any[];
  applicableDiscounts: any;
  driverCountByRadius: Record<string, number>;
}
