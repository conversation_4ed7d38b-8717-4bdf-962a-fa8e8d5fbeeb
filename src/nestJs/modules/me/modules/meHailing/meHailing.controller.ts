import { <PERSON>, Req, <PERSON>, Body, <PERSON>, Param } from "@nestjs/common";
import { ApiBearerAuth, ApiTags, ApiOperation, ApiResponse, ApiBadRequestResponse } from "@nestjs/swagger";
import { Request } from "express";

import { errorBuilder } from "../../../utils/utils/error.utils";
import { apiTags, buildErrorHtml } from "../../../utils/utils/swagger.utils";
import { JoiValidationPipe } from "../../../validation/validationPipe.service";

import { BoostOrderBody, BoostOrderResponse, boostOrderBodySchema } from "./dto/boostOrder.dto";
import {
  HailingCreateOrderBody,
  hailingCreateOrderBodySchema,
  MeHailingCreateOrderResponse,
} from "./dto/meHailing.dto";
import { MeHailingService } from "./meHailing.service";

@ApiBearerAuth()
@Controller()
@ApiTags(...apiTags.me)
export class MeHailingController {
  constructor(private readonly meHailingService: MeHailingService) {}

  /**
   * Creates an order.
   * @param body HailingOrderBody
   * @param req Request
   */
  @Post("order")
  @ApiOperation({
    summary: "Creates an order",
  })
  @ApiResponse({ status: 201, description: "Creates an order" })
  @ApiBadRequestResponse({
    description: buildErrorHtml([
      errorBuilder.user.missing(),
      errorBuilder.validation.failed("[Validation description]", {
        errorsString: "[Error description]",
      }),
      errorBuilder.location.reverseGeocode.invalidParams(),
      errorBuilder.location.reverseGeocode.dependencyFailed(
        {
          candidates: [],
          error_message: "Error while parsing 'fields' parameter: Unsupported field name 'fdslgj'. ",
          status: "INVALID_REQUEST",
        },
        "Error while parsing 'fields' parameter: Unsupported field name 'fdslgj'. ",
      ),
    ]),
  })
  async createOrder(
    @Body(new JoiValidationPipe(hailingCreateOrderBodySchema)) body: HailingCreateOrderBody,
    @Req() req: Request,
  ): Promise<MeHailingCreateOrderResponse> {
    const user = req.user;
    if (!user || !req.headers.authorization) {
      throw errorBuilder.user.missing();
    }

    return this.meHailingService.createOrder(body, user, req.headers.authorization);
  }

  /**
   * Boost an order.
   * @param orderId Order ID
   * @param body BoostOrderBody
   * @param req Request
   */
  @Patch("order/:orderId/boost")
  @ApiOperation({
    summary: "Boost an order",
  })
  @ApiResponse({ status: 200, description: "Order boosted successfully", type: BoostOrderResponse })
  @ApiBadRequestResponse({
    description: buildErrorHtml([
      errorBuilder.user.missing(),
      errorBuilder.validation.failed("[Validation description]", {
        errorsString: "[Error description]",
      }),
      errorBuilder.hailing.invalidBoostAmount("Invalid boost amount provided"),
      errorBuilder.hailing.boostLimitExceeded(100, 500),
      errorBuilder.hailing.hailNotUpdatable("COMPLETED", "hail-123"),
    ]),
  })
  async boostOrder(
    @Param("orderId") orderId: string,
    @Body(new JoiValidationPipe(boostOrderBodySchema)) body: BoostOrderBody,
    @Req() req: Request,
  ): Promise<BoostOrderResponse> {
    const user = req.user;
    if (!user || !req.headers.authorization) {
      throw errorBuilder.user.missing();
    }

    return this.meHailingService.boostOrder(orderId, body, user, req.headers.authorization);
  }
}
