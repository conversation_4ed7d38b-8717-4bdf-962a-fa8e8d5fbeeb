import { Module } from "@nestjs/common";

import { LocationModule } from "../../../location/location.module";

import { MeLocationController } from "./meLocation.controller";
import { MeLocationService } from "./meLocation.service";

@Module({
  providers: [MeLocationController, MeLocationService],
  imports: [LocationModule],
  controllers: [MeLocationController],
  exports: [MeLocationController, MeLocationService],
})
export class MeLocationModule {}
