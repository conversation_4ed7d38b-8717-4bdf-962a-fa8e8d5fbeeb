import { Controller, Req, Query, Get, Post, Body } from "@nestjs/common";
import { ApiBearerAuth, ApiTags, ApiOperation, ApiResponse, ApiBadRequestResponse } from "@nestjs/swagger";
import { Request } from "express";

import {
  ComputeRoutesResponse,
  LocationLanguage,
  LocationPlaceAutocompleteResponse,
  LocationPlaceDetailsResponse,
  LocationReverseGeocodeResponse,
  LocationReverseGeocodeV2Response,
} from "../../../location/dto/location.dto";
import { errorBuilder } from "../../../utils/utils/error.utils";
import { apiTags, buildErrorHtml } from "../../../utils/utils/swagger.utils";
import { JoiValidationPipe } from "../../../validation/validationPipe.service";

import { MeLocationService } from "./meLocation.service";
import {
  LocationReverseGeocodeQuery,
  locationReverseGeocodeQuerySchema,
  LocationPlaceAutocompleteQuery,
  locationPlaceAutocompleteQuerySchema,
  LocationPlaceDetailsQuery,
  locationPlaceDetailsQuerySchema,
  LocationComputeRoutesQuery,
  locationComputeRoutesQuerySchema,
  locationV2PlaceDetailsDtoSchema,
  LocationV2PlaceDetailsDto,
  locationReverseGeocodeV2QuerySchema,
  LocationReverseGeocodeV2Query,
} from "./dto/meLocation.dto";

@ApiBearerAuth()
@Controller()
@ApiTags(...apiTags.me)
export class MeLocationController {
  constructor(private readonly meLocationService: MeLocationService) {}

  /**
   * Query place autocomplete.
   * @param query LocationPlaceAutocompleteQuery
   * @param req Request
   */
  @Get("place-autocomplete")
  @ApiOperation({
    summary: "Query place autocomplete",
  })
  @ApiResponse({ status: 201, description: "Query place autocomplete" })
  @ApiBadRequestResponse({
    description: buildErrorHtml([
      errorBuilder.user.missing(),
      errorBuilder.validation.failed("[Validation description]", {
        errorsString: "[Error description]",
      }),
      errorBuilder.location.placeAutocomplete.dependencyFailed(
        {
          candidates: [],
          error_message: "Error while parsing 'fields' parameter: Unsupported field name 'fdslgj'. ",
          status: "INVALID_REQUEST",
        },
        "Error while parsing 'fields' parameter: Unsupported field name 'fdslgj'. ",
      ),
    ]),
  })
  async placeAutocomplete(
    @Query(new JoiValidationPipe(locationPlaceAutocompleteQuerySchema)) query: LocationPlaceAutocompleteQuery,
    @Req() req: Request,
  ): Promise<LocationPlaceAutocompleteResponse> {
    const user = req.user;
    if (!user) {
      throw errorBuilder.user.missing();
    }
    return this.meLocationService.placeAutocomplete(query);
  }

  /**
   * Query place details.
   * @param query LocationPlaceDetailsQuery
   * @param req Request
   */
  @Get("place-details")
  @ApiOperation({
    summary: "Query place details",
  })
  @ApiResponse({ status: 201, description: "Query place details" })
  @ApiBadRequestResponse({
    description: buildErrorHtml([
      errorBuilder.user.missing(),
      errorBuilder.validation.failed("[Validation description]", {
        errorsString: "[Error description]",
      }),
      errorBuilder.location.placeDetails.dependencyFailed(
        [
          {
            fieldViolations: [
              {
                field: "id,displayName,someUnknownField",
                description:
                  "Error expanding 'fields' parameter. Cannot find matching fields for path 'someUnknownField'.",
              },
            ],
          },
        ],
        "Error expanding 'fields' parameter. Cannot find matching fields for path 'someUnknownField'.",
      ),
    ]),
  })
  async placeDetails(
    @Query(new JoiValidationPipe(locationPlaceDetailsQuerySchema)) query: LocationPlaceDetailsQuery,
    @Req() req: Request,
  ): Promise<LocationPlaceDetailsResponse> {
    const user = req.user;
    if (!user) {
      throw errorBuilder.user.missing();
    }
    return this.meLocationService.placeDetails(query);
  }

  /**
   * Compute routes.
   * @param query LocationReverseGeocodeQuery
   * @param req Request
   */
  @Get("compute-routes")
  @ApiOperation({
    summary: "Compute Routes",
  })
  @ApiResponse({ status: 201, description: "Compute Routes" })
  @ApiBadRequestResponse({
    description: buildErrorHtml([
      errorBuilder.user.missing(),
      errorBuilder.validation.failed("[Validation description]", {
        errorsString: "[Error description]",
      }),
      errorBuilder.location.computeRoutes.dependencyFailed(
        {
          candidates: [],
          error_message: "Error while parsing 'fields' parameter: Unsupported field name 'fdslgj'. ",
          status: "INVALID_REQUEST",
        },
        "Error while parsing 'fields' parameter: Unsupported field name 'fdslgj'. ",
      ),
      errorBuilder.location.computeRoutes.noRoutesFound({
        originPlaceId: "ChIJuwh9w1YABDQRaRbZ1DgateI",
        destinationPlaceId: "ChIJj6ZHpA4DBDQRD99EOoiAGwo",
        language: LocationLanguage.EN,
      }),
    ]),
  })
  async computeRoutes(
    @Query(new JoiValidationPipe(locationComputeRoutesQuerySchema)) query: LocationComputeRoutesQuery,
    @Req() req: Request,
  ): Promise<ComputeRoutesResponse> {
    const user = req.user;
    if (!user) {
      throw errorBuilder.user.missing();
    }
    return this.meLocationService.computeRoutes(query);
  }

  /**
   * Reverse Geocode.
   * @param query LocationReverseGeocodeQuery
   * @param req Request
   */
  @Get("reverse-geocode")
  @ApiOperation({
    summary: "Reverse Geocode",
  })
  @ApiResponse({ status: 201, description: "Reverse Geocode" })
  @ApiBadRequestResponse({
    description: buildErrorHtml([
      errorBuilder.user.missing(),
      errorBuilder.validation.failed("[Validation description]", {
        errorsString: "[Error description]",
      }),
      errorBuilder.location.reverseGeocode.invalidParams(),
      errorBuilder.location.reverseGeocode.dependencyFailed(
        {
          candidates: [],
          error_message: "Error while parsing 'fields' parameter: Unsupported field name 'fdslgj'. ",
          status: "INVALID_REQUEST",
        },
        "Error while parsing 'fields' parameter: Unsupported field name 'fdslgj'. ",
      ),
    ]),
  })
  async reverseGeocode(
    @Query(new JoiValidationPipe(locationReverseGeocodeQuerySchema)) query: LocationReverseGeocodeQuery,
    @Req() req: Request,
  ): Promise<LocationReverseGeocodeResponse> {
    const user = req.user;
    if (!user) {
      throw errorBuilder.user.missing();
    }
    return this.meLocationService.reverseGeocode(query);
  }
  /**
   * Reverse Geocode.
   * @param query LocationReverseGeocodeQuery
   * @param req Request
   */
  @Get("v2/reverse-geocode")
  @ApiOperation({
    summary: "Reverse Geocode V2",
  })
  @ApiResponse({ status: 201, description: "Reverse Geocode V2" })
  @ApiBadRequestResponse({
    description: buildErrorHtml([
      errorBuilder.user.missing(),
      errorBuilder.validation.failed("[Validation description]", {
        errorsString: "[Error description]",
      }),
      errorBuilder.location.reverseGeocode.invalidParams(),
      errorBuilder.location.reverseGeocode.dependencyFailed(
        {
          candidates: [],
          error_message: "Error while parsing 'fields' parameter: Unsupported field name 'fdslgj'. ",
          status: "INVALID_REQUEST",
        },
        "Error while parsing 'fields' parameter: Unsupported field name 'fdslgj'. ",
      ),
    ]),
  })
  async reverseGeocodeV2(
    @Query(new JoiValidationPipe(locationReverseGeocodeV2QuerySchema)) query: LocationReverseGeocodeV2Query,
    @Req() req: Request,
  ): Promise<LocationReverseGeocodeV2Response> {
    const user = req.user;
    if (!user) {
      throw errorBuilder.user.missing();
    }
    return this.meLocationService.reverseGeocodeV2(query);
  }

  /**
   * Query place details (v2)
   * @param query LocationV2PlaceDetailsQuery
   * @param req Request
   */
  @Post("v2/place-details")
  @ApiOperation({
    summary: "Query place details",
  })
  @ApiBadRequestResponse({
    description: buildErrorHtml([
      errorBuilder.user.missing(),
      errorBuilder.validation.failed("[Validation description]", {
        errorsString: "[Error description]",
      }),
      errorBuilder.location.placeDetails.dependencyFailed(
        [
          {
            fieldViolations: [
              {
                field: "id,displayName,someUnknownField",
                description:
                  "Error expanding 'fields' parameter. Cannot find matching fields for path 'someUnknownField'.",
              },
            ],
          },
        ],
        "Error expanding 'fields' parameter. Cannot find matching fields for path 'someUnknownField'.",
      ),
    ]),
  })
  async placeDetailsV2(
    @Body(new JoiValidationPipe(locationV2PlaceDetailsDtoSchema)) dto: LocationV2PlaceDetailsDto,
    @Req() req: Request,
  ): Promise<LocationPlaceDetailsResponse[]> {
    const user = req.user;
    if (!user) {
      throw errorBuilder.user.missing();
    }

    return this.meLocationService.placeDetailsV2(dto);
  }
}
