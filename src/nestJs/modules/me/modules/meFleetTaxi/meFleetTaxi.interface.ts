import FleetOrderEntity from "@nest/modules/database/entities/fleetOrder.entity";
import FleetQuoteEntity from "@nest/modules/database/entities/fleetQuote.entity";
import Tx from "@nest/modules/database/entities/tx.entity";
import { LocationPlaceDetailsResponse } from "@nest/modules/location/dto/location.dto";
import { AppUser } from "@nest/modules/user/dto/user.dto";

import { HailingCreateOrderBody } from "../meHailing/dto/meHailing.dto";

import { RideHailingMatrixDto } from "./dto/meFleetQuote.dto";

export enum PartnerKey {
  SYNCAB = "SYNCAB",
}

export interface IQuoteFleetOrderDelegatee {
  partnerKey: PartnerKey;
  getQuote(data: RideHailingMatrixDto, locations: LocationPlaceDetailsResponse[]): Promise<IQuoteResponse>;
}

export interface ICreateFleetOrderDelegatee {
  partnerKey: PartnerKey;
  execute(
    data: HailingCreateOrderBody,
    tx: Tx,
    fleetQuote: FleetQuoteEntity,
    appUser: AppUser,
  ): Promise<ICreateFleetOrderResponse>;
}

export interface ICreateFleetOrderResponse {
  thirdPartyOrderId: string;
  thirdPartyStatus: string;
  snapshot: any;
}

interface IQuoteResponse {
  vehicleClassOptions: VehicleClassOption[];
  thirdPartyQuoteId: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  snapshot: any;
}

export interface VehicleClassOption {
  fleetVehicleType: string;
  fleetPartnerKey: PartnerKey;
  vehicleClass: string;
  vehicleClassName: string;
  vehicleClassNameTc: string;
  vehicleClassDescription: string;
  vehicleClassDescriptionTc: string;
  seatsCount: number;
  luggageCount: number;
  isIncludeWheelChair: boolean;
  baseFare: number;
  fleetVehicleId: string;
  vehicleIconUrl: string;
  fleetIconUrl: string;
}

export interface QuoteFleetOrderResponse {
  vehicleClassOptions: VehicleClassOption[];
  fleetQuote: FleetQuoteEntity | null;
}

export interface ICancelFleetOrderDelegatee {
  execute(fleetOrder: FleetOrderEntity): Promise<void>;
  getBookingReceiptSnapshot(fleetOrder: FleetOrderEntity): Promise<BookingReceiptSnapshot>;
}

export interface BookingReceiptSnapshot {
  bookingFee: number;
  cancellationFee: number;
  snapshot: any;
}
