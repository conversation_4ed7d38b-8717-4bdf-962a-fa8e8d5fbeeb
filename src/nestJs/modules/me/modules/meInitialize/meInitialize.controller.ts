import { Controller, Put, Body, Req } from "@nestjs/common";
import {
  ApiBearerAuth,
  ApiTags,
  ApiOperation,
  ApiNotFoundResponse,
  ApiUnauthorizedResponse,
  ApiBadRequestResponse,
  ApiResponse,
} from "@nestjs/swagger";
import { Request } from "express";
import { FirebaseAuthError } from "firebase-admin/auth";
import { InsertResult } from "typeorm";

import { errorBuilder } from "../../../utils/utils/error.utils";
import { apiTags, buildErrorHtml } from "../../../utils/utils/swagger.utils";
import { JoiValidationPipe } from "../../../validation/validationPipe.service";

import { MeInitializeService } from "./meInitialize.service";
import { InitializeUserBody, initializeUserBodySchema } from "./dto/initialize.dto";

@ApiBearerAuth()
@Controller()
@ApiTags(...apiTags.me)
export class MeInitializeController {
  constructor(private readonly meInitializeService: MeInitializeService) {}

  /**
   * Initialize user data, like check and update public key, update notification token, update campaigins, etc.
   * @param body InitializeUserBody
   * @param req Request
   */
  @Put("")
  @ApiOperation({
    summary:
      "Initialize user data, like check and update public key, update notification token, update campaigins, etc.",
  })
  @ApiNotFoundResponse({
    description: buildErrorHtml([errorBuilder.user.notFoundInSql("IkCO7YHOJ3c073P8RihpRZxX3yC2")]),
  })
  @ApiUnauthorizedResponse({
    description: buildErrorHtml([
      errorBuilder.auth.tokenNotSet(),
      errorBuilder.auth.tokenInvalidRole(),
      errorBuilder.auth.firebaseTokenExpired({ code: "XXX", message: "firebase token expired" } as FirebaseAuthError),
      errorBuilder.auth.firebaseAuthError({ code: "XXX", message: "firebase auth error" } as FirebaseAuthError),
      errorBuilder.auth.unknown({ code: "XXX", message: "unknown" } as FirebaseAuthError),
    ]),
  })
  @ApiBadRequestResponse({
    description: buildErrorHtml([
      errorBuilder.validation.failed("Validation failed: 'xxx' is required", {
        errorsString: "",
      }),
    ]),
  })
  @ApiResponse({ status: 201, description: "Initialize user data" })
  async upsertNotificationToken(
    @Body(new JoiValidationPipe(initializeUserBodySchema)) body: InitializeUserBody,
    @Req() req: Request,
  ): Promise<InsertResult> {
    const user = req.user;
    if (!user) {
      throw errorBuilder.user.missing();
    }
    return this.meInitializeService.initialize(body, user.user_id);
  }
}
