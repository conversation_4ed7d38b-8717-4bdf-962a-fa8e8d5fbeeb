import { <PERSON>du<PERSON> } from "@nestjs/common";

import { UserRepository } from "../../../database/repositories/user.repository";
import { UserNotificationTokenRepository } from "../../../database/repositories/userNotificationToken.repository";
import { UserModule } from "../../../user/user.module";
import { MeNotificationTokenModule } from "../meNotificationToken/meNotificationToken.module";

import { MeInitializeService } from "./meInitialize.service";
import { MeInitializeController } from "./meInitialize.controller";

@Module({
  providers: [MeInitializeController, MeInitializeService, UserRepository, UserNotificationTokenRepository],
  imports: [MeNotificationTokenModule, UserModule],
  controllers: [MeInitializeController],
  exports: [MeInitializeController, MeInitializeService],
})
export class MeInitializeModule {}
