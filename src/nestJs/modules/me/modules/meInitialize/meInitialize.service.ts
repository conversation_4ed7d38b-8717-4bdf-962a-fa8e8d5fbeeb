import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { InsertResult } from "typeorm";

import { UserRepository } from "../../../database/repositories/user.repository";
import { UserService } from "../../../user/user.service";
import { MeNotificationTokenService } from "../meNotificationToken/meNotificationToken.service";

import { InitializeUserBody } from "./dto/initialize.dto";

@Injectable()
export class MeInitializeService {
  constructor(
    private readonly meNotificationTokenService: MeNotificationTokenService,
    private readonly userService: UserService,
    @InjectRepository(UserRepository)
    private userRepository: UserRepository,
  ) {}

  async initialize(content: InitializeUserBody, appDatabaseId: string): Promise<InsertResult> {
    const user = await this.userRepository.findAppUserById(appDatabaseId);
    await this.userService.checkAndUpdateKey(appDatabaseId, user);
    // await this.userService.checkAndCopyCampaigns(appDatabaseId); // commented out because it is not used right now, and it will be used in the future and will be changed
    if (content.token) {
      const insertResult = await this.meNotificationTokenService.upsertNotificationToken(
        { token: content.token },
        user,
      );
      return insertResult;
    }
    return { generatedMaps: [], raw: [], identifiers: [] };
  }
}
