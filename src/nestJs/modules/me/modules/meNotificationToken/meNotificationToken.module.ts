import { <PERSON>du<PERSON> } from "@nestjs/common";

import { UserRepository } from "../../../database/repositories/user.repository";
import { UserNotificationTokenRepository } from "../../../database/repositories/userNotificationToken.repository";

import { MeNotificationTokenService } from "./meNotificationToken.service";

@Module({
  providers: [MeNotificationTokenService, UserNotificationTokenRepository, UserRepository],
  imports: [],
  exports: [MeNotificationTokenService],
})
export class MeNotificationTokenModule {}
