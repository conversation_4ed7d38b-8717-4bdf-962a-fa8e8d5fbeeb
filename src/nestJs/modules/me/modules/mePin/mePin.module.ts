import { MiddlewareConsumer, <PERSON><PERSON><PERSON>, RequestMethod } from "@nestjs/common";

import { PubSubModule } from "@nest/modules/pubsub/pubsub.module";

import { ProfileAuditRepository } from "../../../database/repositories/profileAudit.repository";
import { UserRepository } from "../../../database/repositories/user.repository";
import { VerifyPinMiddleware } from "../../middlewares/verifyPin.middleware";

import { MePinService } from "./mePin.service";
import { MePinController } from "./mePin.controller";

@Module({
  providers: [MePinService, MePinController, UserRepository, ProfileAuditRepository],
  imports: [PubSubModule],
  controllers: [MePinController],
  exports: [MePinService, MePinController],
})
export class MePinModule {
  /**
   * Configure middleware
   * @param consumer MiddlewareConsumer
   */
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(VerifyPinMiddleware)
      .forRoutes({ path: "me/pin/verify", method: RequestMethod.GET }, { path: "me/pin", method: RequestMethod.PATCH });
  }
}
