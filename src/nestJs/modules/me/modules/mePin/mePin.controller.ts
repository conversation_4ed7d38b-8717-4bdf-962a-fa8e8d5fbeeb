import { Body, Controller, Get, Patch, Post, Query, Req } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { Request } from "express";

import { errorBuilder } from "../../../utils/utils/error.utils";
import { apiTags } from "../../../utils/utils/swagger.utils";
import { JoiValidationPipe } from "../../../validation/validationPipe.service";

import { MePinService } from "./mePin.service";
import { CreatePinBody, UpdateUserPinBody, VerifyPinQuery, pinSchema, updateUserPinSchema } from "./dto/pin.dto";

@ApiBearerAuth()
@Controller()
@ApiTags(...apiTags.me)
export class MePinController {
  constructor(private readonly mePinService: MePinService) {}

  /**
   * Create a user pin
   * @param body CreatePinBody
   * @param req Request
   */
  @Post("")
  @ApiOperation({
    summary: "create a user pin",
  })
  @ApiResponse({ status: 201, description: "created a user pin" })
  async createUserPin(@Body(new JoiValidationPipe(pinSchema)) body: CreatePinBody, @Req() req: Request): Promise<void> {
    const user = req.user;
    if (!user) {
      throw errorBuilder.user.missing();
    }
    return this.mePinService.createUserPin(body.pin, user.user_id);
  }

  /**
   * Verify user pin
   * @param query UpdatePinQuery
   * @param req Request
   */
  @Get("/verify")
  @ApiOperation({
    summary: "verify user pin",
  })
  @ApiResponse({ status: 200, description: "verified user pin" })
  async verifyUserPin(
    @Query(new JoiValidationPipe(pinSchema)) query: VerifyPinQuery,
    @Req() req: Request,
  ): Promise<{ verified: boolean }> {
    const user = req.user;
    if (!user) {
      throw errorBuilder.user.missing();
    }
    const verifyResult = req.verifyPinResult;
    if (!verifyResult) {
      throw errorBuilder.user.pin.verifyFailedWithPinUnkown();
    }
    return { verified: verifyResult.verified };
  }

  /**
   * Update user pin
   * @param body UpdateUserPinBody
   * @param req Request
   */
  @Patch("")
  @ApiOperation({
    summary: "update user pin",
  })
  @ApiResponse({ status: 200, description: "updated user pin" })
  async updateUserPin(
    @Body(new JoiValidationPipe(updateUserPinSchema)) body: UpdateUserPinBody,
    @Req() req: Request,
  ): Promise<void> {
    const user = req.user;
    if (!user) {
      throw errorBuilder.user.missing();
    }
    const verifyResult = req.verifyPinResult;
    if (!verifyResult) {
      throw errorBuilder.user.pin.verifyFailedWithPinUnkown();
    }
    if (!verifyResult.verified) {
      throw errorBuilder.user.pin.updatePinFailedWithWrongOldPin(user.user_id);
    }
    return this.mePinService.updateUserPin(verifyResult.user, body.newPin);
  }
}
