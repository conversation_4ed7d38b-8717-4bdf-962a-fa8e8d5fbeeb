import { Body, Controller, Post, Req } from "@nestjs/common";
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiConflictResponse,
  ApiNotFoundResponse,
  ApiNotImplementedResponse,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiUnauthorizedResponse,
  ApiUnprocessableEntityResponse,
} from "@nestjs/swagger";
import { Request } from "express";
import { FirebaseAuthError } from "firebase-admin/auth";

import { PaymentGatewayResponse } from "../../../payment/dto/paymentGatewayResponses.model";
import { QrCodePairResponse, qrCodeLinkTransform } from "../../../qrCode/dto/qrCode.dto";
import { errorBuilder } from "../../../utils/utils/error.utils";
import { apiTags, buildErrorHtml } from "../../../utils/utils/swagger.utils";
import { JoiValidationPipe } from "../../../validation/validationPipe.service";

import { MeQrCodeService } from "./meQrCode.service";
import { QrCodePairBody, qrCodePairBodySchema } from "./dto/pair.dto";

@ApiBearerAuth()
@Controller()
@ApiTags(...apiTags.me)
export class MeQrCodeController {
  constructor(private qrService: MeQrCodeService) {}

  /**
   * pair a qrcode
   * @param body QrCodePairBody
   * @param req Request
   * @returns Promise<QrCodePairResponse>
   */
  @Post("pair")
  @ApiOperation({ summary: "Pair QR code" })
  @ApiUnauthorizedResponse({
    description: buildErrorHtml([
      errorBuilder.auth.tokenNotSet(),
      errorBuilder.auth.tokenInvalidRole(),
      errorBuilder.auth.firebaseTokenExpired({ code: "XXX", message: "firebase token expired" } as FirebaseAuthError),
      errorBuilder.auth.firebaseAuthError({ code: "XXX", message: "firebase auth error" } as FirebaseAuthError),
      errorBuilder.auth.unknown({ code: "XXX", message: "unknown" } as FirebaseAuthError),
    ]),
  })
  @ApiBadRequestResponse({
    description: buildErrorHtml([
      errorBuilder.validation.failed("Validation failed: 'xxx' is required", {
        errorsString: "",
      }),
      errorBuilder.global.requiredParam("baseUrl"),
      errorBuilder.global.requiredParam("version"),
      errorBuilder.global.requiredParam("type"),
    ]),
  })
  @ApiNotImplementedResponse({
    description: buildErrorHtml([
      errorBuilder.qrcode.factoryNotImplemented("google.com"),
      errorBuilder.qrcode.factoryNotImplemented("[version] [type]"),
    ]),
  })
  @ApiNotFoundResponse({
    description: buildErrorHtml([
      errorBuilder.qrcode.notFound("50uISmOqcO0aeXi1AeUO"),
      errorBuilder.transaction.notFound("5a21d2ba-25ad-44ed-82b6-e941ba628356"),
      errorBuilder.payment.instrument.notFound("5a21d2ba-25ad-44ed-82b6-e941ba628356"),
    ]),
  })
  @ApiConflictResponse({
    description: buildErrorHtml([
      errorBuilder.transaction.alreadyLocked("5a21d2ba-25ad-44ed-82b6-e941ba628356"),
      errorBuilder.qrcode.alreadyPaired("50uISmOqcO0aeXi1AeUO"),
    ]),
  })
  @ApiUnprocessableEntityResponse({
    description: buildErrorHtml([
      errorBuilder.transaction.trip.missingLicencePlate("DASH02T"),
      errorBuilder.payment.authFailed({
        some: "data",
      } as unknown as PaymentGatewayResponse),
    ]),
  })
  @ApiResponse({ status: 200, description: "Pair trip through QR code" })
  async pair(
    @Body(new JoiValidationPipe(qrCodePairBodySchema)) body: QrCodePairBody,
    @Req() req: Request,
  ): Promise<QrCodePairResponse> {
    const user = req.user;
    if (!user) {
      throw errorBuilder.user.missing();
    }

    return this.qrService.pair(qrCodeLinkTransform(body.link), user.user_id, body.paymentInstrumentId, body.language);
  }
}
