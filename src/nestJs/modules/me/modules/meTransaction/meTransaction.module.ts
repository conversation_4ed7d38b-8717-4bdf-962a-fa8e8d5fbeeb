import { <PERSON><PERSON><PERSON> } from "@nestjs/common";

import { AppDatabaseModule } from "../../../appDatabase/appDatabase.module";
import { PaymentTxRepository } from "../../../database/repositories/paymentTx.repository";
import { TxRepository } from "../../../database/repositories/tx.repository";
import { UserRepository } from "../../../database/repositories/user.repository";
import { TransactionModule } from "../../../transaction/transaction.module";

import { MeTransactionService } from "./meTransaction.service";
import { MeTransactionController } from "./meTransaction.controller";

@Module({
  providers: [MeTransactionService, TxRepository, PaymentTxRepository, UserRepository],
  imports: [TransactionModule, AppDatabaseModule],
  controllers: [MeTransactionController],
  exports: [MeTransactionService],
})
export class MeTransactionModule {}
