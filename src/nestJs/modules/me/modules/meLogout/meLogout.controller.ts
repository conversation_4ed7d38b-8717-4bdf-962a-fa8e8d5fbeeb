import { Body, Controller, Post, Req } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { Request } from "express";

import { apiTags } from "@nest/modules/utils/utils/swagger.utils";

import { errorBuilder } from "../../../utils/utils/error.utils";
import { JoiValidationPipe } from "../../../validation/validationPipe.service";

import { LogoutBody, logoutBodySchema } from "./dto/logout.dto";
import { MeLogoutService } from "./meLogout.service";

@ApiBearerAuth()
@Controller()
@ApiTags(...apiTags.me)
export class MeLogoutController {
  constructor(private readonly meLogoutService: MeLogoutService) {}

  @Post("")
  @ApiOperation({ summary: "Logout user and remove specific notification token" })
  async logout(@Body(new JoiValidationPipe(logoutBodySchema)) body: LogoutBody, @Req() req: Request): Promise<void> {
    const user = req.user;
    if (!user) {
      throw errorBuilder.user.missing();
    }
    await this.meLogoutService.logout(user.user_id, body.token);
  }
}
