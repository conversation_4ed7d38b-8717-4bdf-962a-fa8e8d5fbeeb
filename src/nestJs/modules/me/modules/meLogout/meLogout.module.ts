import { Modu<PERSON> } from "@nestjs/common";

import { UserModule } from "@nest/modules/user/user.module";

import { UserRepository } from "../../../database/repositories/user.repository";
import { UserNotificationTokenRepository } from "../../../database/repositories/userNotificationToken.repository";
import { MeNotificationTokenModule } from "../meNotificationToken/meNotificationToken.module";

import { MeLogoutService } from "./meLogout.service";
import { MeLogoutController } from "./meLogout.controller";

@Module({
  providers: [MeLogoutController, MeLogoutService, UserRepository, UserNotificationTokenRepository],
  imports: [MeNotificationTokenModule, UserModule],
  controllers: [MeLogoutController],
  exports: [MeLogoutController, MeLogoutService],
})
export class MeLogoutModule {}
