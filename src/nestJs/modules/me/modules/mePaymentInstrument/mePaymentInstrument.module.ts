import { Module } from "@nestjs/common";

import { PaymentInstrumentModule } from "../../../payment/modules/paymentInstrument/paymentInstrument.module";

import { MePaymentInstrumentController } from "./mePaymentInstrument.controller";
import { MePaymentInstrumentService } from "./mePaymentInstrument.service";

/**
 * MePaymentInstrument module
 */
@Module({
  providers: [MePaymentInstrumentService, MePaymentInstrumentController],
  imports: [PaymentInstrumentModule],
  controllers: [MePaymentInstrumentController],
  exports: [MePaymentInstrumentService, MePaymentInstrumentController],
})
export class MePaymentInstrumentModule {}
