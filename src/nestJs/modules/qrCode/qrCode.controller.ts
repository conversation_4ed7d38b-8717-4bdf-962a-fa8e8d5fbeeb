import { <PERSON>, <PERSON>, Body, Req } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { Request } from "express";

import { apiTags } from "../utils/utils/swagger.utils";
import { JoiValidationPipe } from "../validation/validationPipe.service";

import QrCode from "./dto/qrCode.dto";
import { qrCodeRequestBodyDto, qrRequestBodySchema } from "./dto/qrCodeRequest.dto";
import { QrCodeService } from "./qrCode.service";

@ApiBearerAuth()
@Controller("qrcode")
@ApiTags(...apiTags.meter)
export class QrCodeController {
  constructor(private readonly qrCodeService: QrCodeService) {}

  @Post()
  @ApiOperation({
    summary: "Request to create a QR code",
  })
  @ApiResponse({ status: 201, description: "created qr", type: QrCode })
  async createQrCode(
    @Body(new JoiValidationPipe(qrRequestBodySchema)) qrCodeRequest: qrCodeRequestBodyDto,
    @Req() req: Request,
  ) {
    // call service to create QR document and return
    const userId = req.user?.uid || "";
    return await this.qrCodeService.create(userId, qrCodeRequest.licensePlate, qrCodeRequest.type, qrCodeRequest.txId);
  }
}
