import { Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";

import { AppDatabaseService } from "../appDatabase/appDatabase.service";
import { QrCodeDocument, TripMetadata } from "../appDatabase/documents/qrCode.document";

import QrCode from "./dto/qrCode.dto";
import { QrType } from "./dto/qrType";

/**
 * Qr Generation Service
 */
@Injectable()
export class QrCodeService {
  constructor(private readonly appDatabaseService: AppDatabaseService, private readonly configService: ConfigService) {}

  /**
   * Create a dash QR for whatever type by adding an entry to firestore
   */
  async create(userId: string, meterId: string, type: QrType, transactionId: string) {
    const tripMetadata: TripMetadata = {
      licensePlate: meterId,
    };

    const qrDocument: QrCodeDocument = {
      transactionId: transactionId,
      metadata: tripMetadata,
      type: type,
      baseUrl: this.configService.getOrThrow("QR_CODE_URL_DASH"),
      createdAt: new Date(),
      createdBy: userId,
      expireAt: new Date(Date.now() + 1000 * 60 * 60),
    };
    const qrDocumentWithId = await this.appDatabaseService.qrRepository().createAndGet(qrDocument);
    return QrCode.fromQrDocument(qrDocumentWithId);
  }
}
