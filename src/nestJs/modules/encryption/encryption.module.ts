import { Modu<PERSON> } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";

import { SecretsModule } from "../secrets/secrets.module";

import { KmsEncryptionService } from "./kmsEncryption.service";
import { RSAEncryptionService } from "./rsaEncryption.service";

@Module({
  imports: [SecretsModule, ConfigModule],
  providers: [RSAEncryptionService, KmsEncryptionService],
  exports: [RSAEncryptionService, KmsEncryptionService],
})
export class EncryptionModule {}
