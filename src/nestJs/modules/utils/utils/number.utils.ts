import Jo<PERSON> from "joi";

import { ValidationService } from "../../validation/validation.service";

import { errorBuilder } from "./error.utils";

// uses toFixed to round up to the nearest 10 cents (1 decimal place)
export const roundUpOneDecimal = (value: number): number => {
  if (typeof value !== "number" || isNaN(value)) {
    throw errorBuilder.global.invalidParam(`value: '${value}', type: '${typeof value}'`);
  }

  if (value === 0) {
    return 0; // if zero, return zero
  }

  return Math.ceil(Math.round(value * 100) / 10) / 10; // Math.ceil can handle negative values correctly
};

// basically rounding down to the nearest 10 cents (trims the decimal to 1 digit)
export const roundDownOneDecimal = (value: number): number => {
  if (typeof value !== "number" || isNaN(value)) {
    throw errorBuilder.global.invalidParam(`value: '${value}', type: '${typeof value}'`);
  }

  if (value === 0) {
    return 0; // if zero, return zero
  }

  return Math.floor(Math.round(value * 100) / 10) / 10; // Math.floor can handle negative values correctly
};

export const roundAwayFromZero = (value: number): number => {
  // This ensures that positive values are rounded up and negative values are rounded down
  return value >= 0 ? roundUpOneDecimal(value) : roundDownOneDecimal(value);
};

export const roundOneDecimal = (value: number): number => {
  if (typeof value !== "number") {
    throw errorBuilder.global.invalidParam(`value: '${value}', type: '${typeof value}'`);
  }
  return Math.round(value * 10) / 10;
};

type dashFeeConstantProps = {
  tripTotal: number;
  dashTips?: number;
  dashFeeRate: number;
  dashFeeConstant: number;
  additionalBookingFee?: number;
  dashBookingFee?: number;
  fleetBookingFee?: number;
  boostAmount?: number;
};
/**
 * calculateDashTransactionFee
 * @param dashFeeConstantProps
 * @returns dashFee
 * Note that for the sake of Octopus, we need to round up the dashFee to the nearest 10 cents, but
 * Discount is not rounded up to the nearest 10 cents. therefore the transaction amount will still not
 * be rounded up to the nearest 10 cents. before this is fixed, we cannot launch discount with % rate.
 *
 * 2025-07-02:
 * Accepts an optional `roundingFunc` parameter to allow for different rounding strategies.
 * By default, it uses `roundUpOneDecimal` to ensure the fee is always rounded up to the nearest 10 cents.
 * This is useful if you expect the result to be negative, then we can use `roundDownOneDecimal` to ensure the fee is always rounded away from zero.
 * Or use `roundAwayFromZero` to ensure the fee is always rounded away from zero, which can be important in financial calculations.
 *
 *
 * @param props - An object containing the properties needed to calculate the Dash transaction fee.
 * @param roundingFunc - A function to round the calculated fee. Defaults to `roundUpOneDecimal`.
 * @returns The calculated Dash transaction fee, rounded according to the provided rounding function.
 * @throws Will throw an error if the input properties are not valid numbers.
 */
export const calculateDashTransactionFee = (
  props: dashFeeConstantProps,
  roundingFunc: (value: number) => number = roundUpOneDecimal,
): number => {
  const [
    {
      tripTotal,
      dashTips,
      dashFeeRate,
      dashFeeConstant,
      additionalBookingFee,
      dashBookingFee,
      fleetBookingFee,
      boostAmount,
    },
  ] = ValidationService.validate<[dashFeeConstantProps]>([
    {
      schema: Joi.object({
        tripTotal: Joi.number().allow(null).default(0),
        dashTips: Joi.number().allow(null).default(0),
        dashFeeRate: Joi.number().allow(null).default(0),
        dashFeeConstant: Joi.number().allow(null).default(0),
        additionalBookingFee: Joi.number().allow(null).default(0),
        dashBookingFee: Joi.number().allow(null).default(0),
        fleetBookingFee: Joi.number().allow(null).default(0),
        boostAmount: Joi.number().allow(null).default(0),
      }),
      value: props,
    },
  ]);

  return roundingFunc(
    dashFeeConstant +
      dashFeeRate *
        (tripTotal +
          (dashTips ?? 0) +
          (additionalBookingFee ?? 0) +
          (dashBookingFee ?? 0) +
          (fleetBookingFee ?? 0) +
          (boostAmount ?? 0)),
  );
};

const numberUtils = {
  roundUpOneDecimal,
  roundDownOneDecimal,
  roundAwayFromZero,
  roundOneDecimal,
  calculateDashFee: calculateDashTransactionFee,
};

export default numberUtils;
