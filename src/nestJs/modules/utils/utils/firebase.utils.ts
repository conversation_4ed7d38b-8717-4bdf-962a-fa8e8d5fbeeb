import { FirebaseAuthError } from "firebase-admin/auth";

import LoggerServiceAdapter from "../logger/logger.service";

import casesUtils from "./case/case.utils";
import dateUtils from "./date.utils";

const firebaseUtils = {
  withConverter: (logger?: LoggerServiceAdapter) => ({
    toFirestore: (data: any) => {
      const converted = dateUtils.objectDatesToTimestamps(casesUtils.snakeKeys(data));
      return converted;
    },
    fromFirestore: (snapshot: FirebaseFirestore.QueryDocumentSnapshot) => {
      const converted: Record<string, any> = dateUtils.objectTimestampsToDates(
        casesUtils.camelizeKeys(snapshot.data()),
      );
      return converted;
    },
  }),
  isFirebaseTokenExpiredError(error: any): error is FirebaseAuthError {
    return error.code.startsWith("auth/id-token-expired");
  },
  isFirebaseAuthError(error: any): error is FirebaseAuthError {
    return error.code.startsWith("auth");
  },
};

export default firebaseUtils;
