import { randomUUID } from "crypto";

import { Body, Controller, Get, HttpStatus, Inject, Param, Patch, Post, Req, Res } from "@nestjs/common";
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiConflictResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiUnauthorizedResponse,
} from "@nestjs/swagger";
import { diff } from "deep-diff";
import { AuthenticatedRequest, Request, Response } from "express";
import { getAuth } from "firebase-admin/auth";
import { CloudEvent } from "firebase-functions/v2";
import { Change, DocumentSnapshot, FirestoreEvent } from "firebase-functions/v2/firestore";
import { MessagePublishedData } from "firebase-functions/v2/pubsub";

import { DriverRoleData } from "@nest/modules/admin/adminAuthUser/dto/updateUserRole.dto";
import { MeterDocument } from "@nest/modules/appDatabase/documents/meter.document";
import { WebhookEventType } from "@nest/modules/pubsub/dto/PublishMessageForWebhookProcessing.dto";

import { Role } from "../../auth/types";
import Merchant from "../../database/entities/merchant.entity";
import { ChannelTypes } from "../../message/dto/channelType.dto";
import { TemplateTypesText } from "../../message/dto/templateType.dto";
import { MessageTeamsService } from "../../messageTeams/messageTeams.service";
import { PublishMessageForCopyTripToDriverProcessingParams } from "../../pubsub/dto/publishMessageForDriverTripProcessing.dto";
import { PubSubService } from "../../pubsub/pubsub.service";
import { CorrelationContext } from "../../utils/context/decorators/correlation-context.decorator";
import LoggerServiceAdapter from "../../utils/logger/logger.service";
import { errorBuilder, listAuthErrors } from "../../utils/utils/error.utils";
import { apiTags, buildErrorHtml } from "../../utils/utils/swagger.utils";
import { isGreaterOrEqualThan } from "../../utils/utils/version.utils";
import { LanguageOption } from "../../validation/dto/language.dto";
import { JoiValidationPipe } from "../../validation/validationPipe.service";
import { ApplicationStatus } from "../dto/merchant.dto";

import { DriverService } from "./merchantDriver.service";
import { SessionPairResponseDto } from "./dto/sessionPairResponse.dto";
import {
  MerchantReferralLinkBody,
  merchantReferralLinkBodySchema,
  MerchantReferralRetrieveResponse,
} from "./dto/referral.dto";
import { NonDashMeterPairDto, nonDashMeterPairDtoSchema } from "./dto/nonDashMeterPair.dto";
import {
  CreateMeterBody,
  createMeterBodySchema,
  meterIdSchema,
  GetMeterDataResponse,
  GetMeterhasOngoingTripResponse,
} from "./dto/meterVehicle.dto";
import {
  UpsertDriverNotificationTokenBody,
  upsertDriverNotificationTokenBodySchema,
} from "./dto/driverNotificationToken.dto";
import { UpdateTripEndDto, updateTripEndSchema } from "./dto/updateTripEnd.dto";

@ApiBearerAuth()
@Controller()
@ApiTags(...apiTags.driver)
export class MerchantDriverController {
  constructor(
    private readonly driverService: DriverService,
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
    @Inject(MessageTeamsService) private messageTeamsService: MessageTeamsService,
    private pubsubService: PubSubService,
  ) {}

  @CorrelationContext()
  async updateMerchant(
    event: FirestoreEvent<
      Change<DocumentSnapshot> | undefined,
      {
        driverId: string;
      }
    >,
  ) {
    const { driverId } = event.params;
    const dataBefore = event.data?.before?.data();
    const data = event.data?.after.data();
    const difference = diff(dataBefore, data);
    this.logger.info(`nest trigger: drivers/${driverId} updated`, { driverId, difference });

    if (!data) {
      throw errorBuilder.merchant.driver.updatedNoData();
    }
    this.logger.info(`current drivers/${driverId} data`, { data });
    const merchant = Merchant.fromJson({ ...data, phone_number: driverId });

    if (data.application_status === ApplicationStatus.UNDER_REVIEW && (!dataBefore || !dataBefore.application_status)) {
      await this.messageTeamsService.sendDriverApplicationEventsToTeams(
        merchant.phoneNumber,
        "New Driver Submitted",
        `Driver with phone number: ${driverId} submitted just now, please review the application.`,
      );
    }
    if (
      data.application_status === ApplicationStatus.APPROVED &&
      dataBefore &&
      dataBefore.application_status !== ApplicationStatus.APPROVED
    ) {
      await this.pubsubService.publishMessageForMessageProcessingParams({
        metadata: {
          schemeVersion: "5.0",
          createdAt: new Date(),
        },
        recipient: {
          phone: merchant.phoneNumber.replace("+", ""),
        },
        channel: ChannelTypes.WHATSAPP,
        tranId: "empty",
        language: LanguageOption.ZHHK,
        params: {},
        messageId: randomUUID().toString(),
        template: TemplateTypesText.DRIVER_APPLICATION_APPROVED,
      });
      // set custom user claims to the driver
      // 1. look up user by phone number
      // 2. get auth user
      this.logger.debug(`Setting custom user claims for driver ${merchant.phoneNumber} upon approval`);
      const user = await getAuth().getUserByPhoneNumber(merchant.phoneNumber);
      await getAuth().setCustomUserClaims(user.uid, { role: Role.DRIVER });
    }

    if (dataBefore && !dataBefore.session && data.session) {
      this.logger.info(`nest trigger: drivers/${driverId} session start`, { driverId, session: data.session });
      const webhookPubsubMessages = await this.driverService.processMeterSessionStartEndEvent(
        data,
        WebhookEventType.METER_SESSION_START,
      );
      await Promise.all(
        webhookPubsubMessages.map((webhookPubsubMessage) => {
          this.logger.info("Publishing webhook message for meter session start", {
            webhookId: webhookPubsubMessage.webhookId,
            message: webhookPubsubMessage.message,
          });
          return this.pubsubService.publishMessageForWebhookMessageProcessing(webhookPubsubMessage);
        }),
      );
    }
    if (dataBefore && dataBefore.session && !data.session) {
      this.logger.info(`nest trigger: drivers/${driverId} session end`, { driverId, session: dataBefore.session });
      const webhookPubsubMessages = await this.driverService.processMeterSessionStartEndEvent(
        dataBefore,
        WebhookEventType.METER_SESSION_END,
      );
      await Promise.all(
        webhookPubsubMessages.map((webhookPubsubMessage) => {
          this.logger.info("Publishing webhook message for meter session end", {
            webhookId: webhookPubsubMessage.webhookId,
            message: webhookPubsubMessage.message,
          });
          return this.pubsubService.publishMessageForWebhookMessageProcessing(webhookPubsubMessage);
        }),
      );
    }
    return this.driverService.upsertMerchants([merchant]);
  }

  @CorrelationContext()
  async copyToDriverCollectionInFireStore(
    event: CloudEvent<MessagePublishedData<PublishMessageForCopyTripToDriverProcessingParams>>,
  ) {
    const data = event.data.message.json;
    this.logger.info("nest trigger: copyTripToDriverProcessing", data);

    return this.driverService.copyToDriverCollectionInFireStore(data);
  }

  @CorrelationContext()
  async driverTripChanged(
    event: FirestoreEvent<
      Change<DocumentSnapshot> | undefined,
      {
        tripId: string;
        driverId: string;
        sessionId: string;
      }
    >,
  ) {
    const { tripId, driverId, sessionId } = event.params;

    const dataAfter = event.data?.after.data();
    if (!dataAfter) {
      throw errorBuilder.merchant.driver.trip.tripChangedNoData(driverId, sessionId, tripId);
    }

    const { meter_software_version, is_dash_meter } = dataAfter;
    if (
      (!meter_software_version ||
        !isGreaterOrEqualThan(meter_software_version, MeterDocument.newFlowMeterSoftwareVersion)) &&
      is_dash_meter
    ) {
      this.logger.info(`nest trigger: drivers/${driverId}/sessions/${sessionId}/trips/${tripId} skipped`, {
        tx: tripId,
        data: { meter_software_version },
      });
      return;
    }
    this.logger.info(`nest trigger: drivers/${driverId}/trips/${tripId} changed`, {
      tx: tripId,
      data: { meter_software_version },
    });
    this.logger.info(`current drivers/${driverId}/trips/${tripId} data`, { dataAfter });
    const dataBefore = event.data?.before.data();

    return this.driverService.driverTripChanged(driverId, sessionId, dataAfter, dataBefore);
  }

  /**
   * Get meter settings data
   * @param meterId string
   */
  @Get("meter/:meterId/settings")
  @ApiOperation({
    summary: "Get meter data",
  })
  @ApiResponse({ status: 201, description: "Get meter data" })
  @ApiNotFoundResponse({ description: buildErrorHtml([errorBuilder.meter.notFound("XXX")]) })
  @ApiUnauthorizedResponse({ description: buildErrorHtml(listAuthErrors()) })
  @ApiInternalServerErrorResponse({
    description: buildErrorHtml([errorBuilder.auth.unknown(new Error("Some unknown error"))]),
  })
  async getMeterSettingsData(
    @Param("meterId", new JoiValidationPipe(meterIdSchema)) meterId: string,
    @Req() req: Request,
  ): Promise<GetMeterDataResponse> {
    if (!req.user) {
      throw errorBuilder.user.missing();
    }
    return this.driverService.getMeterSettingsData(meterId);
  }

  /**
   * Create meter
   * @param meterId string
   */
  @Post("meter/:meterId")
  @ApiOperation({
    summary: "Create meter",
  })
  @ApiResponse({ status: 201, description: "Create meter" })
  @ApiConflictResponse({ description: buildErrorHtml([errorBuilder.meter.alreadyExist("XXX")]) })
  @ApiUnauthorizedResponse({ description: buildErrorHtml(listAuthErrors()) })
  @ApiInternalServerErrorResponse({
    description: buildErrorHtml([errorBuilder.auth.unknown(new Error("Some unknown error"))]),
  })
  async createMeter(
    @Param("meterId", new JoiValidationPipe(meterIdSchema)) meterId: string,
    @Body(new JoiValidationPipe(createMeterBodySchema)) body: CreateMeterBody,
    @Req() req: Request,
  ): Promise<GetMeterDataResponse> {
    if (!req.user) {
      throw errorBuilder.user.missing();
    }
    return this.driverService.createMeter(meterId, req.user, body);
  }

  /**
   * Get meter current trip
   * @param meterId string
   */
  @Get("meter/:meterId/hasOngoingTrip")
  @ApiOperation({
    summary: "Get meter current trip",
  })
  @ApiNotFoundResponse({ description: buildErrorHtml([errorBuilder.meter.notFound("XXX")]) })
  @ApiResponse({ status: 201, description: "Get meter current trip" })
  @ApiUnauthorizedResponse({ description: buildErrorHtml(listAuthErrors()) })
  async meterhasOngoingTrip(
    @Param("meterId", new JoiValidationPipe(meterIdSchema)) meterId: string,
    @Req() req: Request,
  ): Promise<GetMeterhasOngoingTripResponse> {
    if (!req.user) {
      throw errorBuilder.user.missing();
    }
    return this.driverService.meterHasOngoingTrip(meterId);
  }

  @Patch("meter/:meterId/trips/:tripId/end")
  @ApiOperation({ summary: "Complete trip for non-DASH meter" })
  @ApiUnauthorizedResponse({
    description: buildErrorHtml([errorBuilder.meter.tripUpdateUnauthorized("meter id", "trip id")]),
  })
  @ApiBadRequestResponse({
    description: buildErrorHtml([
      errorBuilder.meter.tripNotFound("meter id", "trip id"),
      errorBuilder.meter.tripAlreadyEnded("meter id", "trip id"),
    ]),
  })
  @ApiResponse({ status: 200, description: "Complete trip for non-DASH meter" })
  @ApiUnauthorizedResponse({ description: buildErrorHtml(listAuthErrors()) })
  async updateMeterTripEnd(
    @Param("meterId") meterId: string,
    @Param("tripId") tripId: string,
    @Req() req: AuthenticatedRequest<DriverRoleData>,
    @Res() res: Response,
    @Body(new JoiValidationPipe(updateTripEndSchema)) updateTripEndDto: UpdateTripEndDto,
  ): Promise<void> {
    this.logger.info(`Trip end for non-DASH meter ${meterId} trip ${tripId}`, updateTripEndDto);
    const driverId = req.user.phone_number;

    if (!driverId || !req.headers.authorization) {
      throw errorBuilder.user.missing();
    }

    await this.driverService.updateMeterTripEnd({
      driverId,
      meterId,
      tripId,
      updateTripEndDto,
      token: req.headers.authorization,
    });

    res.sendStatus(200);
  }

  /**
   * Update driver push notification token
   * @param content: UpsertDriverNotificationTokenBody
   */
  @Post("notification-token")
  @ApiOperation({
    summary: "Update driver push notification token",
  })
  async upsertNotificationToken(
    @Body(new JoiValidationPipe(upsertDriverNotificationTokenBodySchema)) body: UpsertDriverNotificationTokenBody,
    @Req() req: Request,
  ) {
    if (!req.user) {
      throw errorBuilder.user.missing();
    }
    return this.driverService.upsertNotificationToken(body, req.user);
  }

  @Post("pair")
  @ApiOperation({ summary: "Pair driver with non-DASH meter session" })
  async pairToNonDashMeter(
    @Body(new JoiValidationPipe(nonDashMeterPairDtoSchema)) nonDashMeterPairDto: NonDashMeterPairDto,
    @Req() req: AuthenticatedRequest<DriverRoleData>,
  ): Promise<SessionPairResponseDto> {
    const driverId = req.user.phone_number;
    this.logger.info(`Pairing driver ${driverId} with non-DASH meter session`, nonDashMeterPairDto);
    return this.driverService.pairToNonDashMeter(driverId, nonDashMeterPairDto);
  }

  @Post("unpair")
  @ApiOperation({ summary: "Un-pair driver with non-DASH meter session" })
  async unpairFromNonDashMeter(@Req() req: AuthenticatedRequest<DriverRoleData>): Promise<SessionPairResponseDto> {
    const driverId = req.user.phone_number;
    this.logger.info(`Unpairing driver ${driverId} from non-DASH meter session`);
    return this.driverService.unpairFromNonDashMeter(driverId);
  }

  /**
   * Request a referral code
   */
  @Get("referral")
  @ApiOperation({
    summary: "Fetch a driver referral code",
  })
  @ApiResponse({ status: 200, description: "Fetch a driver referral code" })
  async retrieveReferralCode(
    @Req() req: AuthenticatedRequest<DriverRoleData>,
  ): Promise<MerchantReferralRetrieveResponse> {
    const driverId = req.user.phone_number;
    this.logger.debug(`Requesting referral code for merchant driver ${driverId}`);
    return { code: await this.driverService.getOrCreateReferralCode(driverId) };
  }

  @Post("referral/link")
  @ApiOperation({
    summary: "Link to another driver's referral code",
  })
  @ApiResponse({ status: HttpStatus.OK, description: "Link to another driver's referral code" })
  async linkReferralCode(
    @Body(new JoiValidationPipe(merchantReferralLinkBodySchema)) body: MerchantReferralLinkBody,
    @Req() req: AuthenticatedRequest<DriverRoleData>,
    @Res() res: Response,
  ) {
    const driverId = req.user.phone_number;
    this.logger.debug(`Linking referral code for merchant driver ${driverId}`);
    await this.driverService.linkReferralCode(driverId, body.referralCode);
    res.sendStatus(HttpStatus.OK);
  }
}
