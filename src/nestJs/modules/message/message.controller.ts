import { Controller, Inject } from "@nestjs/common";
import { CloudEvent } from "firebase-functions/v2";
import { MessagePublishedData } from "firebase-functions/v2/pubsub";

import Message from "../database/entities/message.entity";
import { PublishMessageForMessageProcessingParams } from "../pubsub/dto/publishMessageForMessageProcessing.dto";
import { CorrelationContext } from "../utils/context/decorators/correlation-context.decorator";
import LoggerServiceAdapter from "../utils/logger/logger.service";

import { MessageService } from "./message.service";

/**
 * Message controller
 */
@Controller()
export class MessageController {
  constructor(
    private messageService: MessageService,
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
  ) {}

  /**
   * Process sending message
   * @param context functions.EventContext
   * @param message functions.pubsub.Message
   * @returns Message
   */
  @CorrelationContext()
  async processMessage(
    event: CloudEvent<MessagePublishedData<PublishMessageForMessageProcessingParams>>,
  ): Promise<Message> {
    const data = event.data.message.json;
    this.logger.info("nest trigger: messageProcessing", data);
    return this.messageService.processMessage(data);
  }
}
