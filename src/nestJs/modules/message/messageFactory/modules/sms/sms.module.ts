import { Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";

import { MessageRepository } from "../../../../database/repositories/message.repository";
import { UserRepository } from "../../../../database/repositories/user.repository";

import { SmsServices } from "./sms.service";

/**
 * SMS module
 */
@Module({
  imports: [ConfigModule],
  providers: [SmsServices, MessageRepository, UserRepository],
  exports: [SmsServices],
})
export class SmsModule {}
