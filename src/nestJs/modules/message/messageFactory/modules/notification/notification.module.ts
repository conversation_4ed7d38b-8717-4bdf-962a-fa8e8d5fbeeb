import { Module } from "@nestjs/common";

import { MessageRepository } from "../../../../database/repositories/message.repository";
import { UserRepository } from "../../../../database/repositories/user.repository";

import { NotificationService } from "./notification.service";

/**
 * Notification module
 */
@Module({
  imports: [],
  providers: [NotificationService, MessageRepository, UserRepository],
  exports: [NotificationService],
})
export class NotificationModule {}
