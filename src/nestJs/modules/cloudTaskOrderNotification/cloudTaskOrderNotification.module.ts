import { <PERSON>du<PERSON> } from "@nestjs/common";

import { CloudTaskClientModule } from "../cloud-task-client/cloud-task-client.module";
import { TeamOrderNotificationModule } from "../teamOrderNotification/teamOrderNotification.module";

import { CloudTaskOrderNotificationController } from "./cloudTaskOrderNotification.controller";
import { CloudTaskOrderNotificationService } from "./cloudTaskOrderNotification.service";

@Module({
  imports: [TeamOrderNotificationModule, CloudTaskClientModule],
  providers: [CloudTaskOrderNotificationService],
  controllers: [CloudTaskOrderNotificationController],
  exports: [CloudTaskOrderNotificationService],
})
export class CloudTaskOrderNotificationModule {}
