import * as fs from "fs";
import * as os from "os";

import { BigQuery } from "@google-cloud/bigquery";
import { Bucket, Storage } from "@google-cloud/storage";
import { Inject, Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { parseExpression } from "cron-parser";
import { DecodedIdToken } from "firebase-admin/auth";
import Jo<PERSON> from "joi";
import moment from "moment";
import { Between } from "typeorm";

import ReportJob from "../database/entities/reportJob.entity";
import { ReportJobRepository } from "../database/repositories/reportJob.repository";
import { EmailService } from "../email/email.service";
import { MessageTeamsService } from "../messageTeams/messageTeams.service";
import { PublishMessageForReportJobProcessingParams } from "../pubsub/dto/publishMessageForReportJobProcessingParams.dto";
import { PubSubService } from "../pubsub/pubsub.service";
import { SecretsService } from "../secrets/secrets.service";
import { Secret } from "../secrets/types";
import LoggerServiceAdapter from "../utils/logger/logger.service";
import { errorBuilder } from "../utils/utils/error.utils";
import { ZipUtils } from "../utils/utils/zip/zip.utils";

import { ReportType, SystemAlertMetadata, SystemAlertType } from "./dto/reportJob.dto";
import { CreateReportJobBodyDto } from "./dto/createReportJobRequest.dto";
@Injectable()
export class ReportJobService {
  public bucketsNames: Record<ReportType, string>;
  /**32 is the max number of files that can be combined in one batch, from gcs doc */
  maxFileCountForBatch = 32;
  cronParserOption = {
    tz: "Asia/Hong_Kong",
  };
  reportFileDefaultExpireDays = 7;
  constructor(
    private readonly reportJobRepository: ReportJobRepository,
    private pubsubService: PubSubService,
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
    private readonly configService: ConfigService,
    private readonly emailService: EmailService,
    private readonly secretsService: SecretsService,
    private readonly messageTeamsService: MessageTeamsService,
  ) {
    this.bucketsNames = {
      [ReportType.HEARTBEAT]: `${this.configService.getOrThrow<string>("GCLOUD_PROJECT")}-report/${
        ReportType.HEARTBEAT
      }`,
      [ReportType.TRIP]: `${this.configService.getOrThrow<string>("GCLOUD_PROJECT")}-report/${ReportType.TRIP}`,
      [ReportType.SYSTEM_ALERT]: `${this.configService.getOrThrow<string>("GCLOUD_PROJECT")}-report/${
        ReportType.SYSTEM_ALERT
      }`,
    };
  }

  async createReportJob(reportJobDto: CreateReportJobBodyDto, user: DecodedIdToken): Promise<ReportJob> {
    const reportJob = new ReportJob();
    this.logger.info("report job cron", { cron: reportJobDto.cron });
    const nextCron = parseExpression(reportJobDto.cron, this.cronParserOption);
    reportJob.name = reportJobDto.name;
    reportJob.nextRunAt = nextCron.next().toDate();
    reportJob.cron = reportJobDto.cron;
    reportJob.reportType = reportJobDto.reportType;
    reportJob.destination = reportJobDto.destination;
    reportJob.queryMetadata = reportJobDto.queryMetadata;
    reportJob.queryString = reportJobDto.queryString;
    reportJob.merchantMetadata = reportJobDto.merchantMetadata;
    reportJob.createdBy = user.email;
    return this.reportJobRepository.save(reportJob);
  }

  async getMatchedReportJobs(now: Date): Promise<ReportJob[]> {
    const startOfMinute = new Date(
      now.getFullYear(),
      now.getMonth(),
      now.getDate(),
      now.getHours(),
      now.getMinutes(),
      0,
      0,
    );
    const endOfMinute = new Date(
      now.getFullYear(),
      now.getMonth(),
      now.getDate(),
      now.getHours(),
      now.getMinutes(),
      59,
      999,
    );
    this.logger.info("startOfMinute", { startOfMinute });
    this.logger.info("endOfMinute", { endOfMinute });
    return this.reportJobRepository.find({
      where: { nextRunAt: Between(startOfMinute, endOfMinute) },
    });
  }

  async sendReportJobAndUpdate(reportJobs: ReportJob[]): Promise<void> {
    for (const reportJob of reportJobs) {
      const data = {
        name: reportJob.name,
        lastRunAt: reportJob.lastRunAt,
        nowRunAt: reportJob.nextRunAt,
        reportType: reportJob.reportType,
        destination: reportJob.destination,
        queryMetadata: reportJob.queryMetadata,
        queryString: reportJob.queryString,
        merchantMetadata: reportJob.merchantMetadata,
      };
      this.logger.info("data sent to pubsub", { data });
      await this.pubsubService.publishMessageForReportJobProcessing(data);
      //update nextRunAt and lastRunAt
      reportJob.lastRunAt = reportJob.nextRunAt;
      const nextCron = parseExpression(reportJob.cron, this.cronParserOption);
      reportJob.nextRunAt = nextCron.next().toDate();
      this.logger.info("reportJob updated", { reportJob });

      // check if nextRunAt is same as lastRunAt, if yes, get next cron again
      const maxAttempts = 10;
      let attempts = 1;
      while (reportJob.nextRunAt.getTime() <= reportJob.lastRunAt.getTime() && attempts < maxAttempts) {
        this.logger.info(
          `nextCron.next() get same time as current lastRunAt, attempt ${attempts}, need get next cron again`,
        );
        reportJob.nextRunAt = nextCron.next().toDate();
        attempts++;
      }
      if (reportJob.nextRunAt.getTime() <= reportJob.lastRunAt.getTime()) {
        this.logger.error("Failed to get a valid next run time after maximum attempts", {
          cron: reportJob.cron,
          lastRunAt: reportJob.lastRunAt,
          nextRunAt: reportJob.nextRunAt,
        });
      }

      await this.reportJobRepository.save(reportJob);
    }
  }

  formatDateTimeForBigquery(date: Date): string {
    return `TIMESTAMP('${moment(date).tz("Asia/Hong_Kong").format("YYYY-MM-DD")} 00:00:00 Asia/Hong_Kong')`;
  }

  formatDateForBucket(date: Date): string {
    return moment(date).tz("Asia/Hong_Kong").format("YYYY-MM-DD");
  }

  async generateReportFileAndExport(
    data: PublishMessageForReportJobProcessingParams,
  ): Promise<{ reportDatePath: string; yesterday: Date }> {
    this.logger.info("reportJobProcessing ", data);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);
    const todayString = this.formatDateTimeForBigquery(today);
    const yesterdayString = this.formatDateTimeForBigquery(yesterday);
    if (!data.queryString) throw errorBuilder.report.queryMetadataNotImplementedYet(data.name);
    const replacedQueryString = data.queryString
      .replace("[END_TIMESTAMP]", todayString)
      .replace("[START_TIMESTAMP]", yesterdayString);
    this.logger.info("query string", { replacedQueryString });

    try {
      //query from bigquery and save to temp table
      const bigquery = new BigQuery();
      const tempDataset = "temp_dataset";
      const tempTable = data.reportType + data.name.replace(/ /g, "_") + Date.now();

      const destinationTable = bigquery.dataset(tempDataset).table(tempTable);
      const options = {
        query: replacedQueryString,
        destination: destinationTable,
        createDisposition: "CREATE_IF_NEEDED",
        writeDisposition: "WRITE_TRUNCATE",
      };
      const [job] = await bigquery.createQueryJob(options);
      this.logger.info("Query job starting", { jobId: job.id, reportName: data.name });
      await job.promise();
      this.logger.info("Query job completed", { jobId: job.id, reportName: data.name });

      //extract from temp table to gcs
      const storage = new Storage();
      const reportDatePath = `${this.bucketsNames[data.reportType]}/${data.name.replace(
        / /g,
        "-",
      )}/${this.formatDateForBucket(yesterday)}`;
      const [extractJob] = await destinationTable.extract(
        storage.bucket(reportDatePath).file(`${data.reportType.toLowerCase()}-*.csv`),
        {
          format: "CSV",
        },
      );
      this.logger.info("Extract job completed", { jobId: extractJob.id, reportName: data.name });

      //delete temp table
      await destinationTable.delete();
      this.logger.info("Temp table deleted", {
        table: destinationTable.id,
        jobId: job.id,
        reportName: data.name,
      });

      return { reportDatePath, yesterday };
    } catch (err) {
      this.logger.error(err);
      throw err;
    }
  }

  async combineAndGetReportSignedUrl(
    reportFilePath: string,
    reportType: ReportType,
    reportName: string,
  ): Promise<{ signedUrl: string; destinationFilename: string }> {
    const storage = new Storage();
    const bucketName = `${this.configService.getOrThrow<string>("GCLOUD_PROJECT")}-report`;
    const prefix = reportFilePath.replace(bucketName + "/", "") + "/";
    const [allFilesInBucket] = await storage.bucket(bucketName).getFiles({ prefix: prefix, delimiter: "/" });
    const sources = allFilesInBucket.map((file) => file.name);
    if (sources.length === 0) {
      throw errorBuilder.report.noReportFileInCloudStorage(reportName);
    }
    const destinationFilename = `${prefix}${reportType.toLowerCase()}.csv`;
    const file = storage.bucket(bucketName).file(destinationFilename);
    try {
      const tempFilesWillDelete: string[] = [];
      this.logger.info("combine Objects starting...", { prefix, destinationFilename, reportName });
      await this.combineFilesInBatches(
        storage.bucket(bucketName),
        sources.filter((source) => source != destinationFilename),
        this.maxFileCountForBatch,
        destinationFilename,
        tempFilesWillDelete,
        0,
      );
      this.logger.info("combine Objects completed", { prefix, destinationFilename, reportName });
      const signedUrl = await file.getSignedUrl({
        action: "read",
        expires: Date.now() + 1000 * 60 * 60 * 24 * this.reportFileDefaultExpireDays,
      });
      this.logger.info("signedUrl is ", { signedUrl: signedUrl });

      // delete temp files
      if (tempFilesWillDelete.length > 0) {
        this.logger.info("delete temp Objects starting...", { prefix, destinationFilename, reportName });
        const deleteFilePromises = tempFilesWillDelete
          .filter((tempFile) => tempFile != destinationFilename)
          .map((tempFile) => {
            return storage.bucket(bucketName).file(tempFile).delete();
          });
        await Promise.all(deleteFilePromises);
        this.logger.info("delete temp Objects completed", { prefix, destinationFilename, reportName });
      }

      return { signedUrl: signedUrl[0], destinationFilename };
    } catch (error) {
      this.logger.error(`Error combining or getting file signedUrl : ${error}`);
      throw error;
    }
  }

  async combineFilesInBatches(
    bucket: Bucket,
    files: string[],
    batchSize: number,
    destinationFilename: string,
    needDeleteFiles: string[],
    batchIndex: number,
  ) {
    needDeleteFiles.push(...files);

    // if files.length > batchSize, combine files in batches, means there must one more round to combine
    if (files.length > batchSize) {
      const tempFilesThisRound: string[] = [];
      const batches = [];
      for (let i = 0; i < files.length; i += batchSize) {
        batches.push(files.slice(i, i + batchSize));
      }
      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        const tempDestinationFilename = `${destinationFilename.replace(".csv", "")}_temp_${batchIndex}_${i}.csv`;
        await bucket.combine(batch, tempDestinationFilename);
        tempFilesThisRound.push(tempDestinationFilename);
      }
      await this.combineFilesInBatches(
        bucket,
        tempFilesThisRound,
        batchSize,
        destinationFilename,
        needDeleteFiles,
        batchIndex + 1,
      );
    } else {
      // if files.length <= batchSize, combine all files to destinationFilename directly
      await bucket.combine(files, destinationFilename);
    }
  }

  async notifyReportUser(
    data: PublishMessageForReportJobProcessingParams,
    signedUrl: string,
    yesterday: Date,
    destinationFilename: string,
  ): Promise<void> {
    //check if destination is email/url, use Joi
    const isEmail = Joi.string().email().validate(data.destination);
    const isHttp = Joi.string().uri().validate(data.destination);
    switch (data.reportType) {
      case ReportType.HEARTBEAT:
        if (!isEmail.error) {
          const email = data.destination;
          await this.sendEmailToHeartbeatReportUser(email, data.reportType, signedUrl, yesterday);
        }
        break;
      case ReportType.TRIP:
        if (!isEmail.error) {
          const email = data.destination;
          await this.sendEmailToTripReportUser(email, data.reportType, yesterday, destinationFilename);
        }
        break;
      case ReportType.SYSTEM_ALERT:
        if (!isHttp.error) {
          const url = data.destination;
          await this.sendSystemAlertReport(yesterday, destinationFilename, url, data);
        }
        break;
    }
  }

  async sendEmailToHeartbeatReportUser(
    email: string,
    reportType: ReportType,
    signedUrl: string,
    yesterday: Date,
  ): Promise<void> {
    //send email
    this.logger.info("send email to ", { email });
    this.emailService.sendOpenLinkEmail({
      to: email,
      bcc: "<EMAIL>",
      dynamicTemplateData: {
        subject: `Your ${reportType} report for ${this.formatDateForBucket(yesterday)} is ready`,
        heading: `Your ${reportType} report for ${this.formatDateForBucket(yesterday)} is ready`,
        body: `Please download your report within ${this.reportFileDefaultExpireDays} days from the following link:`,
        linkUrl: signedUrl,
        linkText: signedUrl,
      },
    });
  }

  async sendEmailToTripReportUser(
    email: string,
    reportType: ReportType,
    yesterday: Date,
    destinationFilename: string,
  ): Promise<void> {
    const { tempCsvFilePath, tempZipFilePath } = await this.downloadFileFromGcsAndZipIt(destinationFilename, yesterday);
    //send email
    this.logger.info("send email to ", { email });
    const attachment = fs.readFileSync(tempZipFilePath).toString("base64");
    await this.emailService.sendNotificationEmail({
      to: email,
      bcc: "<EMAIL>",
      attachments: [
        {
          content: attachment,
          filename: `report-${this.formatDateForBucket(yesterday)}.zip`,
          type: "application/zip",
          disposition: "attachment",
        },
      ],
      dynamicTemplateData: {
        subject: `Your ${reportType} report for ${this.formatDateForBucket(yesterday)} is ready`,
        heading: `Your ${reportType} report for ${this.formatDateForBucket(yesterday)} is ready`,
        body: "Please find attached report. You may use the previously provided password to access the file.",
      },
    });

    //delete temp files
    if (fs.existsSync(tempCsvFilePath)) {
      fs.unlinkSync(tempCsvFilePath);
    }
    if (fs.existsSync(tempZipFilePath)) {
      fs.unlinkSync(tempZipFilePath);
    }
  }

  async downloadFileFromGcsAndZipIt(
    filePath: string,
    yesterday: Date,
  ): Promise<{ tempCsvFilePath: string; tempZipFilePath: string }> {
    //download file from gcs
    const bucketName = `${this.configService.getOrThrow<string>("GCLOUD_PROJECT")}-report`;
    const storage = new Storage();
    const bucket = storage.bucket(bucketName);
    const file = bucket.file(filePath);
    const tempDir = os.tmpdir();
    const tempCsvFilePath = `${tempDir}/report-${this.formatDateForBucket(yesterday)}-${btoa(filePath)}.csv`;
    const tempZipFilePath = `${tempDir}/report-${this.formatDateForBucket(yesterday)}-${btoa(filePath)}.zip`;
    const options = {
      destination: tempCsvFilePath,
    };
    await file.download(options);
    this.logger.info("File downloaded to", { csvFilePath: tempCsvFilePath });

    //zip file
    const encryptionPassword = await this.secretsService.getSecret(Secret.PAYOUT_MERCHANT_RECON_PASSWORD);
    await ZipUtils.zipFile(tempCsvFilePath, tempZipFilePath, { password: encryptionPassword });
    this.logger.info("File zipped to", { zipFilePath: tempZipFilePath });

    return { tempCsvFilePath, tempZipFilePath };
  }

  async sendSystemAlertReport(
    yesterday: Date,
    destinationFilename: string,
    webhookUrl: string,
    data: PublishMessageForReportJobProcessingParams,
  ): Promise<void> {
    //download file from bucket
    const { tempCsvFilePath, tempZipFilePath } = await this.downloadFileFromGcsAndZipIt(destinationFilename, yesterday);
    //read from csv file and check length
    const csvData = fs.readFileSync(tempCsvFilePath, "utf8");
    this.logger.info("csvData", { csvData });
    const rows = csvData.split("\n").filter(Boolean);
    const dataRows = rows.slice(1);
    const environment = this.configService.getOrThrow<string>("GCLOUD_PROJECT");
    const merchantMetadata = data.merchantMetadata as SystemAlertMetadata;
    switch (merchantMetadata.alertType) {
      case SystemAlertType.FARE:
        await Promise.all(
          dataRows.map(async (row) => {
            // the query is "SELECT id, metadata__billing__estimated_fare, metadata__billing__fare, metadata__trip_end, metadata__license_plate FROM ..."
            const [id, estimated_fare, actual_fare, trip_end, meter_id] = row.split(",");
            this.logger.info("row", { id, estimated_fare, actual_fare, trip_end, meter_id });
            await this.messageTeamsService.sendSystemAlertNotificationToTeams(
              [
                {
                  type: "TextBlock",
                  text: data.name,
                  id: "Title",
                  spacing: "Medium",
                  horizontalAlignment: "Center",
                  size: "ExtraLarge",
                  weight: "Bolder",
                  color: "Accent",
                },
                {
                  type: "TextBlock",
                  text: merchantMetadata.summary,
                  id: "summary",
                  wrap: true,
                  separator: true,
                },
                {
                  type: "TextBlock",
                  text: `Environment: ${environment}`,
                  id: "environment",
                },
                {
                  type: "TextBlock",
                  text: `Id: ${id}`,
                  id: "trip_id",
                },
                {
                  type: "TextBlock",
                  text: `Estimated Fare: ${estimated_fare}`,
                  id: "estimated_fare",
                },
                {
                  type: "TextBlock",
                  text: `Actual Fare: ${actual_fare}`,
                  id: "actual_fare",
                },
                {
                  type: "TextBlock",
                  text: `Meter Id: ${meter_id}`,
                  id: "meter_id",
                },
              ],
              webhookUrl,
              `https://console.firebase.google.com/u/0/project/${environment}/firestore/databases/-default-/data/~2Fmeters~2F${meter_id}~2Ftrips~2F${id}`,
              "View in Firestore",
            );
          }),
        );
        break;
    }

    //delete temp files
    if (fs.existsSync(tempCsvFilePath)) {
      fs.unlinkSync(tempCsvFilePath);
    }
    if (fs.existsSync(tempZipFilePath)) {
      fs.unlinkSync(tempZipFilePath);
    }
  }
}
