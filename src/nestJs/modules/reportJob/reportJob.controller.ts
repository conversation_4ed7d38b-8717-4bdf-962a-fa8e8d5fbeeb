import { Controller, Inject } from "@nestjs/common";
import { CloudEvent } from "firebase-functions/v2";
import { MessagePublishedData } from "firebase-functions/v2/pubsub";

import { PublishMessageForReportJobProcessingParams } from "../pubsub/dto/publishMessageForReportJobProcessingParams.dto";
import { CorrelationContext } from "../utils/context/decorators/correlation-context.decorator";
import LoggerServiceAdapter from "../utils/logger/logger.service";

import { ReportJobService } from "./reportJob.service";

@Controller()
export class ReportJobController {
  constructor(
    private readonly reportJobService: ReportJobService,
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
  ) {}

  @CorrelationContext()
  async checkReportJob(now: Date): Promise<any> {
    this.logger.info("checkReportJobScheduler ", { now });
    const matchedJos = await this.reportJobService.getMatchedReportJobs(now);
    this.logger.info("matchedJos ", { matchedJos });
    return this.reportJobService.sendReportJobAndUpdate(matchedJos);
  }

  @CorrelationContext()
  async processReportJob(event: CloudEvent<MessagePublishedData<PublishMessageForReportJobProcessingParams>>) {
    const data = event.data.message.json;
    this.logger.info("reportJobProcessing ", data);
    const { reportDatePath, yesterday } = await this.reportJobService.generateReportFileAndExport(data);
    const { signedUrl, destinationFilename } = await this.reportJobService.combineAndGetReportSignedUrl(
      reportDatePath,
      data.reportType,
      data.name,
    );

    await this.reportJobService.notifyReportUser(data, signedUrl, yesterday, destinationFilename);
  }
}
