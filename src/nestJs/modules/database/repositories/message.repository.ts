import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";

import { PhoneRecipientType } from "../../message/dto/recipientType.dto";
import { PublishMessageForMessageProcessingParams } from "../../pubsub/dto/publishMessageForMessageProcessing.dto";
import Message from "../entities/message.entity";

import { UserRepository } from "./user.repository";

@Injectable()
export class MessageRepository extends Repository<Message> {
  constructor(
    @InjectRepository(Message) private messageRepository: Repository<Message>,
    private readonly userRepository: UserRepository,
  ) {
    super(messageRepository.target, messageRepository.manager, messageRepository.queryRunner);
  }

  async saveMessageForPhoneRecipient(
    data: PublishMessageForMessageProcessingParams,
    messageProviderId: string,
  ): Promise<Message> {
    const messageToSave = Message.fromJson(data);
    const phoneNumber = (data.recipient as PhoneRecipientType).phone;
    const user = await this.userRepository.findAnonymousUserByPhoneNumber(phoneNumber);
    messageToSave.user = user;
    messageToSave.messageProviderId = messageProviderId;
    return this.messageRepository.save(messageToSave);
  }
}
