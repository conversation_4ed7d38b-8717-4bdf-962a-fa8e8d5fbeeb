import { randomUUID } from "crypto";

import { Api<PERSON>roperty, getSchemaPath } from "@nestjs/swagger";
import { Column, <PERSON><PERSON><PERSON>, <PERSON>in<PERSON><PERSON>umn, ManyToOne, OneToMany, PrimaryColumn } from "typeorm";

import { TripDocument } from "../../appDatabase/documents/trip.document";
import { HailingCreateOrderBody } from "../../me/modules/meHailing/dto/meHailing.dto";
import { PaymentInformationStatus } from "../../payment/dto/paymentInformationStatus.dto";
import { PaymentInformationType } from "../../payment/dto/paymentInformationType.dto";
import { TxHailingRequest } from "../../transaction/dto/tx.dto";
import { TxEventType } from "../../transaction/dto/txEventType.dto";
import {
  TxAdjustmentMetadata,
  TxHailingMetadata,
  TxMetadata,
  TxPaymentInstrumentMetadata,
} from "../../transaction/dto/txMetadata.dto";
import { TxNewAdjustmentFromJsonType } from "../../transaction/dto/txNewAdjustmentFromJsonType.dto";
import { TxPayoutStatus } from "../../transaction/dto/txPayoutStatus.dto";
import { TxTypes } from "../../transaction/dto/txType.dto";
import { AppUser } from "../../user/dto/user.dto";
import LoggerServiceAdapter from "../../utils/logger/logger.service";
import { errorBuilder } from "../../utils/utils/error.utils";

import User from "./user.entity";
import TxTag from "./txTag.entity";
import TxEvent from "./txEvent.entity";
import PaymentTx from "./paymentTx.entity";
import Merchant from "./merchant.entity";
import FleetOrderEntity from "./fleetOrder.entity";
import { DefaultEntity } from "./defaultEntity";
import TxApp from "./app.entity";

/**
 * Tx entity to store tx information
 */
@Entity()
export default class Tx extends DefaultEntity {
  @PrimaryColumn()
  @ApiProperty()
  id: string;

  @ManyToOne(() => TxApp)
  @JoinColumn()
  @ApiProperty({ type: () => TxApp })
  txApp: TxApp;

  @Column()
  @ApiProperty({ type: "enum", enum: TxTypes })
  type: TxTypes;

  @ManyToOne(() => Tx, { nullable: true })
  @JoinColumn()
  @ApiProperty({ type: () => Tx })
  parentTx?: Tx;

  @OneToMany(() => Tx, (tx) => tx.parentTx, { nullable: true })
  @ApiProperty({ isArray: true, type: () => Tx })
  childrenTxs?: Tx[];

  @OneToMany(() => PaymentTx, (paymentTx) => paymentTx.tx, { nullable: true, cascade: ["insert", "update"] })
  @ApiProperty({ isArray: true, type: () => PaymentTx })
  paymentTx?: PaymentTx[];

  @ManyToOne(() => Merchant, { cascade: ["insert", "update"], nullable: true })
  @JoinColumn({ name: "merchantId" })
  @ApiProperty({ type: () => Merchant })
  merchant?: Merchant | null;

  @Column({ nullable: true })
  merchantId?: string;

  @ManyToOne(() => Merchant, { cascade: ["insert", "update"], nullable: true })
  @ApiProperty({ type: () => Merchant })
  payoutMerchant?: Merchant | null;

  @Column("jsonb")
  @ApiProperty({
    oneOf: [
      { $ref: getSchemaPath(TripDocument) },
      { type: getSchemaPath(TxAdjustmentMetadata) },
      { type: getSchemaPath(TxPaymentInstrumentMetadata) },
      { type: getSchemaPath(TxHailingMetadata) },
    ],
  })
  metadata: TxMetadata;

  @Column({ nullable: true })
  @ApiProperty({ type: "enum", enum: TxPayoutStatus })
  payoutStatus?: TxPayoutStatus;

  @Column({ type: "double precision", nullable: true })
  @ApiProperty()
  total?: number;

  @Column({ type: "double precision", nullable: true })
  @ApiProperty()
  payoutAmount?: number;

  @Column({ type: "double precision", nullable: true })
  @ApiProperty()
  adjustment?: number;

  @Column({ type: "double precision", nullable: true })
  @ApiProperty()
  dashFee?: number;

  @ManyToOne(() => User, { nullable: true })
  @ApiProperty({ type: () => User })
  user?: User;

  @Column({ type: "double precision", nullable: true })
  @ApiProperty()
  discount?: number;

  @Column({ type: "double precision", nullable: true })
  @ApiProperty()
  discountThirdParty?: number;

  @Column({ type: "double precision", nullable: true })
  @ApiProperty()
  discountDash?: number;

  @Column({ type: "double precision", nullable: true })
  @ApiProperty()
  bonus?: number;

  @Column({ nullable: true })
  createdBy?: string;

  @OneToMany(() => TxTag, (tagTx) => tagTx.tx, { nullable: true, cascade: ["insert", "update"] })
  txTag?: TxTag[];

  @OneToMany(() => TxEvent, (txEvent) => txEvent.tx, { nullable: true, cascade: ["insert", "update"] })
  txEvents?: TxEvent[];

  @OneToMany(() => FleetOrderEntity, (fleetOrder) => fleetOrder.tx, { nullable: true, cascade: ["insert", "update"] })
  fleetOrder?: FleetOrderEntity;

  static newAdjustmentFromJson = ({ total, parentTx, reason, createdBy }: TxNewAdjustmentFromJsonType) => {
    const newAdjustment = new Tx();
    newAdjustment.id = randomUUID();
    newAdjustment.merchant = parentTx.merchant;
    newAdjustment.payoutMerchant = parentTx.payoutMerchant;
    newAdjustment.metadata = { reason };
    newAdjustment.total = total;
    newAdjustment.parentTx = parentTx;
    newAdjustment.type = TxTypes.TX_ADJUSTMENT;
    newAdjustment.createdBy = createdBy;
    newAdjustment.user = parentTx.user;
    return newAdjustment;
  };

  static fromJson(json: Record<string, any>, logger?: LoggerServiceAdapter): Tx {
    const tx = new Tx();

    if (!json.txApp) {
      throw errorBuilder.txApp.txAppRequired();
    }
    tx.txApp = json.txApp;

    if (json.createdAt) {
      tx.createdAt = new Date(json.createdAt);
    }

    if (json.updatedAt) {
      tx.updatedAt = new Date(json.updatedAt);
    }

    if (json.id) {
      tx.id = json.id;
    }

    tx.type = TxTypes[json.type as TxTypes];
    if (!tx.type) {
      logger?.warn(`Tx.fromJson Tx type ${json.type} is not valid`, { tx: tx.id });
    }

    tx.paymentTx = json.paymentTx?.map((paymentTxJson: Record<string, any>) =>
      PaymentTx.fromJson(paymentTxJson, json.id),
    );

    tx.txTag = json.txTag?.map((txTagJson: Record<string, any>) => TxTag.fromJson(txTagJson, json.id));

    tx.metadata = json.metadata;

    if (json.payoutStatus) {
      tx.payoutStatus = TxPayoutStatus[json.payoutStatus as TxPayoutStatus];
      if (!tx.payoutStatus) {
        logger?.warn(`Tx.fromJson Tx payoutStatus ${json.payoutStatus} is not valid`, { tx: tx.id });
      }
    }

    tx.total = json.total;
    tx.dashFee = json.dashFee;
    tx.payoutAmount = json.payoutAmount;
    tx.adjustment = json.adjustment;

    if (json.user) {
      tx.user = json.user;
    }
    tx.discount = json.discount;
    tx.discountThirdParty = json.discountThirdParty;
    tx.discountDash = json.discountDash;
    tx.bonus = json.bonus;
    return tx;
  }

  static fromHailingRequest(body: HailingCreateOrderBody, user: AppUser, txApp: TxApp): TxHailingRequest {
    const tx = new Tx() as TxHailingRequest;

    tx.id = randomUUID();
    tx.metadata = {
      request: body,
    } as TxHailingMetadata;

    tx.type = TxTypes.HAILING_REQUEST;
    tx.txApp = txApp;
    tx.user = {
      id: user.id,
    } as User;

    const txEvent = new TxEvent();
    txEvent.type = TxEventType.HAILING_USER_CREATES_ORDER;
    tx.txEvents = [txEvent];

    return tx;
  }

  findLastEventType(eventType: TxEventType): TxEvent | undefined {
    return this.txEvents
      ?.filter((txEvent) => txEvent.type === eventType)
      .sort((event1, event2) => event2.createdAt.getTime() - event1.createdAt.getTime())[0]; // Take the first (most recent) item
  }

  findLastPaymentType(paymentInformationType: PaymentInformationType): PaymentTx | undefined {
    return this.paymentTx
      ?.sort((event1, event2) => event2.createdAt.getDate() - event1.createdAt.getDate())
      .find(
        (paymentTx) =>
          paymentTx.type === paymentInformationType && paymentTx.status === PaymentInformationStatus.SUCCESS,
      );
  }
}
