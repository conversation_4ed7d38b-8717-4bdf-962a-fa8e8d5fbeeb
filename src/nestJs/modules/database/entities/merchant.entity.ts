import { ApiProperty } from "@nestjs/swagger";
import { Entity, Column, Index, OneToMany, PrimaryGeneratedColumn, ManyToOne, JoinColumn, Unique } from "typeorm";

import { SessionDocument } from "@nest/modules/appDatabase/documents/session.document";

import { ApplicationStatus, Gender } from "../../merchant/dto/merchant.dto";
import { MerchantMetadata } from "../../merchant/dto/merchantMetadata.dto";
import { PayoutPeriod } from "../../transaction/dto/payout.dto";

import Tx from "./tx.entity";
import { DefaultEntity } from "./defaultEntity";

export type MerchantForPayout = Required<
  Pick<Merchant, "id" | "phoneNumber" | "name" | "bankAccount" | "bankAccountOwnerName" | "bankId">
>;

export enum PlatformMerchantType {
  DASH = "DASH",
  SYNCAB = "SYNCAB",
}

/**
 * Merchant entity to store merchant information
 */
@Unique("unique_idx_phoneNumber_platformMerchantType", ["phoneNumber", "platformMerchantType"])
@Entity()
export default class Merchant extends DefaultEntity {
  @PrimaryGeneratedColumn("uuid")
  @ApiProperty()
  id: string;

  @Index("phoneNumber_index_for_merchant")
  @Column()
  @ApiProperty()
  phoneNumber: string;

  @Column({ nullable: false, default: PlatformMerchantType.DASH })
  @ApiProperty()
  platformMerchantType: PlatformMerchantType;

  @Column({ nullable: true })
  @ApiProperty()
  email?: string;

  @Index("name_index")
  @Column({ nullable: true })
  @ApiProperty()
  name?: string;

  @OneToMany(() => Tx, (tx) => tx.merchant, { nullable: true })
  @ApiProperty({ isArray: true, type: () => Tx })
  txs?: Tx[];

  @OneToMany(() => Tx, (tx) => tx.payoutMerchant, { nullable: true })
  @ApiProperty({ isArray: true, type: () => Tx })
  payoutTxs?: Tx[];

  @Column({ nullable: true })
  @ApiProperty()
  activatedAt?: Date;

  @Column({
    type: "enum",
    enum: ApplicationStatus,
    default: null,
  })
  @ApiProperty({ type: "enum", enum: ApplicationStatus, nullable: true })
  applicationStatus?: ApplicationStatus;

  @Column({ nullable: true })
  bankAccount?: string;

  @Column({ nullable: true })
  bankAccountOwnerName?: string;

  @Column({ nullable: true })
  bankId?: string;

  @Column({ nullable: true })
  bankCardPhoto?: string;

  @Column({ nullable: true })
  @ApiProperty()
  dateOfBirth?: Date;

  @Column({
    type: "enum",
    enum: Gender,
    default: null,
  })
  @ApiProperty({ type: "enum", enum: Gender, nullable: true })
  gender?: Gender;

  @Column({ nullable: true })
  idDocument?: string;

  @Column({ nullable: true })
  idDocumentPhoto?: string;

  @Column({ default: false, type: "boolean" })
  isAdmin: boolean;

  @Column({ nullable: true })
  @ApiProperty()
  nameLocal?: string;

  @Column({ nullable: true })
  @ApiProperty()
  profilePhoto?: string;

  @Column("json", { nullable: true })
  @ApiProperty()
  session?: SessionDocument;

  @Column("json")
  @ApiProperty()
  metadata: MerchantMetadata;

  @Column({ nullable: true })
  @ApiProperty()
  lastExpectedEndTime?: Date;

  @Column({ default: false, type: "boolean" })
  @ApiProperty()
  showCashTrip: boolean;

  @Column({ default: PayoutPeriod.DAILY, type: "enum", enum: PayoutPeriod })
  @ApiProperty()
  payoutPeriod: PayoutPeriod = PayoutPeriod.DAILY;

  @Column("varchar", { nullable: true, unique: true })
  referralCode?: string;

  @Column({ nullable: true })
  referrerId?: string;

  @Column({ nullable: true })
  @Index("contactPhoneNumber_index_for_merchant")
  contactPhoneNumber?: string;

  @ManyToOne(() => Merchant, { nullable: true })
  @JoinColumn({ name: "referrerId" })
  referrer?: Merchant;

  static fromJson(json: Record<string, any>): Merchant {
    const merchant = new Merchant();
    merchant.phoneNumber = json.phone_number ?? json.id;
    merchant.name = json.name;
    merchant.bankAccount = json.bank_account;
    merchant.bankAccountOwnerName = json.bank_account_owner_name;
    merchant.bankId = json.bank_id;
    merchant.gender = json.gender;
    merchant.profilePhoto = json.profile_photo;
    merchant.session = json.session;
    merchant.metadata = {
      driverLicense: json.driver_license,
      driverLicensePhoto: json.driver_license_photo,
      lastname: json.lastname,
      firstname: json.firstname,
    };
    merchant.nameLocal = json.name_ch;
    merchant.idDocument = json.hkid;
    merchant.idDocumentPhoto = json.hkid_photo;
    merchant.activatedAt = json.activated_on
      ? new Date(json.activated_on.seconds * 1000 + json.activated_on.nanoseconds / 1e6)
      : merchant.activatedAt;
    merchant.createdAt = json.created_on
      ? new Date(json.created_on.seconds * 1000 + json.created_on.nanoseconds / 1e6)
      : merchant.createdAt;
    merchant.lastExpectedEndTime = json.last_expected_end_time
      ? new Date(json.last_expected_end_time.seconds * 1000 + json.last_expected_end_time.nanoseconds / 1e6)
      : merchant.lastExpectedEndTime;
    merchant.showCashTrip = json.show_cash_trip;
    merchant.dateOfBirth = json.last_expected_end_time
      ? new Date(json.date_of_birth.seconds * 1000 + json.date_of_birth.nanoseconds / 1e6)
      : merchant.dateOfBirth;
    merchant.bankCardPhoto = json.bank_card_photo;
    if (json.application_status) {
      merchant.applicationStatus = ApplicationStatus[json.application_status as keyof typeof ApplicationStatus];
    }
    if (json.payout_period) {
      merchant.payoutPeriod = json.payout_period;
    }
    merchant.referralCode = json.referral_code;
    if (json.referrer?.id) {
      const referrer = new Merchant();
      referrer.id = json.referrer.id;
      merchant.referrer = referrer;
    }
    if (json.contact_number) {
      merchant.contactPhoneNumber = json.contact_number;
    }

    return merchant;
  }

  canReceivePayout(): this is MerchantForPayout {
    return !!this.name && !!this.bankAccount && !!this.bankAccountOwnerName && !!this.bankId;
  }
}
