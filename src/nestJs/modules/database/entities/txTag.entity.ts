import { <PERSON><PERSON><PERSON>, <PERSON>eteDate<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, PrimaryGeneratedColumn } from "typeorm";

import { TxTagType } from "../../transaction/dto/txTagType.dto";
import LoggerServiceAdapter from "../../utils/logger/logger.service";

import { DefaultEntity } from "./defaultEntity";
import Tx from "./tx.entity";

@Entity()
export default class TxTag extends DefaultEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => Tx, (tx) => tx.id, { eager: true })
  @JoinColumn()
  tx?: Tx;

  @Column()
  tag: TxTagType;

  //Now there are only two kinds of 'createdBy': 'SYSTEM' and 'ADMIN', both of them are string.
  //'SYSTEM' means the txTag is created by the backend system, 'ADMIN' means the txTag is created by the admin portal manually(when calling api).
  //Cause we only have one admin portal user now, we can use 'ADMIN' to represent the admin portal user.
  //If there are more admin portal users, we should create a new entity to store them. And also update the createdBy type.
  @Column()
  createdBy: string;

  @Column("varchar", { length: 500, nullable: true })
  note?: string;

  @DeleteDateColumn({
    type: "timestamp",
    nullable: true,
  })
  removedAt?: Date;

  static createTxTag(tag: TxTagType, txId: string, createdBy = "SYSTEM", note?: string): TxTag {
    const txTag = new TxTag();
    const tx = new Tx();
    tx.id = txId;
    txTag.tx = tx;
    txTag.tag = tag;
    txTag.createdBy = createdBy;
    txTag.note = note;
    return txTag;
  }

  static fromJson(json: Record<string, any>, txId: string, logger?: LoggerServiceAdapter): TxTag {
    const txTag = new TxTag();
    txTag.id = json.id;
    const tx = new Tx();
    tx.id = txId;
    txTag.tx = tx;
    txTag.tag = TxTagType[json.tag as TxTagType];
    if (!txTag.tag) {
      logger?.warn(`TxTag.fromJson tag  ${json.tag} is not valid`, { tx: tx.id });
    }
    txTag.createdBy = json.createdBy;
    txTag.note = json.note;
    txTag.createdAt = new Date(json.createdAt);
    txTag.updatedAt = new Date(json.updatedAt);
    if (json.removedAt) {
      txTag.removedAt = new Date(json.removedAt);
    }
    return txTag;
  }
}
