import { randomUUID } from "crypto";

import { ApiProperty } from "@nestjs/swagger";
import { Timestamp } from "firebase-admin/firestore";
import { AfterInsert, Column, Entity, JoinColumn, ManyToOne, PrimaryColumn } from "typeorm";

import { PaymentInformation } from "../../appDatabase/documents/trip.document";
import { PaymentGatewayResponse } from "../../payment/dto/paymentGatewayResponses.model";
import { PaymentGatewayTypes } from "../../payment/dto/paymentGatewayTypes.dto";
import { PaymentInformationStatus } from "../../payment/dto/paymentInformationStatus.dto";
import { PaymentInformationType } from "../../payment/dto/paymentInformationType.dto";
import { PaymentTxPaymentMethod } from "../../payment/dto/paymentTxPaymentMethod.dto";
import LoggerServiceAdapter from "../../utils/logger/logger.service";
import loggerUtils from "../../utils/utils/logger.utils";

import Tx from "./tx.entity";
import PaymentInstrument from "./paymentInstrument.entity";
import { DefaultEntity } from "./defaultEntity";

/**
 * PaymentTx entity to store payment information
 */
@Entity()
export default class PaymentTx extends DefaultEntity {
  @PrimaryColumn()
  @ApiProperty()
  id: string;

  @Column("double precision", { nullable: true })
  @ApiProperty()
  amount?: number;

  @Column({ nullable: true })
  @ApiProperty()
  gatewayTransactionId?: string;

  @Column({ nullable: true })
  @ApiProperty()
  cardNumber?: string;

  @Column({ nullable: true })
  @ApiProperty()
  paymentMethod?: PaymentTxPaymentMethod;

  @ManyToOne(() => Tx, (tx) => tx.id, { eager: true })
  @JoinColumn()
  @ApiProperty({ type: () => Tx })
  tx: Tx;

  @Column({
    type: "enum",
    enum: PaymentGatewayTypes,
    default: PaymentGatewayTypes.SOEPAY,
  })
  @ApiProperty({ type: "enum", enum: PaymentGatewayTypes })
  gateway: PaymentGatewayTypes;

  @Column("jsonb", { nullable: true })
  @ApiProperty({ type: "object" })
  gatewayResponse?: PaymentGatewayResponse;

  @Column({ nullable: true })
  @ApiProperty({ type: "enum", enum: PaymentInformationStatus })
  status?: PaymentInformationStatus;

  @Column({ nullable: true })
  @ApiProperty({ type: "enum", enum: PaymentInformationType })
  type?: PaymentInformationType;

  @ManyToOne(() => PaymentTx, { nullable: true, eager: true })
  @JoinColumn()
  @ApiProperty({ type: () => PaymentTx })
  parent?: PaymentTx;

  @ManyToOne(() => PaymentInstrument, { nullable: true })
  @JoinColumn()
  @ApiProperty({ type: () => PaymentInstrument })
  paymentInstrument?: PaymentInstrument;

  @Column({ nullable: true })
  @ApiProperty()
  requestedBy?: string;

  static fromJson(json: Record<string, any>, txId?: string, logger?: LoggerServiceAdapter): PaymentTx {
    const paymentTx = new PaymentTx();
    paymentTx.id = json.id ?? randomUUID();
    paymentTx.gatewayResponse = json.gatewayResponse;
    const tx = new Tx();

    if (json.tx) {
      tx.id = json.tx.id;
      tx.txApp = json.tx.txApp;
    }

    if (txId) {
      tx.id = txId;
    }

    paymentTx.tx = tx;
    paymentTx.amount = json.amount;
    paymentTx.gatewayTransactionId = json.gatewayTransactionId;
    paymentTx.cardNumber = json.cardNumber;
    paymentTx.paymentMethod = json.paymentMethod;
    paymentTx.gateway = PaymentGatewayTypes[json.gateway as PaymentGatewayTypes];
    if (!paymentTx.gateway) {
      logger?.warn(`PaymnetTx.fromJson Gateway type ${json.gateway} is not valid`, { tx: tx.id });
    }
    paymentTx.createdAt = new Date(json.createdAt);
    paymentTx.status = PaymentInformationStatus[json.status as PaymentInformationStatus];
    if (!paymentTx.status) {
      logger?.warn(`PaymnetTx.fromJson Payment status ${json.status} is not valid`, { tx: tx.id });
    }
    paymentTx.type = PaymentInformationType[json.type as PaymentInformationType];
    if (!paymentTx.type) {
      logger?.warn(`PaymnetTx.fromJson Payment type ${json.type} is not valid`, { tx: tx.id });
    }
    if (json.parent?.id) {
      const parent = new PaymentTx();
      parent.id = json.parent.id;
      paymentTx.parent = parent;
    }

    if (json.paymentInstrument) {
      paymentTx.paymentInstrument = json.paymentInstrument;
    }

    paymentTx.requestedBy = json.requestedBy;
    return paymentTx;
  }

  toDocument(): PaymentInformation {
    return {
      body: JSON.stringify(this.gatewayResponse ?? {}),
      creationTime: Timestamp.fromDate(this.createdAt),
      status: this.status ?? "",
      type: this.type ?? "",
    };
  }

  @AfterInsert()
  logInsert() {
    loggerUtils.debug(`payment_tx: ${this.id} with tx: ${this.tx?.id} insert successfully: ${JSON.stringify(this)}`);
  }
}
