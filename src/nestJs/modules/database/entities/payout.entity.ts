import { randomUUID } from "crypto";

import { <PERSON>tity, Column, PrimaryColumn } from "typeorm";

import { BankNames } from "../../bank/dto/bankName.dto";
import { TxPayoutStatus } from "../../transaction/dto/txPayoutStatus.dto";

import { DefaultEntity } from "./defaultEntity";

@Entity()
export default class Payout extends DefaultEntity {
  @PrimaryColumn("uuid")
  id: string;

  @Column("json")
  originalRequest: { bankName: BankNames; txIds: string[] };

  @Column({ unique: true })
  batchFile: string;

  @Column({ nullable: true })
  bankFile?: string;

  @Column()
  requestedBy: string;

  @Column()
  requestedAt: Date;

  @Column({ nullable: true })
  processedAt?: Date;

  @Column()
  status: TxPayoutStatus;

  constructor(partial?: Partial<Payout>) {
    super();
    this.id = randomUUID();
    this.status = TxPayoutStatus.PRERELEASED;
    this.batchFile = `payout-${this.id}.txt`;
    this.requestedAt = new Date();
    Object.assign(this, partial);
  }
}
