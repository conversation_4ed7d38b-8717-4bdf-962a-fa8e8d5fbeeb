import {
  LocationLanguage,
  LocationPlaceAutocompleteResponse,
  LocationPlaceDetailsResponse,
} from "../../dto/location.dto";

import { GooglePolylineUtils } from "./google_polyline.utils";

export class ShenzhenBayUtils {
  static readonly SHENZHEN_PLACE_POLYGON_ID = "shenzhen_bay";
  static readonly SHENZHEN_BAY_PORT_PLACE_ID = "DASH_SHENZHEN_BAY_PORT";
  static readonly SHENZHEN_BAY_PLACE = {
    displayNameEn: "Shenzhen Bay Port",
    displayNameZh: "深圳灣口岸",
    formattedAddressEn: "Shenzhen Bay Port, Shenzhen, Guangdong, China",
    formattedAddressZh: "中國深圳市深圳灣口岸",
    placeId: "DASH_SHENZHEN_BAY_PORT",
    lat: 22.498439670639097,
    lng: 113.95016487025208,
  };

  // Different place ids for bridge as compute route requires different points to give a proper route
  static readonly <PERSON>ENZHEN_BAY_BRIDGE_PLACE_ID_FROM = "ChIJ49IruQ_wAzQRaqgX6vv3CuI";
  static readonly SHENZHEN_BAY_BRIDGE_PLACE_ID_TO = "ChIJv7mSvg_wAzQRnOC_PUVhtgY";
  static readonly SHENZHEN_BAY_ADDITIONAL_DISTANCE_FROM = 5500; // 4.5km for bridge + 1 km to taxi stand
  static readonly SHENZHEN_BAY_ADDITIONAL_DISTANCE_TO = 5500; // 6km for bridge + 1 km to taxi stand
  static readonly SHENZHEN_BAY_ADDITIONAL_SECONDS_FROM = 270; // 4.5 minutes
  static readonly SHENZHEN_BAY_ADDITIONAL_SECONDS_TO = 270; // 5.5 minutes
  static readonly SHENZHEN_BAY_POLYLINE_ADDITIONAL_POINTS = [
    { lng: 113.9507178, lat: 22.4973689 },
    { lng: 113.9508036, lat: 22.4869011 },
    { lng: 113.9559535, lat: 22.4757186 },
    { lng: 113.9608458, lat: 22.4713565 },
    { lng: 113.9647082, lat: 22.4669941 },
  ];

  static isShenzhenBayPlace(placeId: string): boolean {
    return placeId === ShenzhenBayUtils.SHENZHEN_BAY_PORT_PLACE_ID;
  }

  static addToPolyline(encodedPolyline: string, originShenzhenBay: boolean = true): string {
    // Decode the existing polyline
    const coordinates = GooglePolylineUtils.decodePolyline(encodedPolyline);

    const coordinatesToAdd = Array.from(ShenzhenBayUtils.SHENZHEN_BAY_POLYLINE_ADDITIONAL_POINTS);
    let combinedCoordinates: { lat: number; lng: number }[];
    if (originShenzhenBay) {
      combinedCoordinates = coordinatesToAdd.concat(coordinates);
    } else {
      coordinatesToAdd.reverse();
      combinedCoordinates = coordinates.concat(coordinatesToAdd);
    }

    // Encode the combined coordinates back to a polyline string
    return GooglePolylineUtils.encodePolyline(combinedCoordinates);
  }

  static getShenzhenBayPlaceDetails(language: LocationLanguage): LocationPlaceDetailsResponse {
    return {
      displayName:
        language === LocationLanguage.ZH_HK
          ? ShenzhenBayUtils.SHENZHEN_BAY_PLACE.displayNameZh
          : ShenzhenBayUtils.SHENZHEN_BAY_PLACE.displayNameEn,
      formattedAddress:
        language === LocationLanguage.ZH_HK
          ? ShenzhenBayUtils.SHENZHEN_BAY_PLACE.formattedAddressZh
          : ShenzhenBayUtils.SHENZHEN_BAY_PLACE.formattedAddressEn,
      placeId: ShenzhenBayUtils.SHENZHEN_BAY_PLACE.placeId,
      lat: ShenzhenBayUtils.SHENZHEN_BAY_PLACE.lat,
      lng: ShenzhenBayUtils.SHENZHEN_BAY_PLACE.lng,
    };
  }

  static autocompleteResultProxy(
    results: LocationPlaceAutocompleteResponse,
    input: string,
  ): LocationPlaceAutocompleteResponse {
    if (ShenzhenBayUtils.searchIsShenzhenEn(input)) {
      const shenzhenBaySuggestion = {
        placeId: ShenzhenBayUtils.SHENZHEN_BAY_PORT_PLACE_ID,
        mainText: ShenzhenBayUtils.SHENZHEN_BAY_PLACE.displayNameEn,
        secondaryText: ShenzhenBayUtils.SHENZHEN_BAY_PLACE.formattedAddressEn,
      };
      results.unshift(shenzhenBaySuggestion);
    } else if (ShenzhenBayUtils.searchIsShenzhenZh(input)) {
      const shenzhenBaySuggestion = {
        placeId: ShenzhenBayUtils.SHENZHEN_BAY_PORT_PLACE_ID,
        mainText: ShenzhenBayUtils.SHENZHEN_BAY_PLACE.displayNameZh,
        secondaryText: ShenzhenBayUtils.SHENZHEN_BAY_PLACE.formattedAddressZh,
      };
      results.unshift(shenzhenBaySuggestion);
    }

    return results;
  }

  static searchIsShenzhenEn(input: string): boolean {
    const shenzhenBayString = "shenzhen bay port";
    const minimumPrefix = "shen";
    const normalizedInput = input.toLowerCase().trim();

    return normalizedInput.startsWith(minimumPrefix) && shenzhenBayString.startsWith(normalizedInput);
  }

  static searchIsShenzhenZh(input: string): boolean {
    const shenzhenBayString = "深圳灣口岸";
    const minimumPrefix = "深圳";
    const normalizedInput = input.trim();

    return normalizedInput.startsWith(minimumPrefix) && shenzhenBayString.startsWith(normalizedInput);
  }
}
