import { Storage } from "@google-cloud/storage";
import { Inject, Injectable, Optional } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { kml as kmlToGeoJSON } from "@tmcw/togeojson";
import * as turf from "@turf/turf";
import { Feature, GeoJsonProperties, MultiPolygon, Polygon } from "geojson";
import { DOMParser } from "xmldom";

import LoggerServiceAdapter from "@nest/modules/utils/logger/logger.service";
import casesUtils from "@nest/modules/utils/utils/case/case.utils";
import { errorBuilder } from "@nest/modules/utils/utils/error.utils";

import { LocalizedLanguage } from "../dto/location.dto";

import { PickupPoint } from "./models/pickup_point_data";

const kmlFileName = "geo_spacial/pickup_points.kml";
@Injectable()
export class GeospatialService {
  private rtreeIndex = turf.geojsonRbush();
  private features = turf.featureCollection([]);
  private storage = new Storage();

  private kmlBucketName = "";

  constructor(
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
    private readonly configService: ConfigService,
    @Optional() initialize: boolean = true,
  ) {
    if (initialize) {
      const projectName = this.configService.getOrThrow<string>("GCLOUD_PROJECT");
      this.kmlBucketName = `${projectName}-hailing`;

      try {
        this.initialize();
      } catch (error) {
        this.logger.error("Failed to initialize GeospatialService", {
          error: error instanceof Error ? error.message : String(error),
          kmlFileName,
          kmlBucketName: this.kmlBucketName,
        });
      }
    }
  }

  async initialize(): Promise<void> {
    this.storage
      .bucket(this.kmlBucketName)
      .file(kmlFileName)
      .download((error, fileContent) => {
        // TODO: error handling
        if (error) {
          throw errorBuilder.geospatial.kmlFileDownloadFailed(kmlFileName, this.kmlBucketName, error);
        }
        if (!fileContent) {
          throw errorBuilder.geospatial.kmlFileIsEmpty(kmlFileName);
        }

        let fileContentStr;
        try {
          fileContentStr = fileContent.toString("utf-8");
        } catch (conversionError) {
          throw errorBuilder.geospatial.kmlFileConversionError(kmlFileName, conversionError);
        }

        // Ensure fileContent is a string
        if (typeof fileContentStr !== "string") {
          throw errorBuilder.geospatial.kmlFileContentNotString(kmlFileName);
        }
        // Parse the KML file content
        const dom = new DOMParser().parseFromString(fileContentStr, "text/xml");
        // Convert KML to GeoJSON
        const geojson = kmlToGeoJSON(dom);
        this.logger.debug(`KML file ${kmlFileName} parsed successfully`, {
          kmlFileName,
          kmlBucketName: this.kmlBucketName,
        });
        this.features = turf.featureCollection(geojson.features.filter((feature) => this.isPolygonFeature(feature)));

        this.logger.debug(`KML file ${kmlFileName} contains ${this.features.features.length} polygon features`, {
          kmlFileName,
          kmlBucketName: this.kmlBucketName,
          featureCount: this.features.features.length,
        });
        // Build the R-tree index
        this.buildRtreeIndex();

        this.logger.debug(`KML file ${kmlFileName} loaded successfully from bucket ${this.kmlBucketName}`, {
          featureCount: this.features.features.length,
        });
      });
  }

  async reinitialize(): Promise<void> {
    this.logger.debug("Reinitializing pickup points from KML file...");
    await this.initialize();
  }

  isPolygonFeature(feature: any): feature is Feature<Polygon | MultiPolygon> {
    return feature.geometry && (feature.geometry.type === "Polygon" || feature.geometry.type === "MultiPolygon");
  }

  async isPointInPolygon(point: [number, number], polygon: Feature<Polygon>): Promise<boolean> {
    const pt = turf.point(point);
    return turf.booleanPointInPolygon(pt, polygon);
  }

  buildRtreeIndex() {
    // Build the R-tree index from the features
    this.rtreeIndex.load(this.features);
  }

  async findPolygonContainingPoint(
    point: [number, number],
  ): Promise<Feature<Polygon, MultiPolygon | GeoJsonProperties> | undefined> {
    // Find indices that contain the point
    const results = this.rtreeIndex.search(turf.point(point));

    if (results.features.length === 0) {
      return undefined;
    }

    // Extract the polygons from the results
    const polygons: Feature<Polygon>[] = results.features
      .map((feature) => {
        if (feature.geometry.type === "Polygon") {
          return feature as Feature<Polygon>;
        }
        return undefined;
      })
      .filter((polygon): polygon is Feature<Polygon> => polygon !== undefined);

    // As rtree uses a bounding box, we need to check if the point is actually inside the polygon
    for (const polygon of polygons) {
      const isInside = await this.isPointInPolygon(point, polygon);
      if (isInside) {
        return polygon; // Return the first polygon that contains the point
      }
    }

    // Return the polygons that contain the point
    return undefined;
  }

  parseAndConvertPolygonDescription(descriptionValue: string): PickupPoint[] | undefined {
    const parsedPolygon = this.parsePolygonDescription(descriptionValue);
    if (!parsedPolygon || !Array.isArray(parsedPolygon)) {
      return undefined;
    }
    return this.convertToPickupPoints(parsedPolygon);
  }

  private parsePolygonDescription(descriptionValue: string): any {
    // Remove all '<br>' occurrences
    const cleaned = descriptionValue.replace(/<br>/g, "");
    // Parse the cleaned string to JSON
    try {
      return JSON.parse(cleaned);
    } catch (error) {
      throw errorBuilder.geospatial.parsePolygonFailed(descriptionValue, error);
    }
  }

  private convertToPickupPoints(polygonDescription: any[]): PickupPoint[] {
    const camelizedData = casesUtils.camelizeKeys({ polygonDescription }) as { polygonDescription: any[] };

    return camelizedData.polygonDescription.map((item) => {
      const i18n: PickupPoint["i18n"] = {};

      Object.entries(item.i18n).forEach(([key, value]) => {
        const enumKey = Object.entries(LocalizedLanguage).find(([, val]) => val === key)?.[0] as
          | keyof typeof LocalizedLanguage
          | undefined;

        if (enumKey) {
          const localizedLang = LocalizedLanguage[enumKey];
          i18n[localizedLang] = {
            displayName:
              typeof value === "object" && value !== null && "displayName" in value
                ? (value as { displayName?: string }).displayName || null
                : null,
            formattedAddress:
              typeof value === "object" && value !== null && "formattedAddress" in value
                ? (value as { formattedAddress?: string }).formattedAddress || null
                : null,
          };
        }
      });

      return new PickupPoint({
        i18n,
        placeId: item.placeId,
        lat: item.lat,
        lng: item.lng,
      });
    });
  }
}
