import { Injectable, Inject } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import axios, { AxiosResponse } from "axios";

import LoggerServiceAdapter from "../utils/logger/logger.service";

import {
  TeamsWebhookConfig,
  TeamsWebhookNotificationResult,
  SendTeamsWebhookMessageDto,
  TeamsThemeColor,
  TeamsMessageCard,
  TeamsWebhookFact,
  TeamsWebhookAction,
} from "./dto/teams-webhook.dto";

/**
 * Microsoft Teams Incoming Webhook Service
 * Independent service for sending messages to Microsoft Teams channels using incoming webhooks
 */
@Injectable()
export class TeamsWebhookService {
  constructor(
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Get Teams webhook configuration from environment variables
   */
  private getWebhookConfig(webhookUrl?: string): TeamsWebhookConfig {
    const defaultWebhookUrl = this.configService.get<string>("TEAMS_WEBHOOK_URL");
    const finalWebhookUrl = webhookUrl || defaultWebhookUrl;

    if (!finalWebhookUrl) {
      throw new Error(
        "Missing Teams webhook URL. Please provide webhookUrl parameter or set TEAMS_WEBHOOK_URL environment variable.",
      );
    }

    return {
      webhookUrl: finalWebhookUrl,
    };
  }

  /**
   * Send a message to Teams using incoming webhook
   */
  async sendMessageToWebhook(messageData: SendTeamsWebhookMessageDto): Promise<TeamsWebhookNotificationResult> {
    try {
      // Skip in test environment
      if (this.configService.get("JEST_WORKER_ID") !== undefined) {
        this.logger.info("Skipping Teams webhook message in test environment");
        return { success: true };
      }

      const config = this.getWebhookConfig(messageData.webhookUrl);

      // Build the message card
      const messageCard: TeamsMessageCard = {
        "@type": "MessageCard",
        "@context": "http://schema.org/extensions",
        summary: messageData.summary || messageData.title || "Notification",
        title: messageData.title,
        text: messageData.message,
        themeColor: messageData.themeColor || TeamsThemeColor.DEFAULT,
      };

      // Add facts if provided
      if (messageData.facts && messageData.facts.length > 0) {
        messageCard.sections = [
          {
            facts: messageData.facts,
          },
        ];
      }

      // Add actions if provided
      if (messageData.actions && messageData.actions.length > 0) {
        messageCard.potentialAction = messageData.actions;
      }

      // Send the message
      const response: AxiosResponse = await axios.post(config.webhookUrl, messageCard, {
        headers: {
          "Content-Type": "application/json",
        },
        timeout: 10000, // 10 second timeout
      });

      this.logger.info("Message sent successfully to Teams webhook", {
        statusCode: response.status,
        webhookUrl: this.maskWebhookUrl(config.webhookUrl),
      });

      return {
        success: true,
        statusCode: response.status,
      };
    } catch (error) {
      this.logger.error(
        "Failed to send message to Teams webhook",
        {
          webhookUrl: messageData.webhookUrl ? this.maskWebhookUrl(messageData.webhookUrl) : "undefined",
          error: error instanceof Error ? error.message : String(error),
        },
        error as Error,
      );

      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        statusCode: axios.isAxiosError(error) ? error.response?.status : undefined,
      };
    }
  }

  /**
   * Send a simple text message
   */
  async sendSimpleMessage(
    webhookUrl: string,
    message: string,
    title?: string,
  ): Promise<TeamsWebhookNotificationResult> {
    return this.sendMessageToWebhook({
      webhookUrl,
      message,
      title,
      themeColor: TeamsThemeColor.INFO,
    });
  }

  /**
   * Send a success message
   */
  async sendSuccessMessage(
    message: string,
    title?: string,
    facts?: TeamsWebhookFact[],
  ): Promise<TeamsWebhookNotificationResult> {
    return this.sendMessageToWebhook({
      message,
      title: title || "✅ Success",
      themeColor: TeamsThemeColor.SUCCESS,
      facts,
    });
  }

  /**
   * Send a warning message
   */
  async sendWarningMessage(
    message: string,
    title?: string,
    facts?: TeamsWebhookFact[],
  ): Promise<TeamsWebhookNotificationResult> {
    return this.sendMessageToWebhook({
      message,
      title: title || "⚠️ Warning",
      themeColor: TeamsThemeColor.WARNING,
      facts,
    });
  }

  /**
   * Send an error message
   */
  async sendErrorMessage(
    message: string,
    title?: string,
    facts?: TeamsWebhookFact[],
  ): Promise<TeamsWebhookNotificationResult> {
    return this.sendMessageToWebhook({
      message,
      title: title || "❌ Error",
      themeColor: TeamsThemeColor.ERROR,
      facts,
    });
  }

  /**
   * Send an info message
   */
  async sendInfoMessage(
    message: string,
    title?: string,
    facts?: TeamsWebhookFact[],
  ): Promise<TeamsWebhookNotificationResult> {
    return this.sendMessageToWebhook({
      message,
      title: title || "ℹ️ Information",
      themeColor: TeamsThemeColor.INFO,
      facts,
    });
  }

  /**
   * Send a message with custom actions
   */
  async sendMessageWithActions(
    webhookUrl: string,
    message: string,
    title: string,
    actions: TeamsWebhookAction[],
    facts?: TeamsWebhookFact[],
  ): Promise<TeamsWebhookNotificationResult> {
    return this.sendMessageToWebhook({
      webhookUrl,
      message,
      title,
      actions,
      facts,
      themeColor: TeamsThemeColor.INFO,
    });
  }

  /**
   * Mask webhook URL for logging (hide sensitive parts)
   */
  private maskWebhookUrl(url: string): string {
    if (!url) return "undefined";

    try {
      const urlObj = new URL(url);
      const pathParts = urlObj.pathname.split("/");
      if (pathParts.length > 2) {
        // Mask the webhook token part
        pathParts[pathParts.length - 1] = "***";
      }
      return `${urlObj.origin}${pathParts.join("/")}`;
    } catch {
      return "invalid-url";
    }
  }
}
