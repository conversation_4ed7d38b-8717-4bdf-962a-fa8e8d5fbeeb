import { Inject, Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { getMessaging, MulticastMessage, Notification } from "firebase-admin/messaging";
import { CloudEvent } from "firebase-functions/v2";
import { MessagePublishedData } from "firebase-functions/v2/pubsub";

import { AppDatabaseService } from "../appDatabase/appDatabase.service";
import { MerchantRepository } from "../database/repositories/merchant.repository";
import { MerchantNotificationTokenRepository } from "../database/repositories/merchantNotificationToken.repository";
import { UserRepository } from "../database/repositories/user.repository";
import { UserNotificationTokenRepository } from "../database/repositories/userNotificationToken.repository";
import { PreferredLanguageType } from "../identity/dto/user.dto";
import { PublishMessageForPushNotificationProcessingParams } from "../pubsub/dto/publishMessageForPushNotificationProcessingParams.dto";
import LoggerServiceAdapter from "../utils/logger/logger.service";
import { errorBuilder } from "../utils/utils/error.utils";

import { CustomData, TemplateMessage, NotificationRecipientType, NotificationTriggerEventType } from "./types";
import { FcmConfig } from "./fcm.config";

@Injectable()
export class FcmService {
  constructor(
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
    private readonly appDatabaseService: AppDatabaseService,
    @InjectRepository(UserRepository)
    private readonly userRepository: UserRepository,
    @InjectRepository(UserNotificationTokenRepository)
    private readonly userNotificationTokenRepository: UserNotificationTokenRepository,
    @InjectRepository(MerchantRepository)
    private readonly merchantRepository: MerchantRepository,
    @InjectRepository(MerchantNotificationTokenRepository)
    private readonly merchantNotificationTokenRepository: MerchantNotificationTokenRepository,
  ) {}

  async pushNotificationToSpecificDevicesWithToken(
    tokens: string[],
    notification: Notification,
    customData?: CustomData,
  ) {
    const messages: MulticastMessage = {
      tokens: tokens,
      notification: notification,
      android: FcmConfig.androidConfig,
      apns: FcmConfig.apnsConfig,
      data: customData,
    };
    await getMessaging()
      .sendEachForMulticast(messages)
      .then((response) => {
        this.logger.info("Successfully sent message:", { response: response });
      })
      .catch((error) => {
        this.logger.error("Error sending message:", { error: error });
      });
  }

  async processPushNotification(
    event: CloudEvent<MessagePublishedData<PublishMessageForPushNotificationProcessingParams>>,
  ) {
    const data = event.data.message.json;
    switch (data.recipientType) {
      case NotificationRecipientType.RIDER: {
        const user = await this.userRepository.findAppUserById(data.recipientId);
        if (!user) throw errorBuilder.user.notFoundInSql(data.recipientId);
        const language = user.preferredLanguage ?? PreferredLanguageType.EN;
        const userNotificationTokens = await this.userNotificationTokenRepository.find({
          where: { user: { id: user.id } },
        });
        if (!userNotificationTokens || userNotificationTokens.length === 0) return;
        const message = await this.getTemplateMessageFromFirestore(
          data.event,
          NotificationRecipientType.RIDER,
          language,
        );
        const notification: Notification = {
          title: message.title,
          body: message.body,
        };
        await this.pushNotificationToSpecificDevicesWithToken(
          userNotificationTokens.map((token) => token.token),
          notification,
        );
        break;
      }
      case NotificationRecipientType.MERCHANT: {
        const merchant = await this.merchantRepository.findOne({ where: { phoneNumber: data.recipientId } });
        if (!merchant) {
          throw errorBuilder.transaction.merchantNotFound(data.recipientId);
        }
        const merchantNotificationTokens = await this.merchantNotificationTokenRepository.find({
          where: { merchant: { id: merchant.id } },
        });
        if (!merchantNotificationTokens || merchantNotificationTokens.length === 0) return;
        const message = await this.getTemplateMessageFromFirestore(
          data.event,
          NotificationRecipientType.MERCHANT,
          PreferredLanguageType.ZHHK,
        );
        const notification: Notification = {
          title: message.title,
          body: message.body,
        };
        await this.pushNotificationToSpecificDevicesWithToken(
          merchantNotificationTokens.map((token) => token.token),
          notification,
        );
        break;
      }
      default:
        break;
    }
  }

  async getTemplateMessageFromFirestore(
    eventType: NotificationTriggerEventType,
    recipientType: NotificationRecipientType,
    language: PreferredLanguageType,
  ): Promise<TemplateMessage> {
    const notificationTemplates = await this.appDatabaseService.configurationRepository().getNotificationTemplates();
    const templateForEvent = notificationTemplates.find((template) => template.event === eventType);
    if (!templateForEvent) throw errorBuilder.pushNotification.templateMissing(eventType);
    const template = templateForEvent.templates.find((template) => template.recipient === recipientType);
    if (!template) throw errorBuilder.pushNotification.wrongTemplate(eventType);
    const message = template.messages.find((message) => message.language === language);
    if (!message) throw errorBuilder.pushNotification.wrongTemplate(eventType);
    return message;
  }
}
