import { Inject, Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import "reflect-metadata";
import { ILike, Like } from "typeorm";

import { UserListingQueryDto, UserSortableType } from "../admin/adminUser/dto/userListingQuery.dto";
import { AppDatabaseService } from "../appDatabase/appDatabase.service";
import { UserDocument } from "../appDatabase/documents/user.document";
import { ProfileAuditActionType, ProfileAuditSourceType } from "../audit/profileAudit/dto/profileAudit.dto";
import User from "../database/entities/user.entity";
import { ProfileAuditRepository } from "../database/repositories/profileAudit.repository";
import { UserRepository } from "../database/repositories/user.repository";
import LoggerServiceAdapter from "../utils/logger/logger.service";
import { errorBuilder } from "../utils/utils/error.utils";
import { UtilsService } from "../utils/utils.service";
import { ListingResponseDto } from "../validation/dto/listingResponseSchema.dto";
import { DirectionType } from "../validation/dto/listingSchema.dto";

import { AppUser } from "./dto/user.dto";

@Injectable()
export class UserService {
  constructor(
    @InjectRepository(UserRepository) private userRepository: UserRepository,
    @Inject(UtilsService) private readonly utilsService: UtilsService,
    private readonly appDatabaseService: AppDatabaseService,
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
    @InjectRepository(ProfileAuditRepository) private profileAuditRepository: ProfileAuditRepository,
  ) {}

  async createUser(userData: Partial<User>): Promise<User> {
    return this.userRepository.createUser(userData);
  }

  async setUserInFirestore(appDatabaseId: string, user: User) {
    const userDocumentToCreate: UserDocument = {
      id: appDatabaseId,
      phoneNumber: user.phoneNumber,
      dashDbId: user.id,
      publicKey: user.publicKey,
    };
    await this.appDatabaseService
      .userRepository()
      .collection.doc(appDatabaseId)
      .set(userDocumentToCreate, { merge: true });
  }

  async getUsers(userListingQueryDto: UserListingQueryDto): Promise<ListingResponseDto<User>> {
    const alias = "user";
    const sort = userListingQueryDto.sort ?? UserSortableType.CREATED_AT;
    const direction = userListingQueryDto.direction ?? DirectionType.DESC;
    const offset = userListingQueryDto.offset ?? 0;
    const limit = userListingQueryDto.limit ?? 50;

    const queryBuilder = this.userRepository.createQueryBuilder(alias).withDeleted();

    queryBuilder.where("1=1");

    if (!userListingQueryDto.includeSecondRow) {
      queryBuilder.andWhere(`${alias}.appDatabaseId IS NOT NULL`);
    }

    if (userListingQueryDto.id) {
      queryBuilder.andWhere({ id: userListingQueryDto.id });
    }

    if (userListingQueryDto.phoneNumber) {
      queryBuilder.andWhere({ phoneNumber: Like(`%${userListingQueryDto.phoneNumber}%`) });
    }

    if (userListingQueryDto.createdAtFrom) {
      queryBuilder.andWhere(`${alias}.createdAt >= :from`, { from: userListingQueryDto.createdAtFrom });
    }
    if (userListingQueryDto.createdAtTo) {
      queryBuilder.andWhere(`${alias}.createdAt <= :to`, { to: userListingQueryDto.createdAtTo });
    }

    if (userListingQueryDto.email) {
      queryBuilder.andWhere({ email: ILike(`%${userListingQueryDto.email}%`) });
    }

    if (userListingQueryDto.name) {
      queryBuilder
        .andWhere({ firstName: ILike(`%${userListingQueryDto.name}%`) })
        .orWhere({ lastName: ILike(`%${userListingQueryDto.name}%`) });
    }

    if (userListingQueryDto.deleted !== undefined) {
      queryBuilder.andWhere(
        userListingQueryDto.deleted ? `${alias}.deletedAt IS NOT NULL` : `${alias}.deletedAt IS NULL`,
      );
    }

    const [users, count] = await queryBuilder
      .orderBy(`${alias}.${sort}`, direction)
      .skip(offset * limit)
      .take(limit)
      .getManyAndCount();

    return { count, data: users };
  }

  async getUserById(userId: string): Promise<User | null> {
    return this.userRepository.findOne({
      where: { id: userId },
    });
  }

  /**Updates the user in the SQL database only */
  async updateDatabaseUser(user: User, userData: Partial<User>): Promise<User> {
    if (userData.id) {
      throw errorBuilder.global.invalidColumns("id", ["appDatabaseId", "id"]);
    }
    if (userData.appDatabaseId) {
      throw errorBuilder.global.invalidColumns("appDatabaseId", ["appDatabaseId", "id"]);
    }
    const newUser = Object.assign(user, userData);
    return this.userRepository.save(newUser);
  }

  async updateUserEmail(appDatabaseId: string, email: string): Promise<User> {
    const user = await this.userRepository.findAppUserById(appDatabaseId);
    user.email = email;
    return this.userRepository.save(user);
  }

  async processUserProfileChanged(
    appDatabaseID: string,
    userDocumentAfter: UserDocument,
    userDocumentBefore?: UserDocument,
  ) {
    const userInSql = await this.userRepository.findOne({ where: { appDatabaseId: appDatabaseID } });
    if (!userInSql) {
      throw errorBuilder.user.notFoundInSql(appDatabaseID);
    }
    this.logger.info(`find userId: ${appDatabaseID} in sql`, { userInSql });
    const updateResult = await this.updateUserFromFirestoreToSql(userInSql, userDocumentAfter);
    if (
      updateResult &&
      userDocumentBefore &&
      (userDocumentBefore.marketingPreferenceEmail !== userDocumentAfter.marketingPreferenceEmail ||
        userDocumentBefore.marketingPreferenceSms !== userDocumentAfter.marketingPreferenceSms ||
        userDocumentBefore.marketingPreferencePush !== userDocumentAfter.marketingPreferencePush)
    ) {
      let action = ProfileAuditActionType.FIRESTORE_MARKETING_EMAIL_UPDATE;
      // NOTE: below condition isn't really necessary.
      // We already have a default value above,
      // and we don't have an else condition anyway.
      if (userDocumentBefore.marketingPreferenceEmail !== userDocumentAfter.marketingPreferenceEmail) {
        action = ProfileAuditActionType.FIRESTORE_MARKETING_EMAIL_UPDATE;
      } else if (userDocumentBefore.marketingPreferenceSms !== userDocumentAfter.marketingPreferenceSms) {
        action = ProfileAuditActionType.FIRESTORE_MARKETING_SMS_UPDATE;
      } else if (userDocumentBefore.marketingPreferencePush !== userDocumentAfter.marketingPreferencePush) {
        action = ProfileAuditActionType.FIRESTORE_MARKETING_PUSH_UPDATE;
      }
      await this.profileAuditRepository.createProfileAudit(
        action,
        updateResult,
        this.utilsService.case.camelizeKeys(userDocumentAfter) as UserDocument,
        ProfileAuditSourceType.FIRESTORE,
      );
    }
  }

  private async updateUserFromFirestoreToSql(currentUser: User, userDocument: UserDocument): Promise<User> {
    const newUser = new User();
    newUser.gender = userDocument.gender;
    newUser.dateOfBirth = userDocument.dateOfBirth;
    newUser.firstName = userDocument.firstName;
    newUser.lastName = userDocument.lastName;
    newUser.preferredLanguage = userDocument.preferredLanguage;
    newUser.marketingPreferenceEmail = userDocument.marketingPreferenceEmail || false;
    newUser.marketingPreferenceSMS = userDocument.marketingPreferenceSms || false;
    newUser.marketingConsent = userDocument.marketingConsent || false;
    newUser.marketingPreferencePush = userDocument.marketingPreferencePush || false;

    return this.updateDatabaseUser(currentUser, newUser);
  }

  async checkAndUpdateKey(appDatabaseId: string, user: AppUser) {
    const userInFirestore = await this.appDatabaseService.userRepository().findOneById(appDatabaseId);
    if (!userInFirestore) {
      throw errorBuilder.user.notFoundInFirestore(appDatabaseId);
    }
    if (!userInFirestore.publicKey) {
      const userDocumentToUpdate: UserDocument = {
        id: appDatabaseId,
        dashDbId: user.id,
        publicKey: user.publicKey,
      };
      await this.appDatabaseService.userRepository().set(userDocumentToUpdate);
    }
  }

  async getOrCreateReferralCode(userId: string): Promise<string> {
    this.logger.debug(`[generateOrFetchReferralCode] Generating or fetching referral code for user ${userId}`);
    const user = await this.userRepository.findOne({ where: { appDatabaseId: userId } });
    if (!user) {
      throw errorBuilder.user.notFoundInSql(userId);
    }

    // If user already has a referral code just return it
    if (user.referralCode) {
      return user.referralCode;
    }

    // Otherwise, generate a new code and write to both SQL and Firestore
    let attempts = 0;
    while (attempts < 10) {
      attempts++;
      const code = this.utilsService.string.generateReferralCode();
      // Try to save the code to SQL
      try {
        user.referralCode = code;
        await this.userRepository.save(user);
      } catch (e: any) {
        if (e.code === "23505") {
          // If the code already exists in SQL, try again
          this.logger.debug(`[generateOrFetchReferralCode] Referral code ${code} already exists. Attempt ${attempts}`);
          continue;
        }
        throw errorBuilder.user.referralCodeGenerationFailed(userId);
      }
      // If the code is successfully saved to SQL, try to save it to Firestore
      await this.appDatabaseService.userRepository().collection.doc(userId).update({ referral_code: code });
      return code;
    }

    throw errorBuilder.user.referralCodeGenerationLimitExceeded;
  }

  async linkReferralCode(userId: string, referralCode: string): Promise<void> {
    this.logger.debug(`[linkReferralCode] Linking user referral code ${referralCode} to user ${userId}`);
    const user = await this.userRepository.findOne({ where: { appDatabaseId: userId } });
    if (!user) {
      throw errorBuilder.user.notFoundInSql(userId);
    }

    // Check if the referral code exists in SQL
    const referrer = await this.userRepository.findOne({ where: { referralCode: referralCode } });
    if (!referrer) {
      throw errorBuilder.user.referralCodeNotFound(referralCode);
    }

    if (referrer.id === user.id) {
      throw errorBuilder.user.referralCodeInvalid(referralCode);
    }

    user.referrerId = referrer.id;
    await this.userRepository.save(user);

    // Save to Firestore
    await this.appDatabaseService
      .userRepository()
      .collection.doc(userId)
      .update({ referrer_code: referralCode, referrer: referrer.appDatabaseId });
  }

  async getReferredUsersCount(userId: string): Promise<number> {
    const user = await this.userRepository.findOne({ where: { appDatabaseId: userId } });
    if (!user) {
      throw errorBuilder.user.notFoundInSql(userId);
    }

    return this.userRepository.count({ where: { referrer: { id: user.id } } });
  }
}
