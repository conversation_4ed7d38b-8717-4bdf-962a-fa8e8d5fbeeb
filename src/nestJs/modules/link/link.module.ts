// Link module for shortened urls
import { Module } from "@nestjs/common";

import { AppDatabaseModule } from "../appDatabase/appDatabase.module";

import { LinkController } from "./link.controller";
import { LinkService } from "./link.service";

@Module({
  imports: [AppDatabaseModule],
  controllers: [LinkController],
  providers: [LinkService, AppDatabaseModule],
  exports: [LinkService],
})
export class LinkModule {}
