import { Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import axios, { AxiosError } from "axios";
import { PhoneMultiFactorInfo, getAuth } from "firebase-admin/auth";
import { Filter } from "firebase-admin/firestore";
import { AuthUserRecord } from "firebase-functions/v2/identity";
import { decode } from "jws";
import moment from "moment";

import { AuthUserListingQueryDto } from "../admin/adminAuthUser/dto/listUser.dto";
import { RoleData } from "../admin/adminAuthUser/dto/updateUserRole.dto";
import { AppDatabaseService } from "../appDatabase/appDatabase.service";
import { AuthDocument } from "../appDatabase/documents/auth.document";
import { EmailService } from "../email/email.service";
import LoggerServiceAdapter from "../utils/logger/logger.service";
import { errorBuilder } from "../utils/utils/error.utils";

import { AuthDto, AuthStatus } from "./dto/auth.dto";
import { CustomTokenAuthResultsDto } from "./dto/custom-token-auth-results.dto";
import { AuthResultsType, EmailAuthResponseDto } from "./dto/email-auth-response.dto";
import { FirebaseAuthResultsDto } from "./dto/firebase-auth-results.dto";
import { FirebaseMfaFinalizeResultsDto } from "./dto/firebase-mfa-finalize-results.dto";
import { FirebaseMfaStartResultsDto } from "./dto/firebase-mfa-start-results.dto";
import { DeviceAuthResponseDto } from "./dto/meter-auth-response.dto";
import { MfaFinalizeDto } from "./dto/mfa-finalize.dto";
import { MfaStartDto } from "./dto/mfa-start.dto";
import { RecaptchaResultsDto } from "./dto/recaptcha-results.dto";
import { AuthClaims, CreateUserError, CustomTokenAuthError, FirebaseAuthError, PasswordAuthError, Role } from "./types";

@Injectable()
export class AuthService {
  private static readonly V1_IDENTITY_API_URL = "https://identitytoolkit.googleapis.com/v1";
  private static readonly V2_IDENTITY_API_URL = "https://identitytoolkit.googleapis.com/v2";

  private readonly apiKey: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly appDatabaseService: AppDatabaseService,
    private readonly logger: LoggerServiceAdapter,
    private readonly emailService: EmailService,
  ) {
    this.apiKey = this.configService.getOrThrow("WEB_API_KEY");
  }

  /**
   * Get the type of the authentication result
   */
  private getAuthResultType(firebaseAuthResultsDto: FirebaseAuthResultsDto): AuthResultsType {
    if (firebaseAuthResultsDto.idToken) {
      return AuthResultsType.SUCCESS;
    } else if (firebaseAuthResultsDto.mfaPendingCredential) {
      return AuthResultsType.MFA_REQUIRED;
    } else {
      return AuthResultsType.ERROR;
    }
  }

  private async sendPasswordResetEmail(email: string, expiration: Date, token: string) {
    const webAdminUrl = this.configService.getOrThrow("WEB_ADMIN_URL");
    await this.emailService.sendActionEmail({
      to: email,
      dynamicTemplateData: {
        subject: "D-ASH Password Reset",
        heading: "Password Reset",
        body: `You have requested a password reset link that will expire at ${moment(expiration)
          .utcOffset("+0800")
          .format("h:mma on MMMM Do")}. Click the button below to reset your password.`,
        button: "Reset Password",
        actionUrl: `${webAdminUrl}/reset-password?token=${token}`,
      },
    });
  }

  /**
   * Get the recaptcha site key required for the recaptcha widget
   */
  async getRecaptcha() {
    try {
      const requestUrl = `${AuthService.V1_IDENTITY_API_URL}/recaptchaParams?key=${this.apiKey}`;
      const response = await axios.get<RecaptchaResultsDto>(requestUrl);
      return response.data;
    } catch (err) {
      this.logger.error(err);
      throw errorBuilder.auth.recaptchaError(err);
    }
  }

  async signInWithEmailAndPassword(email: string, password: string): Promise<EmailAuthResponseDto> {
    const authRepository = this.appDatabaseService.authRepository();
    let auth: AuthDocument | undefined;
    try {
      auth = await authRepository.getAuthByEmail(email);

      if (auth?.isAuthAttemptsExceeded) {
        throw errorBuilder.auth.authAttemptsExceeded();
      }
      const requestUrl = `${AuthService.V1_IDENTITY_API_URL}/accounts:signInWithPassword?key=${this.apiKey}`;
      const response = await axios.post<FirebaseAuthResultsDto>(requestUrl, {
        email,
        password,
        returnSecureToken: true,
      });
      const authResult = response.data;

      const firebaseAuth = getAuth();
      const user = await firebaseAuth.getUserByEmail(email);
      const multiFactor = user.multiFactor?.enrolledFactors[0] as PhoneMultiFactorInfo | undefined;

      const { idToken } = response.data;
      if (auth) {
        auth.updateLastAuth();
        await authRepository.saveAuth(email, auth);
      } else {
        const decoded = idToken ? decode(idToken) : undefined;
        auth = AuthDocument.fromDto({
          status: AuthStatus.NEW,
          userId: authResult.localId,
          email,
          role: decoded?.payload.admin ? Role.ADMIN : AuthDocument.DEFAULT_ROLE,
          failedAuthAttempts: 0,
          lastAuth: new Date(),
          previousHashes: [],
          passwordReset: null,
        });

        await authRepository.saveAuth(email, auth);
      }

      if (auth.isPasswordResetRequired) {
        if (auth.isPasswordResetExpired) {
          const { token, expiration } = auth.initiatePasswordReset();
          await authRepository.saveAuth(email, auth);

          await this.sendPasswordResetEmail(email, expiration, token);
        }

        return {
          type: AuthResultsType.RESET_REQUIRED,
        };
      }

      const customToken = await firebaseAuth.createCustomToken(user.uid, auth?.claims);

      this.logger.info("User authenticated", { email });

      return {
        ...authResult,
        customToken,
        type: this.getAuthResultType(authResult),
        role: auth?.role ?? AuthDocument.DEFAULT_ROLE,
        phone: multiFactor?.phoneNumber,
      };
    } catch (err) {
      if (axios.isAxiosError(err)) {
        const error = err as AxiosError;
        const firebaseAuthError = error.response?.data as FirebaseAuthError;
        switch (firebaseAuthError.error.message) {
          case PasswordAuthError.EMAIL_NOT_FOUND:
          case PasswordAuthError.INVALID_PASSWORD:
          case PasswordAuthError.INVALID_LOGIN_CREDENTIALS:
            this.logger.warn("Auth failed with invalid credentials", { email });
            if (auth) {
              auth.incrementFailedAuthAttempts();
              await authRepository.saveAuth(email, auth);

              if (auth.isAuthAttemptsExceeded) {
                throw errorBuilder.auth.authAttemptsExceeded();
              }
            }

            throw errorBuilder.auth.invalidCredentials();
          case PasswordAuthError.USER_DISABLED:
            throw errorBuilder.auth.userDisabled();
          default:
            this.logger.error(err);
            throw errorBuilder.auth.unknown(firebaseAuthError);
        }
      }
      this.logger.error(err);
      throw err;
    }
  }

  async signInWithDeviceId(deviceId: string): Promise<DeviceAuthResponseDto> {
    const customToken = await getAuth().createCustomToken(deviceId, AuthDocument.getClaimsForRole(Role.METER));
    try {
      const requestUrl = `${AuthService.V1_IDENTITY_API_URL}/accounts:signInWithCustomToken?key=${this.apiKey}`;
      const idTokenResponse = await axios.post<CustomTokenAuthResultsDto>(requestUrl, {
        token: customToken,
        returnSecureToken: true,
      });

      return {
        custom_token: customToken,
        id_token: idTokenResponse.data.idToken,
      };
    } catch (err) {
      if (axios.isAxiosError(err)) {
        const error = err as AxiosError;
        const firebaseAuthError = error.response?.data as FirebaseAuthError;
        switch (firebaseAuthError.error.message) {
          case CustomTokenAuthError.INVALID_CUSTOM_TOKEN:
          case CustomTokenAuthError.CREDENTIAL_MISMATCH:
            this.logger.error(error.response?.data);
            throw errorBuilder.auth.invalidCredentials();
          default:
            this.logger.error(error.response?.data);
            throw errorBuilder.auth.unknown(firebaseAuthError);
        }
      }

      this.logger.error(err);
      throw err;
    }
  }

  /**
   * Trigger the MFA process by sending an OTP
   */
  async startMfa(mfaStartDto: MfaStartDto) {
    try {
      const requestUrl = `${AuthService.V2_IDENTITY_API_URL}/accounts/mfaSignIn:start?key=${this.apiKey}`;
      const response = await axios.post<FirebaseMfaStartResultsDto>(requestUrl, mfaStartDto);
      return response.data;
    } catch (err) {
      this.logger.error(err);
      throw errorBuilder.auth.mfaError(err);
    }
  }

  /**
   * Finalize the MFA process by verifying the OTP code
   */
  async finalizeMfa(mfaFinalizeDto: MfaFinalizeDto) {
    try {
      const requestUrl = `${AuthService.V2_IDENTITY_API_URL}/accounts/mfaSignIn:finalize?key=${this.apiKey}`;
      const response = await axios.post<FirebaseMfaFinalizeResultsDto>(requestUrl, mfaFinalizeDto);
      return response.data;
    } catch (err) {
      this.logger.error(err);
      throw errorBuilder.auth.mfaError(err);
    }
  }

  /**
   * Get the custom claims for a user to be passed into the auth token
   */
  async getClaimsForUser(authUserRecord: AuthUserRecord): Promise<AuthClaims | undefined> {
    if (authUserRecord.email) {
      const authRepository = this.appDatabaseService.authRepository();
      const auth = await authRepository.getAuthByEmail(authUserRecord.email);
      return auth?.claims;
    } else if (authUserRecord.phoneNumber) {
      const driverRepository = this.appDatabaseService.driverRepository();
      const driver = await driverRepository.getDriver(authUserRecord.phoneNumber);
      return AuthDocument.getClaimsForRole(driver ? Role.DRIVER : Role.DEFAULT);
    } else {
      return undefined;
    }
  }

  async startResetPassword(email: string) {
    const authRepository = this.appDatabaseService.authRepository();
    try {
      const auth = await authRepository.getAuthByEmail(email);
      if (!auth) return;

      const { token, expiration } = auth.initiatePasswordReset();
      await authRepository.saveAuth(email, auth);

      await this.sendPasswordResetEmail(email, expiration, token);
    } catch (err) {
      this.logger.error(err);
      throw errorBuilder.auth.unknown(err);
    }
  }

  async finalizeResetPassword(token: string, newPassword: string) {
    const authRepository = this.appDatabaseService.authRepository();
    try {
      const auth = await authRepository.getAuthByValidResetToken(token);
      if (!auth) {
        throw errorBuilder.auth.invalidResetToken();
      }

      if (!AuthDocument.isViablePassword(newPassword)) {
        throw errorBuilder.auth.inadequatePassword();
      }

      const isPasswordUsedInLastYear = await auth.isPasswordUsedInLastYear(newPassword);
      if (isPasswordUsedInLastYear) {
        throw errorBuilder.auth.passwordUsedInLastYear();
      }

      await getAuth().updateUser(auth.userId, { password: newPassword, emailVerified: true });

      await auth.finalizePasswordReset(newPassword);
      await authRepository.saveAuth(auth.email, auth);
    } catch (err) {
      this.logger.error(err);
      throw err;
    }
  }

  async signUpWithEmail(email: string): Promise<AuthDocument> {
    const authRepository = this.appDatabaseService.authRepository();
    let existAuth: AuthDocument | undefined;
    try {
      existAuth = await authRepository.getAuthByEmail(email);
      if (existAuth) {
        throw errorBuilder.auth.emailAlreadyExists(email);
      }
      const requestUrl = `${AuthService.V1_IDENTITY_API_URL}/accounts:signUp?key=${this.apiKey}`;
      const response = await axios.post<FirebaseAuthResultsDto>(requestUrl, {
        email,
        password: "tempPsd123",
        returnSecureToken: true,
      });
      const authResult = response.data;
      this.logger.log("create auth user result", authResult);
      const newAuth = AuthDocument.fromDto({
        status: AuthStatus.NEW,
        userId: authResult.localId,
        email,
        role: AuthDocument.DEFAULT_ROLE,
        failedAuthAttempts: 0,
        lastAuth: new Date(),
        previousHashes: [],
        passwordReset: null,
      });
      await authRepository.saveAuth(email, newAuth);
      return newAuth;
    } catch (err) {
      if (axios.isAxiosError(err)) {
        const error = err as AxiosError;
        const firebaseAuthError = error.response?.data as FirebaseAuthError;
        this.logger.error("firebaseAuthError", firebaseAuthError);
        switch (firebaseAuthError.error.message) {
          case CreateUserError.EMAIL_EXISTS:
            throw errorBuilder.auth.emailAlreadyExists(email);
        }
      }
      this.logger.error(err);
      throw err;
    }
  }

  async updateUserRole(email: string, roleData: RoleData): Promise<AuthDocument> {
    const authRepository = this.appDatabaseService.authRepository();
    try {
      const auth = await authRepository.getAuthByEmail(email);
      if (!auth) {
        throw errorBuilder.auth.userNotFound(email);
      }
      let customUserClaims = AuthDocument.getClaimsForRole(roleData.role);
      const { role, ...additionalClaims } = roleData;
      customUserClaims = { ...customUserClaims, ...additionalClaims };

      const firebaseAuth = getAuth();
      await firebaseAuth.setCustomUserClaims(auth.userId, customUserClaims);
      auth.role = role;
      auth.roleMetadata = additionalClaims ? additionalClaims : null;
      await authRepository.saveAuth(email, auth);
      return auth;
    } catch (err) {
      this.logger.error(err);
      throw err;
    }
  }

  async getUsers(listingQueryDto: AuthUserListingQueryDto): Promise<AuthDto[]> {
    try {
      const filters: Filter[] = [];
      if (listingQueryDto.role) {
        filters.push(Filter.where("role", "==", listingQueryDto.role));
      }
      if (listingQueryDto.email) {
        filters.push(Filter.where("email", "==", listingQueryDto.email));
      }
      return this.appDatabaseService.authRepository().find(...filters);
    } catch (err) {
      this.logger.error(err);
      throw err;
    }
  }

  async resetFailedLoginAttempts(email: string): Promise<AuthDocument> {
    const authRepository = this.appDatabaseService.authRepository();
    try {
      const auth = await authRepository.getAuthByEmail(email);
      if (!auth) {
        throw errorBuilder.auth.userNotFound(email);
      }
      if (auth.failedAuthAttempts >= 5) {
        auth.failedAuthAttempts = 0;
        await authRepository.saveAuth(email, auth);
      }
      return auth;
    } catch (err) {
      this.logger.error(err);
      throw err;
    }
  }

  async forceResetPassword(email: string): Promise<AuthDocument> {
    const authRepository = this.appDatabaseService.authRepository();
    try {
      const auth = await authRepository.getAuthByEmail(email);
      if (!auth) {
        throw errorBuilder.auth.userNotFound(email);
      }
      auth.status = AuthStatus.NEW;
      await authRepository.saveAuth(email, auth);
      return auth;
    } catch (err) {
      this.logger.error(err);
      throw err;
    }
  }

  async disableUser(email: string): Promise<AuthDocument> {
    const authRepository = this.appDatabaseService.authRepository();
    try {
      const auth = await authRepository.getAuthByEmail(email);
      if (!auth) {
        throw errorBuilder.auth.userNotFound(email);
      }
      auth.failedAuthAttempts = 5;
      await authRepository.saveAuth(email, auth);
      return auth;
    } catch (err) {
      this.logger.error(err);
      throw err;
    }
  }
}
