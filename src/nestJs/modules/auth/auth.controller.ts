import { Body, Controller, Get, HttpStatus, Inject, Post, Req, Res, UseGuards } from "@nestjs/common";
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiUnauthorizedResponse,
} from "@nestjs/swagger";
import { AuthenticatedRequest, Response } from "express";
import { BeforeSignInResponse } from "firebase-functions/lib/common/providers/identity";
import { AuthBlockingEvent } from "firebase-functions/v2/identity";
import { decode } from "jws";

import { AuthGuard } from "../../infrastructure/guards/auth.guard";
import { RSAEncryptionService } from "../encryption/rsaEncryption.service";
import { CorrelationContext } from "../utils/context/decorators/correlation-context.decorator";
import LoggerServiceAdapter from "../utils/logger/logger.service";
import { errorBuilder } from "../utils/utils/error.utils";
import { apiTags, buildErrorHtml } from "../utils/utils/swagger.utils";
import { JoiValidationPipe } from "../validation/validationPipe.service";

import { AuthService } from "./auth.service";
import { EmailAuthResponseDto } from "./dto/email-auth-response.dto";
import { EmailAuthDto, emailAuthSchema } from "./dto/email-auth.dto";
import { FirebaseAuthResultsDto } from "./dto/firebase-auth-results.dto";
import { FirebaseMfaFinalizeResultsDto } from "./dto/firebase-mfa-finalize-results.dto";
import { FirebaseMfaStartResultsDto } from "./dto/firebase-mfa-start-results.dto";
import { DeviceAuthResponseDto } from "./dto/meter-auth-response.dto";
import { DeviceAuthDto, deviceAuthSchema } from "./dto/meter-auth.dto";
import { MfaFinalizeDto, mfaFinalizeSchema } from "./dto/mfa-finalize.dto";
import { MfaStartDto, mfaStartSchema } from "./dto/mfa-start.dto";
import { RecaptchaResultsDto } from "./dto/recaptcha-results.dto";
import { ResetPasswordFinalize, resetPasswordFinalizeSchema } from "./dto/reset-password-finalize.dto";
import { ResetPasswordStart, resetPasswordStartSchema } from "./dto/reset-password-start.dto";
import { VerifyAuthResponseDto } from "./dto/verify-auth-response.dto";

@ApiBearerAuth()
@Controller("auth")
@ApiTags(...apiTags.auth)
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    @Inject(LoggerServiceAdapter) private readonly logger: LoggerServiceAdapter,
    private readonly rsaEncryptionService: RSAEncryptionService,
  ) {}

  @UseGuards(AuthGuard)
  @Post()
  @ApiOperation({ summary: "Reverify token and get auth data" })
  @ApiUnauthorizedResponse({
    description: buildErrorHtml([errorBuilder.auth.tokenNotSet(), errorBuilder.auth.missingEmail()]),
  })
  @ApiResponse({ status: HttpStatus.OK, description: "Auth token verification", type: VerifyAuthResponseDto })
  async verifyAuth(@Req() req: AuthenticatedRequest): Promise<VerifyAuthResponseDto> {
    if (!req.user.email) {
      throw errorBuilder.auth.missingEmail();
    }

    return {
      email: req.user.email,
      role: req.user.role,
    };
  }

  @Get("recaptcha")
  @ApiOperation({ summary: "Get recaptcha site key" })
  @ApiResponse({ status: HttpStatus.OK, description: "Recaptcha site key", type: RecaptchaResultsDto })
  async getRecaptcha(): Promise<RecaptchaResultsDto> {
    return this.authService.getRecaptcha();
  }

  @Post("email")
  @ApiOperation({ summary: "Email and password authentication" })
  @ApiUnauthorizedResponse({
    description: buildErrorHtml([errorBuilder.auth.invalidCredentials(), errorBuilder.auth.userDisabled()]),
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: "Email and password authentication",
    type: FirebaseAuthResultsDto,
  })
  async emailAuthenticate(
    @Body(new JoiValidationPipe(emailAuthSchema)) emailAuthDto: EmailAuthDto,
  ): Promise<EmailAuthResponseDto> {
    this.logger.info("Email authentication started", { email: emailAuthDto.email });
    return this.authService.signInWithEmailAndPassword(emailAuthDto.email, emailAuthDto.password);
  }

  @Post("device")
  @ApiOperation({ summary: "Device authentication" })
  @ApiUnauthorizedResponse({
    description: buildErrorHtml([errorBuilder.auth.invalidDeviceJwt(), errorBuilder.auth.invalidCredentials()]),
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: "Device authentication",
    type: DeviceAuthResponseDto,
  })
  async deviceAuthenticate(@Body(new JoiValidationPipe(deviceAuthSchema)) deviceAuthDto: DeviceAuthDto) {
    const signature = decode(deviceAuthDto.deviceToken);
    if (!signature) {
      this.logger.error("Invalid JWT", { deviceToken: deviceAuthDto.deviceToken });
      throw errorBuilder.auth.invalidDeviceJwt();
    }
    const { header, payload } = signature;
    const deviceId = header.kid;

    if (!deviceId || !this.rsaEncryptionService.isAlgorithmSupported(header.alg)) {
      this.logger.error("Invalid JWT", { deviceToken: deviceAuthDto.deviceToken });
      throw errorBuilder.auth.invalidDeviceJwt();
    }

    this.logger.info("Meter authentication started", { deviceId });
    this.logger.debug("JWT Header", { header });
    this.logger.debug("JWT Payload", { payload });

    const isVerified = await this.rsaEncryptionService.devicePublicVerify(deviceAuthDto.deviceToken);
    if (!isVerified) {
      this.logger.error("Invalid JWT", { deviceToken: deviceAuthDto.deviceToken });
      throw errorBuilder.auth.invalidDeviceJwt();
    }

    return this.authService.signInWithDeviceId(deviceId);
  }

  @Post("mfa/start")
  @ApiOperation({ summary: "Start MFA process" })
  @ApiResponse({
    status: HttpStatus.OK,
    description: "MFA process started",
    type: FirebaseMfaStartResultsDto,
  })
  async startMfa(
    @Body(new JoiValidationPipe(mfaStartSchema)) mfaStartDto: MfaStartDto,
  ): Promise<FirebaseMfaStartResultsDto> {
    this.logger.info("MFA started", { phone: mfaStartDto.phoneSignInInfo.phoneNumber });
    return this.authService.startMfa(mfaStartDto);
  }

  @Post("mfa/finalize")
  @ApiOperation({ summary: "Finalize MFA process" })
  @ApiResponse({
    status: HttpStatus.OK,
    description: "MFA process finalized",
    type: FirebaseMfaFinalizeResultsDto,
  })
  async finalizeMfa(
    @Body(new JoiValidationPipe(mfaFinalizeSchema)) mfaFinalizeDto: MfaFinalizeDto,
  ): Promise<FirebaseMfaFinalizeResultsDto> {
    this.logger.info("MFA finalizing");
    return this.authService.finalizeMfa(mfaFinalizeDto);
  }

  @Post("reset-password/start")
  @ApiOperation({ summary: "Start password reset process" })
  @ApiResponse({
    status: HttpStatus.OK,
    description: "Start password reset process",
  })
  async resetPasswordStart(
    @Res() res: Response,
    @Body(new JoiValidationPipe(resetPasswordStartSchema)) resetPasswordStart: ResetPasswordStart,
  ): Promise<void> {
    this.logger.info("Password reset started", { email: resetPasswordStart.email });
    await this.authService.startResetPassword(resetPasswordStart.email);
    res.sendStatus(HttpStatus.OK);
  }

  @Post("reset-password/finalize")
  @ApiOperation({ summary: "Finalize password reset process with new password" })
  @ApiResponse({
    status: HttpStatus.OK,
    description: "Finalize password reset process with new password",
  })
  @ApiBadRequestResponse({
    description: buildErrorHtml([
      errorBuilder.auth.invalidResetToken(),
      errorBuilder.auth.inadequatePassword(),
      errorBuilder.auth.passwordUsedInLastYear(),
    ]),
  })
  async resetPasswordFinalize(
    @Res() res: Response,
    @Body(new JoiValidationPipe(resetPasswordFinalizeSchema)) resetPasswordFinalize: ResetPasswordFinalize,
  ): Promise<void> {
    await this.authService.finalizeResetPassword(resetPasswordFinalize.token, resetPasswordFinalize.newPassword);
    res.sendStatus(HttpStatus.OK);
  }

  @CorrelationContext()
  async processSuccessfulAuth(event: AuthBlockingEvent): Promise<BeforeSignInResponse> {
    this.logger.debug("Auth trigger: processSuccessfulAuth", { event });
    const customClaims = await this.authService.getClaimsForUser(event.data);
    return customClaims
      ? {
          customClaims,
        }
      : {};
  }
}
