import { Modu<PERSON> } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";

import { AppDatabaseModule } from "../appDatabase/appDatabase.module";
import { EmailModule } from "../email/email.module";
import { EncryptionModule } from "../encryption/encryption.module";

import { AuthService } from "./auth.service";
import { AuthController } from "./auth.controller";

@Module({
  imports: [ConfigModule, AppDatabaseModule, EmailModule, EncryptionModule],
  controllers: [AuthController],
  providers: [AuthService],
  exports: [AuthService],
})
export class AuthModule {}
