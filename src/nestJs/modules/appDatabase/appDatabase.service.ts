import { Inject, Injectable } from "@nestjs/common";
import { CollectionReference, Firestore } from "firebase-admin/firestore";

import { TxAppsNames } from "../apps/dto/Apps.dto";
import { AuthDto } from "../auth/dto/auth.dto";
import LoggerServiceAdapter from "../utils/logger/logger.service";
import { UtilsService } from "../utils/utils.service";
import { SessionDto } from "../merchant/merchantDriver/dto/session.dto";

import { appDatabaseProvider } from "./appDatabase.provider";
import { AuthDocument } from "./documents/auth.document";
import { ConfigurationDocument } from "./documents/configuration.document";
import { DriverDocument, NotificationDocument } from "./documents/driver.document";
import { DriverTripDocument } from "./documents/driverTrip.document";
import { FleetDocument } from "./documents/fleet.document";
import { FleetVehicleType } from "./documents/fleetVehicleType.document";
import { LinkDocument } from "./documents/link.document";
import { LockDocument } from "./documents/lock.document";
import { MeterDocument } from "./documents/meter.document";
import { MeterSecurityDocument } from "./documents/meterSecurity.document";
import { QrCodeDocument } from "./documents/qrCode.document";
import { SessionDocument } from "./documents/session.document";
import { TripDocument } from "./documents/trip.document";
import { UserDocument } from "./documents/user.document";
import { UserPaymentInstrumentDocument } from "./documents/userPaymentInstrument.document";
import { VehicleType } from "./documents/vehicleType.document";
import { VoidTxJobDocument } from "./documents/voidTxJob.document";
import AuthRepository from "./repositories/auth.repository";
import ConfigurationRepository from "./repositories/configuration.repository";
import DriverRepository from "./repositories/driver.repository";
import DriverNotificationsRepository from "./repositories/driverNotifications.repository";
import DriverSessionRepository from "./repositories/driverSession.repository";
import DriverSessionTripRepository from "./repositories/driverSessionTrip.repository";
import DriverTripRepository from "./repositories/driverTrip.repository";
import FleetRepository from "./repositories/fleet.repository";
import FleetVehicleTypeRepository from "./repositories/FleetVehicleType.repository";
import LinkRepository from "./repositories/link.repository";
import MeterRepository from "./repositories/meter.repository";
import MeterTripRepository from "./repositories/meterTrip.repository";
import MeterTripLockRepository from "./repositories/meterTripLock.repository";
import QrCodeRepository from "./repositories/qrCode.repository";
import SessionRepository from "./repositories/session.repository";
import TripRepository from "./repositories/trip.repository";
import UsersRepository from "./repositories/user.repository";
import UserPaymentInstrumentRepository from "./repositories/userPaymentInstrument.repository";
import UserTripRepository from "./repositories/userTrip.repository";
import { HailingRequestDocument } from "./documents/hailingRequest.document";
import DriverSessionHailingRequestRepository from "./repositories/driverSessionHailingRequest.repository";
import MeterSecurityRepository from "./repositories/meterSecurity.repository";
import VehicleTypesRepository from "./repositories/vehicleTypes.repository";
import VoidTxJobsRepository from "./repositories/voidTxJobs.repository";


/**
 * AppDatabase service
 */
@Injectable()
export class AppDatabaseService {
  constructor(
    @Inject(appDatabaseProvider) public readonly db: Firestore,
    @Inject(UtilsService) private readonly utilsService: UtilsService,
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
  ) {}

  private readonly dataPoint = <T>(collectionPath: string): CollectionReference<T> =>
    this.db
      .collection(collectionPath)
      .withConverter(this.utilsService.firebase.withConverter(this.logger)) as CollectionReference<T>;

  /**
   * User repository
   */
  public readonly userRepository = () =>
    new UsersRepository(this.dataPoint<UserDocument>(UserDocument.collectionName), this.logger);

  /**
   * User Trip repository
   */
  public readonly userTripRepository = (userId: string) =>
    new UserTripRepository(
      this.dataPoint<TripDocument>(
        `${UserDocument.collectionName}/${userId}/app/${TxAppsNames.TAPXI}/${TripDocument.collectionName}`,
      ),
      this.logger,
    );
  /**
   * Trip repository
   * @returns TripRepository
   */
  public readonly tripRepository = () => new TripRepository(this.dataPoint<TripDocument>("trips"), this.logger);

  /**
   * Meter repository
   * @param meterId string
   * @returns MeterRepository
   */
  public readonly meterRepository = () => new MeterRepository(this.dataPoint<MeterDocument>("meters"), this.logger);

  /**
   * Meter trip repository
   * @param meterId string
   * @returns MeterTripRepository
   */
  public readonly meterTripRepository = (meterId: string) =>
    new MeterTripRepository(this.dataPoint<TripDocument>(`meters/${meterId}/trips`), this.logger);

  /**
   * Session repository
   * @returns SessionRepository
   */
  public readonly sessionRepository = () => new SessionRepository(this.dataPoint<SessionDto>("sessions"), this.logger);

  public readonly driverSessionRepository = (driverId: string) =>
    new DriverSessionRepository(this.dataPoint<SessionDocument>(`drivers/${driverId}/sessions`), this.logger);

  public readonly driverRepository = () => new DriverRepository(this.dataPoint<DriverDocument>("drivers"), this.logger);

  /**
   * Driver Trip Repository
   * @param driverId string
   */
  public readonly driverTripRepository = (driverId: string) =>
    new DriverTripRepository(this.dataPoint<DriverTripDocument>(`drivers/${driverId}/trips`), this.logger);

  public readonly driverSessionTripRepository = (driverId: string, sessionId: string) =>
    new DriverSessionTripRepository(
      this.dataPoint<DriverTripDocument>(`drivers/${driverId}/sessions/${sessionId}/trips`),
      this.logger,
    );

  public readonly driverSessionHailingRequestRepository = (driverId: string, sessionId: string) =>
    new DriverSessionHailingRequestRepository(
      this.dataPoint<HailingRequestDocument>(
        `${DriverDocument.collectionName}/${driverId}/${SessionDocument.collectionName}/${sessionId}/${HailingRequestDocument.collectionName}`,
      ),
      this.logger,
    );

  public readonly driverHailingRequestRepository = (driverId: string) =>
    new DriverSessionHailingRequestRepository(
      this.dataPoint<HailingRequestDocument>(
        `${DriverDocument.collectionName}/${driverId}/${HailingRequestDocument.collectionName}`,
      ),
      this.logger,
    );

  public readonly driverNotificationsRepository = (driverId: string) =>
    new DriverNotificationsRepository(
      this.dataPoint<NotificationDocument>(`drivers/${driverId}/notifications`),
      this.logger,
    );

  private configurationRepositoryInstance: ConfigurationRepository;
  /**
   * Configuration repository
   * @returns ConfigurationRepository
   */
  public readonly configurationRepository = () => {
    if (!this.configurationRepositoryInstance) {
      this.configurationRepositoryInstance = new ConfigurationRepository(
        this.dataPoint<ConfigurationDocument>("configurations"),
        this.logger,
      );
    }
    return this.configurationRepositoryInstance;
  };

  /**
   * User payment instruments repository
   * @param userId string
   * @param app string
   * @returns UserTripRepository
   */
  public readonly userPaymentInstrumentRepository = (userId: string) =>
    new UserPaymentInstrumentRepository(
      this.dataPoint<UserPaymentInstrumentDocument>(`users/${userId}/${UserPaymentInstrumentDocument.collectionName}`),
      this.logger,
    );

  /**
   * Meter Trip Lock repository
   * @param meterId string
   * @param tripId string
   * @param app string
   * @returns UserTripRepository
   */
  public readonly meterTripLockRepository = (meterId: string, tripId: string) =>
    new MeterTripLockRepository(
      this.dataPoint<LockDocument>(`/meters/${meterId}/trips/${tripId}/${LockDocument.collectionName}`),
      this.logger,
    );

  /**
   * Qr code repository
   * @returns QrCodeRepository
   */
  public readonly qrRepository = () =>
    new QrCodeRepository(this.dataPoint<QrCodeDocument>(QrCodeDocument.collectionName), this.logger);

  public readonly authRepository = () =>
    new AuthRepository(this.dataPoint<AuthDto>(AuthDocument.COLLECTION_NAME), this.logger);

  /**
   * Fleet repository
   * @returns FleetRepository
   */
  public readonly fleetRepository = () =>
    new FleetRepository(this.dataPoint<FleetDocument>(`${FleetDocument.collectionName}`), this.logger);

  /**
   * Meter Security repository
   * @returns MeterSecurityRepository
   */
  public readonly meterSecurityRepository = (meterId: string) =>
    new MeterSecurityRepository(this.dataPoint<MeterSecurityDocument>(`meters/${meterId}/securities`), this.logger);

  /**
   * Vehicle Types repository
   * @returns VehicleTypesRepository
   */
  public readonly vehicleTypesRepository = () =>
    new VehicleTypesRepository(this.dataPoint<VehicleType>(`${VehicleType.collectionName}`), this.logger);

  /**
   * Void Tx Jobs repository
   * @returns VoidTxJobsRepository
   */
  public readonly voidTxJobsRepository = () =>
    new VoidTxJobsRepository(this.dataPoint<VoidTxJobDocument>(`${VoidTxJobDocument.collectionName}`), this.logger);

  public readonly fleetVehicleTypesRepository = () =>
    new FleetVehicleTypeRepository(this.dataPoint<FleetVehicleType>(`${FleetVehicleType.collectionName}`), this.logger);

  /**
   * Runs a Firestore transaction
   * @param updateFunction The function to run within the transaction context
   * @returns The result of the transaction
   */
  public async runTransaction<T>(
    updateFunction: (transaction: FirebaseFirestore.Transaction) => Promise<T>,
  ): Promise<T> {
    return this.db.runTransaction(async (transaction) => {
      try {
        const result = await updateFunction(transaction);
        return result;
      } catch (error) {
        this.logger.error(error);
        throw error;
      }
    });
  }

  /**
   * Link repository
   * @returns LinkRepository
   */
  public readonly linkRepository = () =>
    new LinkRepository(this.dataPoint<LinkDocument>(LinkDocument.collectionName), this.logger);
}
