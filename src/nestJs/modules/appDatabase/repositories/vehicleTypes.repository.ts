import { CollectionReference } from "firebase-admin/firestore";

import LoggerServiceAdapter from "../../utils/logger/logger.service";
import { VehicleType } from "../documents/vehicleType.document";

import BaseRepository from "./baseRepository.repository";

class VehicleTypesRepository extends BaseRepository<VehicleType> {
  constructor(collection: CollectionReference<VehicleType>, logger: LoggerServiceAdapter) {
    super(collection, logger);
  }
}

export default VehicleTypesRepository;
