import { CollectionReference, Filter } from "firebase-admin/firestore";

import LoggerServiceAdapter from "../../utils/logger/logger.service";
import { FleetVehicleType } from "../documents/fleetVehicleType.document";

import BaseRepository from "./baseRepository.repository";

class FleetVehicleTypeRepository extends BaseRepository<FleetVehicleType> {
  constructor(collection: CollectionReference<FleetVehicleType>, logger: LoggerServiceAdapter) {
    super(collection, logger);
  }

  async getFleetVehicleType(fleetVehicleKey: string): Promise<FleetVehicleType | undefined> {
    const fleetVehicleTypes = await this.find(...[Filter.where("fleet_vehicle_key", "==", fleetVehicleKey)]);
    return fleetVehicleTypes[0];
  }
}

export default FleetVehicleTypeRepository;
