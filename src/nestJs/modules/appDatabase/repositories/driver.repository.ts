import { CollectionReference } from "firebase-admin/firestore";

import LoggerServiceAdapter from "../../utils/logger/logger.service";
import { DriverDocument } from "../documents/driver.document";

import BaseRepository from "./baseRepository.repository";

class DriverRepository extends BaseRepository<DriverDocument> {
  constructor(driverCollection: CollectionReference<DriverDocument>, logger: LoggerServiceAdapter) {
    super(driverCollection, logger);
  }

  async getDriver(driverId: string): Promise<DriverDocument | undefined> {
    return this.findOneById(driverId);
  }

  async updateSessionDataInDriver(driverId: string, data: Record<string, any>) {
    await this.collection.doc(driverId).set({ session: data }, { merge: true });
  }
}

export default DriverRepository;
