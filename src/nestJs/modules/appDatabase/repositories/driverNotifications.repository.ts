import { CollectionReference } from "firebase-admin/firestore";

import LoggerServiceAdapter from "../../utils/logger/logger.service";
import { NotificationDocument } from "../documents/driver.document";

import BaseRepository from "./baseRepository.repository";

class DriverNotificationsRepository extends BaseRepository<NotificationDocument> {
  constructor(driverNotificationsCollection: CollectionReference<NotificationDocument>, logger: LoggerServiceAdapter) {
    super(driverNotificationsCollection, logger);
  }
}

export default DriverNotificationsRepository;
