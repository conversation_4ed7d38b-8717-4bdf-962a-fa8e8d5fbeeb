import { CollectionReference } from "firebase-admin/firestore";

import LoggerServiceAdapter from "../../utils/logger/logger.service";
import { TripDocument } from "../documents/trip.document";

import BaseRepository from "./baseRepository.repository";

class UserTripRepository extends BaseRepository<TripDocument> {
  constructor(sessionCollection: CollectionReference<TripDocument>, logger: LoggerServiceAdapter) {
    super(sessionCollection, logger);
  }

  async setCurrentTrip(tripId: string, trip: FirebaseFirestore.DocumentData): Promise<void> {
    await this.collection.doc(tripId).set(trip, { merge: true });
  }
}

export default UserTripRepository;
