import { CollectionReference } from "firebase-admin/firestore";

import LoggerServiceAdapter from "../../utils/logger/logger.service";
import { LinkDocument } from "../documents/link.document";

import BaseRepository from "./baseRepository.repository";

/**
 * Repository for link
 */
class LinkRepository extends BaseRepository<LinkDocument> {
  constructor(linkCollection: CollectionReference<LinkDocument>, logger: LoggerServiceAdapter) {
    super(linkCollection, logger);
  }
}

export default LinkRepository;
