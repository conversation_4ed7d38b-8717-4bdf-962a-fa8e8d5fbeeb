import { CollectionReference } from "firebase-admin/firestore";

import LoggerServiceAdapter from "../../utils/logger/logger.service";
import { MeterDocument } from "../documents/meter.document";

import BaseRepository from "./baseRepository.repository";

class MeterRepository extends BaseRepository<MeterDocument> {
  constructor(meterCollection: CollectionReference<MeterDocument>, logger: LoggerServiceAdapter) {
    super(meterCollection, logger);
  }
}

export default MeterRepository;
