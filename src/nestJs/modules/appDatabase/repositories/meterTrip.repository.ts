import { CollectionReference, FieldValue } from "firebase-admin/firestore";

import { DeepPartial } from "@nest/types/types.utils";

import { PaymentMethodSelected } from "../../payment/dto/paymentMethodSelected.dto";
import LoggerServiceAdapter from "../../utils/logger/logger.service";
import { errorBuilder } from "../../utils/utils/error.utils";
import { TripDocument } from "../documents/trip.document";

import BaseRepository from "./baseRepository.repository";

class MeterTripRepository extends BaseRepository<TripDocument> {
  constructor(meterTripCollection: CollectionReference<TripDocument>, logger: LoggerServiceAdapter) {
    super(meterTripCollection, logger);
  }

  async removeDriverAndSession(tripId: string) {
    return this.collection.doc(tripId).update({
      driver: FieldValue.delete(),
      session: FieldValue.delete(),
    });
  }

  async updateMeterTripTip(tripId: string, tip: number, paymentMethodSelected: PaymentMethodSelected) {
    return this.collection
      .doc(tripId)
      .set({ dashTips: tip, isTipsCalculated: false, paymentMethodSelected: paymentMethodSelected }, { merge: true })
      .then(() => this.collection.doc(tripId).get())
      .then((docSnapShot) => {
        if (!docSnapShot.exists) {
          throw errorBuilder.meter.tripNotFound(this.collection.parent?.id || "", tripId);
        }
        return docSnapShot.data() as TripDocument;
      })
      .catch((error) => {
        this.logger.error(error);
        throw errorBuilder.meter.tripUpdateFailed(this.collection.parent?.id || "", tripId);
      });
  }

  async updateTrip(tripId: string, data: DeepPartial<TripDocument>): Promise<TripDocument> {
    return this.collection
      .doc(tripId)
      .set(data, { merge: true })
      .then(() => this.collection.doc(tripId).get())
      .then((doc) => {
        if (!doc.exists) {
          throw errorBuilder.meter.tripNotFound(this.collection.parent?.id || "", tripId);
        }
        return doc.data() as TripDocument;
      })
      .catch((err) => {
        this.logger.error(err);
        throw err;
      });
  }
}

export default MeterTripRepository;
