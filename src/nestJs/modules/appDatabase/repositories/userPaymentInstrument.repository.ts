import { CollectionReference } from "firebase-admin/firestore";

import LoggerServiceAdapter from "../../utils/logger/logger.service";
import { UserPaymentInstrumentDocument } from "../documents/userPaymentInstrument.document";

import BaseRepository from "./baseRepository.repository";

class UserPaymentInstrumentRepository extends BaseRepository<UserPaymentInstrumentDocument> {
  constructor(
    userPaymentInstrumentCollection: CollectionReference<UserPaymentInstrumentDocument>,
    logger: LoggerServiceAdapter,
  ) {
    super(userPaymentInstrumentCollection, logger);
  }
}

export default UserPaymentInstrumentRepository;
