import { CollectionReference } from "firebase-admin/firestore";

import LoggerServiceAdapter from "../../utils/logger/logger.service";
import { MeterSecurityDocument, SecurityType } from "../documents/meterSecurity.document";

import BaseRepository from "./baseRepository.repository";

class MeterSecurityRepository extends BaseRepository<MeterSecurityDocument> {
  constructor(meterSecretCollection: CollectionReference<MeterSecurityDocument>, logger: LoggerServiceAdapter) {
    super(meterSecretCollection, logger);
  }

  /**
   * Query latest security docs by SecurityType
   * @param type SecurityType enum
   */
  async findLatestSecurityByType(type: SecurityType) {
    const snapshot = await this.collection.where("type", "==", type).orderBy("created_at", "desc").limit(1).get();
    return snapshot.docs[0]?.data();
  }
}

export default MeterSecurityRepository;
