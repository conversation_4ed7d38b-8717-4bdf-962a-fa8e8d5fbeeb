import { CollectionReference } from "firebase-admin/firestore";

// import { QrType } from "../../qrCode/dto/qrType";
import LoggerServiceAdapter from "../../utils/logger/logger.service";
import { QrCodeDocument } from "../documents/qrCode.document";

import BaseRepository from "./baseRepository.repository";

class QrCodeRepository extends BaseRepository<QrCodeDocument> {
  constructor(collection: CollectionReference<QrCodeDocument>, logger: LoggerServiceAdapter) {
    super(collection, logger);
  }
}

export default QrCodeRepository;
