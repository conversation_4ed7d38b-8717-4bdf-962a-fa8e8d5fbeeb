import { CollectionReference } from "firebase-admin/firestore";

import LoggerServiceAdapter from "../../utils/logger/logger.service";
import { VoidTxJobDocument } from "../documents/voidTxJob.document";

import BaseRepository from "./baseRepository.repository";

class VoidTxJobsRepository extends BaseRepository<VoidTxJobDocument> {
  constructor(collection: CollectionReference<VoidTxJobDocument>, logger: LoggerServiceAdapter) {
    super(collection, logger);
  }
}

export default VoidTxJobsRepository;
