import { CollectionReference } from "firebase-admin/firestore";

import { SessionDto } from "@nest/modules/merchant/merchantDriver/dto/session.dto";

import LoggerServiceAdapter from "../../utils/logger/logger.service";
import { SessionDocument } from "../documents/session.document";

import BaseRepository from "./baseRepository.repository";

class SessionRepository extends BaseRepository<SessionDto> {
  constructor(sessionCollection: CollectionReference<SessionDto>, logger: LoggerServiceAdapter) {
    super(sessionCollection, logger);
  }

  async getSession(sessionId: string): Promise<SessionDocument | undefined> {
    const session = await this.findOneById(sessionId);
    return session ? SessionDocument.fromDto(session) : undefined;
  }
}

export default SessionRepository;
