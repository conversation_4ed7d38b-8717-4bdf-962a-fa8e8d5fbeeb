import { CollectionReference } from "firebase-admin/firestore";

import Tx from "../../database/entities/tx.entity";
import { TxPayoutStatus } from "../../transaction/dto/txPayoutStatus.dto";
import LoggerServiceAdapter from "../../utils/logger/logger.service";
import { errorBuilder } from "../../utils/utils/error.utils";
import { DriverTripDocument } from "../documents/driverTrip.document";

import BaseRepository from "./baseRepository.repository";

class DriverSessionTripRepository extends BaseRepository<DriverTripDocument> {
  constructor(driverTripCollection: CollectionReference<DriverTripDocument>, logger: LoggerServiceAdapter) {
    super(driverTripCollection, logger);
  }

  async saveSessionTrip(firestoreTx: FirebaseFirestore.Transaction, tripId: string, tx: Tx, expiresAt: Date) {
    let tripData = { ...tx.metadata, expiresAt: expiresAt } as Record<string, any>;
    if (tx.adjustment) {
      tripData = { ...tripData, adjustment: tx.adjustment };
    }
    if (tx.payoutStatus) {
      tripData = { ...tripData, payoutStatus: tx.payoutStatus };
    }
    this.logger.debug(`Saving trip ${tripId} to driver session trips`, tripData);
    return firestoreTx.set(this.collection.doc(tripId), DriverTripDocument.updateDateFormat(tripData), {
      merge: false,
    });
  }

  async updateSessionTrip(
    firestoreTx: FirebaseFirestore.Transaction,
    tripId: string,
    adjustment?: number,
    payoutStatus?: TxPayoutStatus,
  ) {
    return firestoreTx.set(
      this.collection.doc(tripId),
      {
        ...(adjustment ? { adjustment } : {}),
        ...(payoutStatus ? { payoutStatus } : {}),
      },
      { merge: true },
    );
  }

  async updateSessionTripWithObject(tripId: string, data: Record<string, any>): Promise<DriverTripDocument> {
    this.logger.debug(`Updating trip ${tripId} with data`, data);
    return this.collection
      .doc(tripId)
      .set(data, { merge: true })
      .then(() => this.collection.doc(tripId).get())
      .then((doc) => {
        if (!doc.exists) {
          throw errorBuilder.meter.tripNotFound(this.collection.parent?.id || "", tripId);
        }
        return doc.data() as DriverTripDocument;
      })
      .catch((err) => {
        this.logger.error(err);
        throw err;
      });
  }
}

export default DriverSessionTripRepository;
