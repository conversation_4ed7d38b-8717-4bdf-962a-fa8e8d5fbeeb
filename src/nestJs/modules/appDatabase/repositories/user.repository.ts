import { CollectionReference } from "firebase-admin/firestore";

import LoggerServiceAdapter from "../../utils/logger/logger.service";
import { UserDocument } from "../documents/user.document";

import BaseRepository from "./baseRepository.repository";

class UsersRepository extends BaseRepository<UserDocument> {
  constructor(sessionCollection: CollectionReference<UserDocument>, logger: LoggerServiceAdapter) {
    super(sessionCollection, logger);
  }
}

export default UsersRepository;
