import { Controller, Inject } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { CloudEvent } from "firebase-functions/v2";
import { CrashlyticsEvent, NewFatalIssuePayload } from "firebase-functions/v2/alerts/crashlytics";
import { MessagePublishedData } from "firebase-functions/v2/pubsub";

import { CorrelationContext } from "../utils/context/decorators/correlation-context.decorator";
import { DeadLetterQueueMessage } from "../utils/CustomFunction/CustomFunction";
import LoggerServiceAdapter from "../utils/logger/logger.service";

import { MessageTeamsService } from "./messageTeams.service";
import {
  TeamsAdaptiveCard,
  TeamsAdaptiveCardAction,
  TeamsAdaptiveCardBody,
  TeamsAdaptiveCardContent,
  TeamsMessageCard,
  TeamsMessageCardAction,
  TeamsMessageCardActionTarget,
  TeamsMessageCardFact,
  TeamsMessageCardSection,
} from "./dto/messageTeams.dto";

/**
 * MessageTeams controller
 */
@Controller()
export class MessageTeamsController {
  constructor(
    private messageTeamsService: MessageTeamsService,
    private readonly configService: ConfigService,
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
  ) {}

  /**
   * Process sending message
   * @param context functions.EventContext
   * @param message functions.pubsub.Message
   * @returns Message
   */
  @CorrelationContext()
  async sendFirestoreFatalEventsToTeams(event: CrashlyticsEvent<NewFatalIssuePayload>): Promise<void> {
    this.logger.info(`Received Firestore fatal event: ${JSON.stringify(event)}`);
    const appId = event.appId;
    const { id, title, subtitle, appVersion } = event.data.payload.issue;
    const project = this.configService.getOrThrow("GCLOUD_PROJECT");
    const createTime = new Date(event.data.createTime).toLocaleString(); // Convert to readable date-time format

    const platform = appId.split(":")[2];
    const names = title.split(".");
    const packageName = `${names[0]}.${names[1]}.${names[2]}`;
    const env = project?.split("-")[1];
    const capitalizedEnv = env.charAt(0).toUpperCase() + env.slice(1);

    const crashlyticsDashboardUrl = `https://console.firebase.google.com/u/2/project/${project}/crashlytics/app/${platform}:${packageName}/issues/${id}`;

    const webHook =
      "https://prod-10.southeastasia.logic.azure.com:443/workflows/f7673609ed1c4ed1afd79497f17b3bbf/triggers/manual/paths/invoke?api-version=2016-06-01&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&sig=0KK6LnMMzy5TtzwSKIjtocOWsToDdJV2fC3jkm4E54M";

    const adaptiveCard = new TeamsAdaptiveCard();
    const content = new TeamsAdaptiveCardContent();

    // Title
    const titleBlock = new TeamsAdaptiveCardBody();
    titleBlock.type = "TextBlock";
    titleBlock.size = "Large";
    titleBlock.weight = "Bolder";
    titleBlock.text = `🚨 New Fatal Issue Detected in ${capitalizedEnv} 🚨`;
    content.body.push(titleBlock);

    // Facts
    const factSet = new TeamsAdaptiveCardBody();
    factSet.type = "FactSet";
    factSet.facts = [
      { title: "Environment", value: project },
      { title: "App Name", value: packageName },
      { title: "App ID", value: appId },
      { title: "Platform", value: platform },
      { title: "Version", value: appVersion },
      { title: "Title", value: title },
      { title: "Subtitle", value: subtitle },
      { title: "Issue ID", value: id },
      { title: "Create Date", value: createTime },
    ];
    content.body.push(factSet);

    // Description
    const descriptionBlock = new TeamsAdaptiveCardBody();
    descriptionBlock.type = "TextBlock";
    descriptionBlock.text = "A new fatal issue has been detected and needs attention.";
    descriptionBlock.wrap = true;
    content.body.push(descriptionBlock);

    // Action
    const action = new TeamsAdaptiveCardAction();
    action.type = "Action.OpenUrl";
    action.title = "View in Crashlytics";
    action.url = crashlyticsDashboardUrl;
    content.actions.push(action);

    adaptiveCard.attachments.push({
      contentType: "application/vnd.microsoft.card.adaptive",
      content: content,
    });

    return this.messageTeamsService.sendMessageToTeams(adaptiveCard, webHook);
  }

  /**
   * Process sending message
   * @param context functions.EventContext
   * @param message functions.pubsub.Message
   * @returns Message
   */
  @CorrelationContext()
  async sendBackendFatalEventsToTeams(event: CloudEvent<MessagePublishedData<DeadLetterQueueMessage>>): Promise<void> {
    this.logger.info(`Received backend error: ${event}`);

    const project = this.configService.getOrThrow("GCLOUD_PROJECT");
    const createTime = new Date(event.time).toLocaleString(); // Convert to readable date-time format
    const functionName = event.data.message.json.from;
    const topic = event.data.message.json.topic;
    const error = event.data.message.json.error;

    const logsUrl = `https://console.cloud.google.com/run/detail/asia-east2/${functionName}/logs?project=${project}`;

    const webHook =
      "https://netorgft8809665.webhook.office.com/webhookb2/62d277bc-8fc0-4fda-87f0-52e1cac80376@42553b9c-b625-4c8d-9139-fa1af19f40fb/IncomingWebhook/737bed21a842434fa93c5851b9abaa18/788d59b6-e05a-425b-b3b4-8f2ed1e2ae31";

    const messageCard = new TeamsMessageCard();
    messageCard["@type"] = "MessageCard";
    messageCard["@context"] = "http://schema.org/extensions";
    messageCard.summary = "New Error Detected";
    messageCard.title = "🚨 New Error Detected 🚨";
    const section = new TeamsMessageCardSection();
    const facts: TeamsMessageCardFact[] = [];
    facts.push({ name: "Environment:", value: project });
    facts.push({ name: "Create Date:", value: createTime });
    facts.push({ name: "Source:", value: functionName });
    facts.push({ name: "Topic:", value: topic });
    facts.push({ name: "Error Message:", value: error.message });
    facts.push({ name: "Error Code:", value: error.code });
    if (error.cause) {
      facts.push({ name: "Error Cause Message:", value: error.cause.message });
    }
    if (error.custom) {
      facts.push({ name: "Error Custom:", value: JSON.stringify(error.custom) });
    }

    section.facts = facts;
    section.text = "A new error has been detected and needs attention.";
    messageCard.sections = [section];
    const action = new TeamsMessageCardAction();
    action["@type"] = "OpenUri";
    action.name = "View in Cloud Run Logs";
    const target = new TeamsMessageCardActionTarget();
    target.os = "default";
    target.uri = logsUrl;
    action.targets = [target];
    messageCard.potentialAction = [action];

    return this.messageTeamsService.sendMessageToTeams(messageCard, webHook);
  }
}
