import { Controller, Get, Query } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";

import { apiTags } from "../../utils/utils/swagger.utils";
import { JoiValidationPipe } from "../../validation/validationPipe.service";

import { AdminMerchantService } from "./adminMerchant.service";
import { AdminMerchantQueryDto, adminMerchantQuerySchema } from "./dto/adminMerchant.dto";

@ApiBearerAuth()
@Controller()
@ApiTags(...apiTags.admin)
export class AdminMerchantController {
  constructor(private readonly adminMerchantService: AdminMerchantService) {}

  @Get()
  @ApiOperation({ summary: "Get merchants" })
  @ApiResponse({ status: 200, description: "Get merchants" })
  getMerchants(@Query(new JoiValidationPipe(adminMerchantQuerySchema)) query: AdminMerchantQueryDto) {
    return this.adminMerchantService.getMerchants(query);
  }
}
