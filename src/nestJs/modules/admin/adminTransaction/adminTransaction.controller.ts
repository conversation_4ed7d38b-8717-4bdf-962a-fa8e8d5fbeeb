import { Body, Controller, Delete, Get, Param, Patch, Post, Query, Req } from "@nestjs/common";
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiBody,
  ApiExtraModels,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiResponse,
  ApiTags,
  getSchemaPath,
  refs,
} from "@nestjs/swagger";
import { Request } from "express";
import <PERSON><PERSON> from "joi";

import TxEvent from "@nest/modules/database/entities/txEvent.entity";
import {
  AddEventBodyDefault,
  AddEventBodyHailingMerchantAcceptsOrder,
  AddEventBodyHailingUserUpdatesOrder,
  addEventBodySchemaMe,
  AddEventBodyMe,
} from "@nest/modules/me/modules/meTransaction/dto/addEvent.dto";
import { TxEventType } from "@nest/modules/transaction/dto/txEventType.dto";

import { PayoutFileResponse } from "../../bank/dto/payoutFileResponse.dto";
import Tx from "../../database/entities/tx.entity";
import { AddTxTagBodyDto, addTxTagSchema } from "../../transaction/dto/addTxTags.dto";
import {
  CreateAutoPayoutDto,
  CreatePayoutDto,
  createAutoPayoutSchema,
  createPayoutSchema,
} from "../../transaction/dto/createPayout.dto";
import { RemoveTxTagQueryDto, removeTxTagQuerySchema } from "../../transaction/dto/removeTxTags.dto";
import {
  TransactionListingQueryDto,
  TransactionListingResponseDto,
  transactionListingSchema,
} from "../../transaction/dto/transactionListing.dto";
import { txIdSchema } from "../../transaction/dto/tx.dto";
import {
  DASH_FEE_RECALCULATION,
  DashFeeWithDescription,
  TxAdjustmentAmountDto,
  TxAdjustmentCustomAmountDto,
  TxAdjustmentCreateDto,
  txAdjustmentCreateSchema,
  TxAdjustmentDiscountDto,
  TxAdjustmentFullRefundDto,
} from "../../transaction/dto/txAdjustment.dto";
import { TransactionService } from "../../transaction/transaction.service";
import { errorBuilder } from "../../utils/utils/error.utils";
import { apiTags, buildErrorHtml } from "../../utils/utils/swagger.utils";
import { JoiValidationPipe } from "../../validation/validationPipe.service";

import { updateTxMetadataSchema, UpdateTxMetadataDto } from "./dto/updateTxMetadata.dto";
import { AdminTransactionService } from "./adminTransaction.service";

@ApiBearerAuth()
@Controller()
@ApiTags(...apiTags.admin)
export class AdminTransactionController {
  constructor(
    private readonly transactionService: TransactionService,
    private readonly adminTransactionService: AdminTransactionService,
  ) {}

  /**
   * Create transaction adjustment
   * @param txId string
   * @param param1 TxAdjustmentCreate
   * @returns Tx
   */
  @Post(":txId/adjustments")
  @ApiOperation({ summary: "Create transaction adjustment" })
  @ApiExtraModels(
    TxAdjustmentAmountDto,
    TxAdjustmentCustomAmountDto,
    TxAdjustmentDiscountDto,
    TxAdjustmentFullRefundDto,
  )
  @ApiBody({
    description: "Transaction adjustment details",
    required: true,
    schema: {
      oneOf: [
        { $ref: getSchemaPath(TxAdjustmentAmountDto) },
        { $ref: getSchemaPath(TxAdjustmentCustomAmountDto) },
        { $ref: getSchemaPath(TxAdjustmentDiscountDto) },
        { $ref: getSchemaPath(TxAdjustmentFullRefundDto) },
      ],
    },
  })
  @ApiResponse({ status: 201, description: "Create transaction adjustment", type: Tx })
  createTxAdjustment(
    @Param("txId", new JoiValidationPipe(txIdSchema.required())) txId: string,
    @Body(new JoiValidationPipe(txAdjustmentCreateSchema)) txAdjustmentCreate: TxAdjustmentCreateDto,
    @Req() req: Request,
  ): Promise<Tx> {
    const user = req.user;
    if (!user) {
      throw errorBuilder.user.missing();
    }

    return this.adminTransactionService.createTxAdjustment(txId, user, txAdjustmentCreate);
  }

  @Get("adjustment-types")
  @ApiResponse({ status: 200, description: "Get transaction adjustment types" })
  @ApiOkResponse({
    description: "Transaction adjustment types",
  })
  getTxAdjustmentTypes(): Record<DASH_FEE_RECALCULATION, string> {
    return DashFeeWithDescription;
  }

  /**
   * Remove transaction tags
   * @param txId string
   * @param removeTxTagQueryDto RemoveTxTagQueryDto
   */
  @Delete(":txId/tags")
  @ApiOperation({ summary: "Remove transaction tags" })
  @ApiResponse({ status: 200, description: "Remove transaction tags" })
  async removeTxTag(
    @Param("txId", new JoiValidationPipe(txIdSchema.required())) txId: string,
    @Query(new JoiValidationPipe(removeTxTagQuerySchema)) removeTxTagQueryDto: RemoveTxTagQueryDto,
  ): Promise<void> {
    await this.transactionService.removeTagsFromTx(txId, removeTxTagQueryDto.ids);
  }

  /**
   * Add transaction tags
   * @param txId string
   * @param addTxTagBodyDto AddTxTagBodyDto
   * @returns Tx
   */
  @Post(":txId/tags")
  @ApiOperation({ summary: "Add transaction tags" })
  @ApiResponse({ status: 200, description: "Add transaction tags", type: Tx })
  async addTxTag(
    @Param("txId", new JoiValidationPipe(txIdSchema.required())) txId: string,
    @Body(new JoiValidationPipe(addTxTagSchema)) addTxTagBodyDto: AddTxTagBodyDto,
    @Req() req: Request,
  ): Promise<Tx> {
    const requestedBy = req.user?.email || "ADMIN";
    return this.transactionService.addNewTagsToTx(txId, addTxTagBodyDto.tags, requestedBy);
  }

  @Post("payouts")
  @ApiOperation({ summary: "Create a new payout" })
  @ApiResponse({ status: 200, description: "Create a new payout" })
  async createPayout(
    @Body(new JoiValidationPipe(createPayoutSchema)) { txIds, bankName }: CreatePayoutDto,
    @Req() req: Request,
  ): Promise<PayoutFileResponse> {
    const requestedBy = req.user?.email || "ADMIN";
    return this.transactionService.createPayout(txIds, bankName, requestedBy);
  }

  @Post("auto-payouts")
  @ApiOperation({ summary: "Create a new automatic payout" })
  @ApiResponse({ status: 200, description: "Create a new automatic payout" })
  async createAutomaticPayout(
    @Body(new JoiValidationPipe(createAutoPayoutSchema))
    { bankName, payoutMerchantId }: CreateAutoPayoutDto,
    @Req() req: Request,
  ): Promise<PayoutFileResponse> {
    const requestedBy = req.user?.email || "ADMIN";
    return this.transactionService.createAutomaticPayout(bankName, requestedBy, payoutMerchantId);
  }

  @Get()
  @ApiOperation({ summary: "Get transactions" })
  @ApiResponse({ status: 200, description: "Get transactions", type: TransactionListingResponseDto })
  async getTransactions(
    @Query(new JoiValidationPipe(transactionListingSchema)) listingQueryDto: TransactionListingQueryDto,
  ): Promise<TransactionListingResponseDto> {
    return this.transactionService.getTransactions(listingQueryDto);
  }

  /**
   * Get transaction by id
   * @param txId string
   * @returns Tx
   */
  @Get(":txId/")
  @ApiOperation({ summary: "Get transaction by id" })
  @ApiResponse({ status: 200, description: "Get transaction by id", type: Tx })
  async getTransaction(@Param("txId", new JoiValidationPipe(txIdSchema.required())) txId: string): Promise<Tx> {
    const tx = await this.transactionService.getTransactionById(txId);
    if (!tx) {
      throw errorBuilder.transaction.notFound(txId);
    }
    if (tx.paymentTx && tx.paymentTx.length > 0) {
      tx.paymentTx = this.transactionService.convertPaymentTxFromArrayToTree(tx.paymentTx);
    }
    return tx;
  }

  /**
   * Check a transaction and will update like PaymentType if needed, Called in Portal
   * @param txId string
   * @return Tx
   */
  @Patch(":txId/checkPaymentType")
  @ApiOperation({ summary: "Check and Update a transaction's PaymentType" })
  async checkPaymentType(@Param("txId", new JoiValidationPipe(txIdSchema)) txId: string): Promise<Tx> {
    const tx = await this.transactionService.getTransactionById(txId);
    if (!tx) {
      throw errorBuilder.transaction.notFound(txId);
    }
    const { skipUpdate, paymentType } = this.adminTransactionService.checkPaymentTxStatus(tx);
    if (skipUpdate) {
      return tx;
    } else {
      return this.transactionService.updateMetadata(tx, { paymentType: paymentType });
    }
  }

  /**
   * Copy trip to driver
   * @param txId string
   * @returns Tx
   */
  @Post(":txId/copyTripToDriver")
  @ApiOperation({ summary: "Copy trip to driver" })
  @ApiResponse({ status: 200, description: "Copy trip to driver", type: Tx })
  async copyTripToDriver(@Param("txId", new JoiValidationPipe(txIdSchema.required())) txId: string): Promise<Tx> {
    return this.transactionService.copyTripToDriver(txId);
  }

  /**
   * Manually update a transaction's PaymentType and/or Driver, and optionally check/update merchantId and session
   * @param txId string
   * @param body { paymentType?: string, driver?: string, session?: string, checkMerchantId?: boolean }
   * @return Tx
   */
  @Patch(":txId/txMetadata")
  @ApiOperation({
    summary: "Manually update a transaction's metadata: [driver, paymentType, merchantId, session, creationTime]",
  })
  @ApiOkResponse({
    description: "The updated transaction",
    type: Tx,
  })
  @ApiNotFoundResponse({ description: "Transaction not found" })
  async updateTxMetadata(
    @Param("txId", new JoiValidationPipe(txIdSchema)) txId: string,
    @Body(new JoiValidationPipe(updateTxMetadataSchema))
    body: UpdateTxMetadataDto,
  ): Promise<Tx> {
    return this.adminTransactionService.updateTxMetadata(txId, body);
  }

  /**
   * Add Tx Event
   * @param req Request
   * @returns tx Tx
   */
  @Post(":txId/events")
  @ApiOperation({ summary: "Add Tx Event" })
  @ApiResponse({ status: 200, description: "Add Tx Event" })
  @ApiNotFoundResponse({
    description: buildErrorHtml([
      errorBuilder.transaction.notFound("009d13ed-3bae-4f3c-bd37-26686ca23d30"),
      errorBuilder.user.notFoundInSql("IkCO7YHOJ3c073P8RihpRZxX3yC2"),
    ]),
  })
  @ApiExtraModels(AddEventBodyDefault, AddEventBodyHailingMerchantAcceptsOrder, AddEventBodyHailingUserUpdatesOrder)
  @ApiBody({
    schema: {
      anyOf: refs(AddEventBodyDefault, AddEventBodyHailingMerchantAcceptsOrder, AddEventBodyHailingUserUpdatesOrder),
    },
    examples: {
      [TxEventType.HAILING_USER_CREATES_ORDER]: {
        description: "Hailing user creates order",
        value: {
          type: TxEventType.HAILING_USER_CREATES_ORDER,
        },
      },
      [TxEventType.HAILING_MERCHANT_ACCEPTS_ORDER]: {
        description: "Hailing merchant accepts order. Adds the merchant foreign key to the tx.",
        value: {
          type: TxEventType.HAILING_MERCHANT_ACCEPTS_ORDER,
          content: {
            phoneNumber: "+85211111111",
          },
        },
      },
      [TxEventType.HAILING_USER_CANCELS_ORDER]: {
        description:
          "Hailing user cancels order. If the driver accepted more than 3 minutes ago the user will be charged a cancellation fee. Response includes breakdown of charges.",
        value: {
          type: TxEventType.HAILING_USER_CANCELS_ORDER,
        },
      },
      [TxEventType.HAILING_USER_UPDATES_ORDER]: {
        description: "Hailing user updates order. Updates the order metadata with new data such as boost amount.",
        value: {
          type: TxEventType.HAILING_USER_UPDATES_ORDER,
          content: {
            boostAmount: 150,
            minMaxFareCalculations: {
              boost: 150,
            },
          },
        },
      },
      [TxEventType.HAILING_MERCHANT_CANCELS_ORDER]: {
        description: "Hailing merchant cancels order. Removes the merchant foreign key from the tx.",
        value: {
          type: TxEventType.HAILING_MERCHANT_CANCELS_ORDER,
        },
      },
      [TxEventType.HAILING_MERCHANT_PICK_UP_CONFIRMED]: {
        description: "Hailing merchant comfirms pick up.",
        value: {
          type: TxEventType.HAILING_MERCHANT_PICK_UP_CONFIRMED,
          content: {
            txId: "009d13ed-3bae-4f3c-bd37-26686ca23d30",
          },
        },
      },
    },
  })
  @ApiBadRequestResponse({
    description: buildErrorHtml([errorBuilder.user.missing()]),
  })
  async addTxEvent(
    @Param("txId", new JoiValidationPipe(Joi.string().required())) txId: string,
    @Body(new JoiValidationPipe(addEventBodySchemaMe.required())) addEventBody: AddEventBodyMe,
    @Req() req: Request,
  ): Promise<TxEvent> {
    const user = req.user;
    if (!user) throw errorBuilder.user.missing();
    return this.adminTransactionService.addEvent(txId, addEventBody);
  }
}
