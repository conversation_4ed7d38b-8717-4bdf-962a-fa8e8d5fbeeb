import { Controller, Get, Param, Query } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";

import FleetOrderEntity from "@nest/modules/database/entities/fleetOrder.entity";
import { JoiValidationPipe } from "@nest/modules/validation/validationPipe.service";

import { apiTags } from "../../utils/utils/swagger.utils";

import { AdminFleetOrderService } from "./adminFleetOrder.service";
import {
  adminFleetOrderQuerySchema,
  AdminFleetOrderQueryDto,
  AdminFleetOrderResponseDto,
} from "./dto/adminFleetOrder.dto";

@ApiBearerAuth()
@ApiTags(...apiTags.admin)
@Controller()
export class AdminFleetOrderController {
  constructor(private readonly adminFleetOrderService: AdminFleetOrderService) {}

  @ApiOperation({ summary: "Get fleet orders" })
  @ApiResponse({ status: 200, description: "Get fleet orders", type: AdminFleetOrderResponseDto })
  @Get()
  async getFleetOrders(@Query(new JoiValidationPipe(adminFleetOrderQuerySchema)) query: AdminFleetOrderQueryDto) {
    return this.adminFleetOrderService.getFleetOrders(query);
  }

  @ApiOperation({ summary: "Get fleet order" })
  @ApiResponse({ status: 200, description: "Get fleet order", type: FleetOrderEntity })
  @Get(":fleetOrderId")
  async getFleetOrder(@Param("fleetOrderId") fleetOrderId: string) {
    return this.adminFleetOrderService.getFleetOrder(fleetOrderId);
  }
}
