import { Module } from "@nestjs/common";

import { PaymentInstrumentModule } from "../../payment/modules/paymentInstrument/paymentInstrument.module";

import { AdminPaymentInstrumentController } from "./adminPaymentInstrument.controller";

@Module({
  imports: [PaymentInstrumentModule],
  controllers: [AdminPaymentInstrumentController],
  providers: [AdminPaymentInstrumentController],
  exports: [AdminPaymentInstrumentController],
})
export class AdminPaymentInstrumentModule {}
