import { <PERSON>, Get, HttpStatus, Param, Query } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";

import { UserService } from "../../user/user.service";
import { errorBuilder } from "../../utils/utils/error.utils";
import { apiTags } from "../../utils/utils/swagger.utils";
import genericSchemas from "../../validation/dto/genericSchemas.dto";
import { JoiValidationPipe } from "../../validation/validationPipe.service";

import { UserResponseDto } from "./dto/userResponse.dto";
import { UserListingResponseDto } from "./dto/userListingResponse.dto";
import { UserListingQueryDto, userQueryListingSchema } from "./dto/userListingQuery.dto";

@ApiBearerAuth()
@Controller()
@ApiTags(...apiTags.admin)
export class AdminUserController {
  constructor(private readonly userService: UserService) {}

  @Get()
  @ApiOperation({ summary: "Get users" })
  @ApiResponse({ status: HttpStatus.OK, description: "Get users", type: UserListingResponseDto })
  async getUsers(
    @Query(new JoiValidationPipe(userQueryListingSchema)) userListingQueryDto: UserListingQueryDto,
  ): Promise<UserListingResponseDto> {
    const userListing = await this.userService.getUsers(userListingQueryDto);
    return {
      count: userListing.count,
      data: userListing.data.map((user) => UserResponseDto.fromEntity(user)),
    };
  }

  @Get(":userId")
  @ApiOperation({ summary: "Get user by id" })
  @ApiResponse({ status: HttpStatus.OK, description: "Get user by id", type: UserResponseDto })
  async getUserById(
    @Param("userId", new JoiValidationPipe(genericSchemas.uuid.required())) userId: string,
  ): Promise<UserResponseDto> {
    const user = await this.userService.getUserById(userId);
    if (!user) {
      throw errorBuilder.user.notFoundInSql(userId);
    }
    return UserResponseDto.fromEntity(user);
  }

  // TODO: Uncomment this code when endpoint is necessary and after implementing the updateAppDatabaseUser method in the UserService
  // @Patch(":userId")
  // @ApiOperation({ summary: "Update user by id" })
  // @ApiResponse({ status: HttpStatus.OK, description: "Update user by id", type: UserResponseDto })
  // async updateUserById(
  //   @Param("userId", new JoiValidationPipe(genericSchemas.uuid.required())) userId: string,
  //   @Body(new JoiValidationPipe(updateUserSchema)) updateUserDto: UpdateUserDto,
  // ): Promise<UserResponseDto> {
  //   const user = await this.userService.getUserById(userId);
  //   if (!user) {
  //     throw errorBuilder.user.notFoundInSql(userId);
  //   }
  //   const updatedUser = await this.userService.updateDatabaseUser(user, updateUserDto);
  //   return UserResponseDto.fromEntity(updatedUser);
  // }
}
