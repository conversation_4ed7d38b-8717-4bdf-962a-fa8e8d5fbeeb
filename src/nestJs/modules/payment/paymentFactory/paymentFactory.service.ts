import { Injectable } from "@nestjs/common";

import PaymentInstrument from "../../database/entities/paymentInstrument.entity";
import PaymentTx from "../../database/entities/paymentTx.entity";
import Tx from "../../database/entities/tx.entity";
import { errorBuilder } from "../../utils/utils/error.utils";
import { EnquiryResponse, PaymentGatewayResponse } from "../dto/paymentGatewayResponses.model";
import { PaymentGatewayTypes } from "../dto/paymentGatewayTypes.dto";
import PaymentTxFromDocument from "../dto/paymentTxFromDocument.model";
import { KrakenService } from "../modules/paymentInstrument/modules/kraken/kraken.service";

import { GlobalPaymentPaymentService } from "./modules/globalPayment/globalPaymentPayment.service";
import { ManualService } from "./modules/manual/manual.service";
import { SoepayService } from "./modules/soepay/soepay.service";

type FactoryServicesMap = {
  service: SoepayService | ManualService | GlobalPaymentPaymentService | KrakenService;
};

/**
 * Payment factory service
 * This service is used to extract payment transaction information from document
 * and process payment transaction
 */
@Injectable()
export class PaymentFactoryService {
  /**
   * Map of the payment gateways types and their services
   */
  readonly paymentFactoryServices: Record<PaymentGatewayTypes, FactoryServicesMap>;

  constructor(
    private readonly soepayService: SoepayService,
    private readonly manualService: ManualService,
    private readonly globalPaymentService: GlobalPaymentPaymentService,
    private readonly krakenService: KrakenService,
  ) {
    this.paymentFactoryServices = {
      [PaymentGatewayTypes.SOEPAY]: {
        service: this.soepayService,
      },
      [PaymentGatewayTypes.MANUAL]: {
        service: this.manualService,
      },
      [PaymentGatewayTypes.GLOBAL_PAYMENTS]: {
        service: this.globalPaymentService,
      },
      [PaymentGatewayTypes.KRAKEN]: {
        service: this.krakenService,
      },
    };
  }

  /**
   * check tx
   * @param tx Tx
   * @returns { tx: Tx, factory: FactoryServicesMap}
   */
  private checkFactory(paymentGatewayType: PaymentGatewayTypes): FactoryServicesMap {
    if (!paymentGatewayType) {
      throw errorBuilder.payment.factoryPaymentGatewayRequired();
    }

    const factory = this.paymentFactoryServices[paymentGatewayType];
    if (!factory || !factory.service) {
      throw errorBuilder.payment.factoryNotImplemented(paymentGatewayType);
    }

    return factory;
  }

  /**
   * Extract payment transaction information from document
   * @param document
   * @param paymentGatewayType PaymentGatewayTypes
   * @returns PaymentTxFromDocument
   */
  extractPaymentTxInfoFromDocument(
    document: PaymentGatewayResponse,
    paymentGatewayType: PaymentGatewayTypes,
    options?: any,
  ): PaymentTxFromDocument {
    const factory = this.checkFactory(paymentGatewayType);
    return factory.service.extractPaymentTxInfoFromDocument(document, options);
  }

  /**
   * Process capture transaction
   * @param paymentTx PaymentTx
   * @returns PaymentTx
   */
  enquirePayment(paymentTx: PaymentTx, requestedBy?: string): Promise<EnquiryResponse> {
    const factory = this.checkFactory(paymentTx.gateway);
    return factory.service.doEnquiry(paymentTx, requestedBy);
  }

  /**
   * Process capture transaction
   * @param paymentTx PaymentTx
   * @returns PaymentTx
   */
  searchPayment(paymentTx: PaymentTx, requestedBy?: string): Promise<EnquiryResponse> {
    const factory = this.checkFactory(paymentTx.gateway);
    return factory.service.searchPayment(paymentTx, requestedBy);
  }

  /**
   * Process capture transaction
   * @param paymentTx PaymentTx
   * @returns PaymentTx
   */
  isStillProcessing(gateway: PaymentGatewayTypes, paymentInfo: EnquiryResponse): boolean {
    const factory = this.checkFactory(gateway);
    return factory.service.isStillProcessing(paymentInfo);
  }

  /**
   * Process capture transaction
   * @param paymentTx PaymentTx
   * @returns PaymentTx
   */
  processCapture(paymentTx: PaymentTx, total: number, requestedBy?: string): Promise<PaymentTx> {
    const factory = this.checkFactory(paymentTx.gateway);
    return factory.service.processCapture(paymentTx, total, requestedBy);
  }

  /**
   * void previous auths
   * @param paymentTxs PaymentTx[]
   * @returns PaymentTxs PaymentTx[]
   */
  async processVoid(paymentTxs: PaymentTx[], requestedBy?: string): Promise<PaymentTx[]> {
    const factory = this.checkFactory(paymentTxs[0].gateway);
    return factory.service.processVoid(paymentTxs, requestedBy);
  }

  /**
   * Find all sales in processing status
   * @param paymentTxs PaymentTx[]
   * @returns PaymentTx[]
   */
  findAllSalesInProcessingStatus(paymentTxs: PaymentTx[]): PaymentTx[] {
    const factory = this.checkFactory(paymentTxs[0].gateway);
    return factory.service.findAllSalesInProcessingStatus(paymentTxs);
  }

  /**
   * Process sale transaction
   * @param tx Tx
   * @param user User
   * @param paymentInstrument PaymentInstrument
   * @returns PaymentTx
   */
  processSale(
    tx: Tx,
    paymentInstrument: PaymentInstrument,
    overwriteAmount?: number,
    requestedBy?: string,
  ): Promise<PaymentTx> {
    const factory = this.checkFactory(paymentInstrument.paymentGateway);
    return factory.service.processSale(tx, paymentInstrument, overwriteAmount, requestedBy);
  }

  /**
   * Process auth transaction
   * @param tx Tx
   * @param user User
   * @param paymentInstrument PaymentInstrument
   * @returns PaymentTx
   */
  processAuth(
    tx: Tx,
    paymentInstrument: PaymentInstrument,
    overwriteAmount?: number,
    requestedBy?: string,
  ): Promise<PaymentTx> {
    const factory = this.checkFactory(paymentInstrument.paymentGateway);
    return factory.service.processAuth(tx, paymentInstrument, overwriteAmount, requestedBy);
  }

  /**
   * process refund
   * @param paymentTx PaymentTx
   * @param requestedBy string
   * @param customAmount number
   * @returns PaymentTx
   */
  processRefund(paymentTx: PaymentTx, requestedBy?: string, customAmount?: number): Promise<PaymentTx> {
    const factory = this.checkFactory(paymentTx.gateway);
    return factory.service.processRefund(paymentTx, requestedBy, customAmount);
  }
}
