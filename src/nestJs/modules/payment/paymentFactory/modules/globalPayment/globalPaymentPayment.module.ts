import { Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";

import { PaymentTxRepository } from "../../../../database/repositories/paymentTx.repository";
import { GlobalPaymentModule } from "../../../modules/paymentInstrument/modules/globalPayment/globalPayment.module";

import { GlobalPaymentPaymentService } from "./globalPaymentPayment.service";

/**
 * GlobalPayment module
 */
@Module({
  imports: [ConfigModule, GlobalPaymentModule],
  providers: [GlobalPaymentPaymentService, PaymentTxRepository],
  exports: [GlobalPaymentPaymentService],
})
export class GlobalPaymentPaymentModule {}
