import { HttpModule } from "@nestjs/axios";
import { Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";

import { PaymentTxRepository } from "../../../../database/repositories/paymentTx.repository";

import { SoepayService } from "./soepay.service";

/**
 * Soepay module
 */
@Module({
  imports: [ConfigModule, HttpModule],
  providers: [SoepayService, PaymentTxRepository],
  exports: [SoepayService],
})
export class SoepayModule {}
