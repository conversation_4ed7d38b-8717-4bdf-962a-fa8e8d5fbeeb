import { randomUUID } from "crypto";

import { Inject, Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { InjectRepository } from "@nestjs/typeorm";
import cybersourceRestApi from "cybersource-rest-client";

import { AppDatabaseService } from "@nest/modules/appDatabase/appDatabase.service";
import { PaymentInstrumentRepository } from "@nest/modules/database/repositories/paymentInstument.repository";
import { PaymentGatewayResponse } from "@nest/modules/payment/dto/paymentGatewayResponses.model";
import { PaymentTxPaymentMethod } from "@nest/modules/payment/dto/paymentTxPaymentMethod.dto";
import { buildMessage } from "@nest/modules/utils/utils/logger.utils";

import PaymentInstrument from "../../../../../database/entities/paymentInstrument.entity";
import PaymentTx from "../../../../../database/entities/paymentTx.entity";
import Tx from "../../../../../database/entities/tx.entity";
import User from "../../../../../database/entities/user.entity";
import { PaymentTxRepository } from "../../../../../database/repositories/paymentTx.repository";
import {
  CreatePaymentInstrumentBody,
  UpdatePaymentInstrumentBody,
} from "../../../../../me/modules/mePaymentInstrument/mePaymentInstrument.dto";
import LoggerServiceAdapter from "../../../../../utils/logger/logger.service";
import { errorBuilder } from "../../../../../utils/utils/error.utils";
import { PaymentGatewayTypes } from "../../../../dto/paymentGatewayTypes.dto";
import { PaymentInformationStatus } from "../../../../dto/paymentInformationStatus.dto";
import { PaymentInformationType } from "../../../../dto/paymentInformationType.dto";
import { GlobalPaymentCardTypeMap } from "../../../../paymentFactory/modules/globalPayment/dto/globalPayment.dto";
import GlobalPaymentDefault from "../../../_providers/globalPaymentDefault";
import {
  CreatePaymentInstrumentIdentifierResponse,
  PaymentInstrumentState,
  Initiate3DSecureResponse,
  CreatePaymentResponse,
  ValidateAuthenticationResultResponse,
  CheckPayerAuthEnrollmentResponse,
  CreatePaymentInstrumentResponse,
  PaymentInstrumentType,
} from "../../dto/paymentInstrument.dto";
import { KrakenService } from "../kraken/kraken.service";
import PaymentInstrumentInterface from "../paymentInstrumentInterface";

import {
  PAYMENT_MERCHANT_INFORMATION_MERCHANT_DESCRIPTOR_AUTH,
  ThreeDSecureOptions,
  payerAuthenticationStatusMapping,
} from "./dto/globalPayment.dto";

/**
 * GlobalPayment service
 */
@Injectable()
export class GlobalPaymentService extends GlobalPaymentDefault implements PaymentInstrumentInterface {
  apiClient: cybersourceRestApi.ApiClient;
  config: cybersourceRestApi.ApiConfig;
  maxRetry: number = 3;
  constructor(
    readonly configService: ConfigService,
    @InjectRepository(PaymentTxRepository) private paymentTxRepository: PaymentTxRepository,
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
    @InjectRepository(PaymentInstrumentRepository) private paymentInstrumentRepository: PaymentInstrumentRepository,
    private readonly krakenService: KrakenService,
    private readonly appDatabaseService: AppDatabaseService,
  ) {
    super(configService);
  }

  async getMigratingPaymentToKraken(): Promise<boolean> {
    return await this.appDatabaseService.configurationRepository().getMigratingPaymentToKraken();
  }

  promisify = <T>(
    fn: any,
    paymentTx?: PaymentTx,
    requestedBy?: string,
    retryCount = 0,
  ): ((...args: any) => Promise<T>) => {
    this.logger.log("[GP] promisify", { fn });
    return (...args: any) => {
      this.logger.log("[GP] promisify", { args });
      return new Promise<T>((resolve, reject) => {
        this.logger.log("[GP] promisify promise");
        fn(...args, async (error: any, data: any) => {
          this.logger.log("[GP] promisify promise callback", { error, data });

          if ((!data || (!data.id && !data.searchId)) && !error && paymentTx) {
            this.logger.log("[GP] promisify retry", { retryCount });

            if (retryCount >= this.maxRetry) {
              return reject(`Max retry reached for ${fn.name}`);
            }

            const searchResult = await this.searchPayment(paymentTx, requestedBy);
            const gatewayResponse = searchResult?.gatewayResponse as unknown as cybersourceRestApi.SearchResponse;
            const content = gatewayResponse?._embedded?.transactionSummaries?.[0];
            if (gatewayResponse?.totalCount !== 0 && content) {
              return resolve(content as unknown as T);
            }

            return this.promisify<T>(
              fn,
              paymentTx,
              requestedBy,
              retryCount + 1,
            )(...args)
              .then(resolve)
              .catch(reject);
          }

          if (error) {
            return reject(error);
          } else {
            return resolve(data);
          }
        });
      });
    };
  };

  async deletePaymentInstrument(paymentInstrument: PaymentInstrument, user: User): Promise<PaymentInstrument> {
    return paymentInstrument;
  }

  async createNewPaymentInstrument(content: CreatePaymentInstrumentBody, user: User): Promise<PaymentInstrument> {
    const paymentInstrumentIdentifier = await this.createPaymentInstrumentIdentifier(content);

    const paymentInstrument = await this.createPaymentInstrument(
      {
        instrumentIdentifier: paymentInstrumentIdentifier.instrumentIdentifier,
        cardType: content.cardType,
      },
      content,
      user,
    );

    let toCreatePaymentInstrument = await this.paymentInstrumentRepository.findOne({
      where: { instrumentIdentifier: paymentInstrumentIdentifier.instrumentIdentifier, user: { id: user.id } },
    });

    if (!toCreatePaymentInstrument) {
      toCreatePaymentInstrument = this.paymentInstrumentRepository.create({
        id: randomUUID(),
        user,
        token: paymentInstrument.token,
        instrumentIdentifier: paymentInstrumentIdentifier.instrumentIdentifier,
        expirationYear: content.expirationYear,
        expirationMonth: content.expirationMonth,
        cardHolderName: content.cardHolderName,
        cardType: content.cardType,
        cardPrefix: content.cardNumber.substring(0, 6),
        cardSuffix: content.cardNumber.substring(content.cardNumber.length - 4),
        state: PaymentInstrumentState.ACTIVE,
        paymentGateway: PaymentGatewayTypes.GLOBAL_PAYMENTS,
      });
      toCreatePaymentInstrument.updateExpirationDate();
    }

    try {
      const krakenResponse = await this.krakenService.createCard({
        cardNumber: content.cardNumber,
        cardHolderName: content.cardHolderName,
        expirationYear: content.expirationYear,
        expirationMonth: content.expirationMonth,
        type: content.cardType,
        cvv: content.securityCode,
        userId: user.id,
      });

      toCreatePaymentInstrument.tokenKraken = krakenResponse.token;
    } catch (error: any) {
      this.logger.error("Error creating card in Kraken", error);
    }

    return this.paymentInstrumentRepository.save(toCreatePaymentInstrument);
  }

  async updateNewPaymentInstrument(
    paymentInstrumentId: string,
    content: UpdatePaymentInstrumentBody,
    user: User,
  ): Promise<PaymentInstrument> {
    const paymentInstrument = await this.paymentInstrumentRepository.findOne({
      where: { id: paymentInstrumentId, user: { id: user.id } },
    });

    if (!paymentInstrument) {
      throw errorBuilder.payment.instrument.notFound(paymentInstrumentId);
    }

    const cardData = await this.createPaymentInstrument(
      paymentInstrument,
      {
        expirationYear: content.expirationYear,
        expirationMonth: content.expirationMonth,
        cardHolderName: content.cardHolderName,
        securityCode: content.securityCode,
      },
      user,
    );

    const { token, state } = cardData;

    paymentInstrument.token = token;
    paymentInstrument.state = state;
    paymentInstrument.expirationYear = content.expirationYear;
    paymentInstrument.expirationMonth = content.expirationMonth;
    paymentInstrument.cardHolderName = content.cardHolderName;
    paymentInstrument.isPayerAuthEnroled = null;
    paymentInstrument.isPreferred = false;
    paymentInstrument.verificationTransactionId = null;
    paymentInstrument.verifiedAt = null;

    paymentInstrument.updateExpirationDate();

    try {
      const krakenResponse = await this.krakenService.updateCard(paymentInstrument.tokenKraken, {
        cardHolderName: content.cardHolderName,
        expirationYear: content.expirationYear,
        expirationMonth: content.expirationMonth,
        cvv: content.securityCode,
      });

      paymentInstrument.tokenKraken = krakenResponse.token;
    } catch (error: any) {
      this.logger.error("Error creating card in Kraken", error);
    }

    return this.paymentInstrumentRepository.save(paymentInstrument);
  }

  async createPaymentInstrumentIdentifier(
    content: CreatePaymentInstrumentBody,
  ): Promise<CreatePaymentInstrumentIdentifierResponse> {
    const requestObj = {
      card: {
        number: content.cardNumber,
      },
    };

    const instance = new cybersourceRestApi.InstrumentIdentifierApi(this.config, this.apiClient);
    this.logger.info("[createPaymentInstrumentIdentifier] before promisify", {
      requestObj: requestObj,
      instance: instance,
    });
    return this.promisify<cybersourceRestApi.CreatePaymentInstrumentIdentifierResponse>(
      instance.postInstrumentIdentifier.bind(instance),
    )(requestObj, []).then((data) => ({
      instrumentIdentifier: data.id,
      state: data.state === "ACTIVE" ? PaymentInstrumentState.ACTIVE : PaymentInstrumentState.INACTIVE,
      raw: data,
    }));
  }

  async createPaymentInstrument(
    paymentInstrument: { cardType: PaymentInstrumentType; instrumentIdentifier: string },
    content: CreatePaymentInstrumentBody | UpdatePaymentInstrumentBody,
    user: User,
  ): Promise<CreatePaymentInstrumentResponse> {
    const requestObj = new cybersourceRestApi.PostPaymentInstrumentRequest();

    const card = new cybersourceRestApi.Tmsv2customersEmbeddedDefaultPaymentInstrumentCard();
    card.expirationMonth = content.expirationMonth;
    card.expirationYear = content.expirationYear;
    card.type = paymentInstrument.cardType.toLowerCase();
    requestObj.card = card;

    const billTo = new cybersourceRestApi.Tmsv2customersEmbeddedDefaultPaymentInstrumentBillTo();
    billTo.firstName = content.cardHolderName;
    billTo.lastName = content.cardHolderName;
    billTo.company = "Vis Mobility";
    billTo.address1 = "Room 2006, 1 Sunning Road";
    billTo.locality = "Causeway Bay";
    billTo.country = "HK";
    billTo.email = user.email ?? "<EMAIL>";
    billTo.phoneNumber = user.phoneNumber;
    requestObj.billTo = billTo;

    const instrumentIdentifier =
      new cybersourceRestApi.Tmsv2customersEmbeddedDefaultPaymentInstrumentInstrumentIdentifier();
    instrumentIdentifier.id = paymentInstrument.instrumentIdentifier;
    requestObj.instrumentIdentifier = instrumentIdentifier;

    const instance = new cybersourceRestApi.PaymentInstrumentApi(this.config, this.apiClient);
    this.logger.info("[createPaymentInstrument] before promisify", {
      requestObj: requestObj,
      instance: instance,
    });
    return this.promisify<cybersourceRestApi.CreatePaymentInstrumentResponse>(
      instance.postPaymentInstrument.bind(instance),
    )(requestObj, []).then((data) => ({
      token: data.id,
      state: data.state === "ACTIVE" ? PaymentInstrumentState.ACTIVE : PaymentInstrumentState.INACTIVE,
      raw: data,
    }));
  }

  async initiate3DSecure(paymentInstrument: PaymentInstrument): Promise<Initiate3DSecureResponse> {
    const requestObj = new cybersourceRestApi.PayerAuthSetupRequest();

    const clientReferenceInformation = new cybersourceRestApi.Riskv1decisionsClientReferenceInformation();
    clientReferenceInformation.code = this.configService.getOrThrow("GLOBAL_PAYMENT_CLIENT_REFERENCE_CODE");
    requestObj.clientReferenceInformation = clientReferenceInformation;

    const paymentInformation = new cybersourceRestApi.Riskv1authenticationsetupsPaymentInformation();

    const customer = new cybersourceRestApi.Riskv1authenticationsetupsPaymentInformationCustomer();
    customer.customerId = paymentInstrument.token;
    paymentInformation.customer = customer;

    requestObj.paymentInformation = paymentInformation;

    const instance = new cybersourceRestApi.PayerAuthenticationApi(this.config, this.apiClient);
    this.logger.info("[initiate3DSecure] before promisify", {
      requestObj: requestObj,
      instance: instance,
    });
    return this.promisify<cybersourceRestApi.PayerAuthSetupResponse>(instance.payerAuthSetup.bind(instance))(
      requestObj,
    ).then((data) => ({
      accessToken: data.consumerAuthenticationInformation.accessToken,
      referenceId: data.consumerAuthenticationInformation.referenceId,
      deviceDataCollectionUrl: data.consumerAuthenticationInformation.deviceDataCollectionUrl,
      status: data.status,
    }));
  }

  async capturePayment(authPaymentTx: PaymentTx, total: number, requestedBy?: string): Promise<PaymentTx> {
    const capturePaymentTx = this.paymentTxRepository.create({
      requestedBy: requestedBy,
      id: randomUUID(),
      gatewayResponse: {},
      tx: authPaymentTx.tx,
      amount: total,
      gatewayTransactionId: "",
      cardNumber: authPaymentTx.cardNumber,
      paymentMethod: authPaymentTx.paymentMethod,
      gateway: PaymentGatewayTypes.GLOBAL_PAYMENTS,
      createdAt: new Date(),
      status: PaymentInformationStatus.PENDING,
      type: PaymentInformationType.CAPTURE,
      paymentInstrument: authPaymentTx.paymentInstrument,
      parent: authPaymentTx,
    });

    const requestObj = new cybersourceRestApi.CapturePaymentRequest();

    const clientReferenceInformation = new cybersourceRestApi.Ptsv2paymentsClientReferenceInformation();
    clientReferenceInformation.code = capturePaymentTx.id;
    requestObj.clientReferenceInformation = clientReferenceInformation;

    const orderInformation = new cybersourceRestApi.Ptsv2paymentsidcapturesOrderInformation();
    const orderInformationAmountDetails = new cybersourceRestApi.Ptsv2paymentsidcapturesOrderInformationAmountDetails();
    orderInformationAmountDetails.totalAmount = total;
    orderInformationAmountDetails.currency = "HKD";
    orderInformation.amountDetails = orderInformationAmountDetails;

    requestObj.orderInformation = orderInformation;

    const instance = new cybersourceRestApi.CaptureApi(this.config, this.apiClient);

    capturePaymentTx.gatewayResponse = { requestObj } as unknown as PaymentGatewayResponse;
    await this.paymentTxRepository.save(capturePaymentTx);
    this.logger.info("[capturePayment] before promisify", {
      requestObj: requestObj,
      instance: instance,
    });
    return this.promisify<cybersourceRestApi.AuthResponse>(instance.capturePayment.bind(instance), capturePaymentTx)(
      requestObj,
      authPaymentTx.gatewayTransactionId,
    )
      .then((data) => {
        capturePaymentTx.gatewayResponse = { ...data, requestObj } as unknown as PaymentGatewayResponse;
        capturePaymentTx.gatewayTransactionId = data.id;
        capturePaymentTx.createdAt = new Date(data.submitTimeUtc);
        capturePaymentTx.status =
          data.status === "PENDING" ? PaymentInformationStatus.SUCCESS : PaymentInformationStatus.FAILURE;
        return this.paymentTxRepository.save(capturePaymentTx);
      })
      .catch((error) => {
        this.logger.error("Error capturing payment", error);
        capturePaymentTx.gatewayResponse = { ...error, requestObj };
        capturePaymentTx.gatewayTransactionId = "";
        capturePaymentTx.status = PaymentInformationStatus.FAILURE;
        return this.paymentTxRepository.save(capturePaymentTx);
      });
  }

  async createPayment(
    tx: Tx,
    paymentInstrument: PaymentInstrument,
    isSale = false,
    overwriteAmount?: number,
    requestedBy?: string,
  ): Promise<PaymentTx> {
    this.logger.info("[createPayment] start", { txId: tx.id });

    const isAfterMigratingPaymentInstrumentToKraken = await this.getMigratingPaymentToKraken();
    if (isAfterMigratingPaymentInstrumentToKraken) {
      this.logger.info("[createPayment] migrating payment to kraken", { txId: tx.id });
      return isSale
        ? this.krakenService.processSale(tx, paymentInstrument, overwriteAmount, requestedBy)
        : this.krakenService.processAuth(tx, paymentInstrument, overwriteAmount, requestedBy);
    }
    const paymentTx = this.paymentTxRepository.create({
      id: randomUUID(),
      tx: tx,
      amount: overwriteAmount ?? tx.total ?? 0,
      gatewayTransactionId: "",
      gatewayResponse: {},
      cardNumber: paymentInstrument.cardNumber,
      paymentMethod: paymentInstrument.cardType as unknown as PaymentTxPaymentMethod,
      gateway: PaymentGatewayTypes.GLOBAL_PAYMENTS,
      createdAt: new Date().toISOString(),
      status: PaymentInformationStatus.PENDING,
      type: isSale ? PaymentInformationType.SALE : PaymentInformationType.AUTH,
      paymentInstrument: paymentInstrument,
      requestedBy,
    });
    const requestObj = new cybersourceRestApi.CreatePaymentRequest();

    const clientReferenceInformation = new cybersourceRestApi.Ptsv2paymentsClientReferenceInformation();
    clientReferenceInformation.code = paymentTx.id;
    requestObj.clientReferenceInformation = clientReferenceInformation;

    const processingInformation = new cybersourceRestApi.Ptsv2paymentsProcessingInformation();

    processingInformation.capture = isSale;
    requestObj.processingInformation = processingInformation;

    const paymentInformation = new cybersourceRestApi.Ptsv2paymentsPaymentInformation();

    const payment = new cybersourceRestApi.Ptsv2paymentsPaymentInformationPaymentInstrument();
    payment.id = paymentInstrument.token;
    paymentInformation.paymentInstrument = payment;

    requestObj.paymentInformation = paymentInformation;

    const orderInformation = new cybersourceRestApi.Ptsv2paymentsOrderInformation();
    const orderInformationAmountDetails = new cybersourceRestApi.Ptsv2paymentsOrderInformationAmountDetails();
    orderInformationAmountDetails.totalAmount = overwriteAmount?.toString() ?? (tx.total ?? 0).toString();
    orderInformationAmountDetails.currency = "HKD";
    orderInformation.amountDetails = orderInformationAmountDetails;

    requestObj.orderInformation = orderInformation;

    if (!isSale) {
      requestObj.merchantInformation = this.createMerchantInformation();
    }

    const instance = new cybersourceRestApi.PaymentsApi(this.config, this.apiClient);
    this.logger.info("[createPayment] before promisify", { requestObj: requestObj, instance: instance });

    paymentTx.gatewayResponse = { requestObj } as unknown as PaymentGatewayResponse;
    await this.paymentTxRepository.save(paymentTx);

    return this.promisify<cybersourceRestApi.AuthResponse>(
      instance.createPayment.bind(instance),
      paymentTx,
    )(requestObj)
      .then(async (data) => {
        this.logger.info("Payment created successfully", data);
        paymentTx.gatewayResponse = { ...data, requestObj } as unknown as PaymentGatewayResponse;
        paymentTx.gatewayTransactionId = data.id;
        paymentTx.status =
          data.status === "AUTHORIZED" ? PaymentInformationStatus.SUCCESS : PaymentInformationStatus.FAILURE;
        this.logger.info("[createPayment] done", { txId: tx.id });
        this.logger.info("PaymentTx created successfully", paymentTx);
        await this.paymentTxRepository.save(paymentTx);
        return paymentTx;
      })
      .catch(async (error) => {
        this.logger.error("Error creating payment", error);
        paymentTx.gatewayResponse = {
          error: buildMessage(error),
          requestObj,
        } as unknown as PaymentGatewayResponse;
        paymentTx.gatewayTransactionId = "";
        paymentTx.status = PaymentInformationStatus.FAILURE;
        this.logger.info("[createPayment] failed", { txId: tx.id });
        this.logger.info("Error PaymentTx", paymentTx);
        await this.paymentTxRepository.save(paymentTx);
        return paymentTx;
      });
  }

  async createPaymentWith3DS(
    tx: Tx,
    paymentInstrument: PaymentInstrument,
    threeDSecureOptions: ThreeDSecureOptions,
    isSale = false,
  ): Promise<CreatePaymentResponse> {
    const requestObj = new cybersourceRestApi.CreatePaymentRequest();

    const clientReferenceInformation = new cybersourceRestApi.Ptsv2paymentsClientReferenceInformation();
    clientReferenceInformation.code = this.configService.getOrThrow("GLOBAL_PAYMENT_CLIENT_REFERENCE_CODE");
    requestObj.clientReferenceInformation = clientReferenceInformation;

    const processingInformation = new cybersourceRestApi.Ptsv2paymentsProcessingInformation();

    const actionList = [];
    actionList.push("CONSUMER_AUTHENTICATION");
    processingInformation.actionList = actionList;

    processingInformation.capture = isSale;
    requestObj.processingInformation = processingInformation;

    const paymentInformation = new cybersourceRestApi.Ptsv2paymentsPaymentInformation();

    const payment = new cybersourceRestApi.Ptsv2paymentsPaymentInformationPaymentInstrument();
    payment.id = paymentInstrument.token;
    paymentInformation.paymentInstrument = payment;

    requestObj.paymentInformation = paymentInformation;

    const orderInformation = new cybersourceRestApi.Ptsv2paymentsOrderInformation();
    const orderInformationAmountDetails = new cybersourceRestApi.Ptsv2paymentsOrderInformationAmountDetails();
    orderInformationAmountDetails.totalAmount = (tx.total ?? 0).toString();
    orderInformationAmountDetails.currency = "HKD";
    orderInformation.amountDetails = orderInformationAmountDetails;

    requestObj.orderInformation = orderInformation;

    const deviceInformation = new cybersourceRestApi.Ptsv2paymentsDeviceInformation();
    deviceInformation.ipAddress = threeDSecureOptions.ipAddress;
    deviceInformation.httpBrowserScreenHeight = threeDSecureOptions.httpBrowserScreenHeight;
    deviceInformation.httpBrowserScreenWidth = threeDSecureOptions.httpBrowserScreenWidth;

    requestObj.deviceInformation = deviceInformation;

    const consumerAuthenticationInformation = new cybersourceRestApi.Ptsv2paymentsConsumerAuthenticationInformation();
    consumerAuthenticationInformation.referenceId = threeDSecureOptions.sessionId;
    consumerAuthenticationInformation.messageCategory = "02";
    consumerAuthenticationInformation.returnUrl = `${this.configService.getOrThrow(
      "GLOBAL_PAYMENT_BACKEND_URL",
    )}/global-payments/3d-secure/notification`;
    requestObj.consumerAuthenticationInformation = consumerAuthenticationInformation;

    if (!isSale) {
      requestObj.merchantInformation = this.createMerchantInformation();
    }

    const instance = new cybersourceRestApi.PaymentsApi(this.config, this.apiClient);
    this.logger.info("[createPaymentWith3DS] before promisify", { requestObj: requestObj, instance: instance });
    return this.promisify<cybersourceRestApi.ThreeDSResponse>(instance.createPayment.bind(instance))(requestObj).then(
      (data) => {
        if (!["PENDING_AUTHENTICATION", "AUTHORIZED"].includes(data.status)) {
          throw errorBuilder.payment.instrument.threeDSecure.notCompatible(paymentInstrument.id, data.status);
        }
        return {
          id: data.id,
          accessToken: data.consumerAuthenticationInformation.accessToken,
          stepUpUrl: data.consumerAuthenticationInformation.stepUpUrl,
          status:
            data.status === "AUTHORIZED"
              ? PaymentInstrumentState.VERIFIED
              : PaymentInstrumentState.VERIFICATION_PENDING,
          raw: data,
        };
      },
    );
  }

  async validateAuthenticationResults(
    paymentInstrument: PaymentInstrument,
  ): Promise<ValidateAuthenticationResultResponse> {
    if (!paymentInstrument.verificationTransactionId) {
      throw errorBuilder.global.requiredParam("paymentInstrument.verificationTransactionId");
    }

    const requestObj = new cybersourceRestApi.ValidateRequest();

    const clientReferenceInformation = new cybersourceRestApi.Riskv1decisionsClientReferenceInformation();
    clientReferenceInformation.code = this.configService.getOrThrow("GLOBAL_PAYMENT_CLIENT_REFERENCE_CODE");
    requestObj.clientReferenceInformation = clientReferenceInformation;

    const paymentInformation = new cybersourceRestApi.Riskv1authenticationresultsPaymentInformation();
    const payment = new cybersourceRestApi.Ptsv2paymentsPaymentInformationPaymentInstrument();
    payment.id = paymentInstrument.token;
    paymentInformation.paymentInstrument = payment;
    requestObj.paymentInformation = paymentInformation;

    const consumerAuthenticationInformation =
      new cybersourceRestApi.Riskv1authenticationresultsConsumerAuthenticationInformation();
    consumerAuthenticationInformation.authenticationTransactionId = paymentInstrument.verificationTransactionId;
    requestObj.consumerAuthenticationInformation = consumerAuthenticationInformation;

    const instance = new cybersourceRestApi.PayerAuthenticationApi(this.config, this.apiClient);
    this.logger.info("[validateAuthenticationResults] before promisify", {
      requestObj: requestObj,
      instance: instance,
    });

    return this.promisify<cybersourceRestApi.ValidatePayerAuthenticationResponse>(
      instance.validateAuthenticationResults.bind(instance),
    )(requestObj).then((data) => {
      return {
        status: payerAuthenticationStatusMapping[data.status] ?? PaymentInstrumentState.VERIFICATION_FAILED,
        raw: data,
      };
    });
  }

  async createAuthWithPayerAuthValidation(tx: Tx, paymentInstrument: PaymentInstrument): Promise<PaymentTx> {
    if (!paymentInstrument.verificationTransactionId) {
      throw errorBuilder.global.requiredParam("paymentInstrument.verificationTransactionId");
    }

    const requestObj = new cybersourceRestApi.CreatePaymentRequest();

    const clientReferenceInformation = new cybersourceRestApi.Riskv1decisionsClientReferenceInformation();
    clientReferenceInformation.code = this.configService.getOrThrow("GLOBAL_PAYMENT_CLIENT_REFERENCE_CODE");
    requestObj.clientReferenceInformation = clientReferenceInformation;

    const processingInformation = new cybersourceRestApi.Ptsv2paymentsProcessingInformation();

    const actionList = [];
    actionList.push("VALIDATE_CONSUMER_AUTHENTICATION");
    processingInformation.actionList = actionList;

    processingInformation.capture = false;
    requestObj.processingInformation = processingInformation;

    const paymentInformation = new cybersourceRestApi.Ptsv2paymentsPaymentInformation();
    const payment = new cybersourceRestApi.Ptsv2paymentsPaymentInformationPaymentInstrument();
    payment.id = paymentInstrument.token;
    paymentInformation.paymentInstrument = payment;
    requestObj.paymentInformation = paymentInformation;

    const orderInformation = new cybersourceRestApi.Ptsv2paymentsOrderInformation();
    const orderInformationAmountDetails = new cybersourceRestApi.Ptsv2paymentsOrderInformationAmountDetails();
    orderInformationAmountDetails.totalAmount = (tx.total ?? 0).toString();
    orderInformationAmountDetails.currency = "HKD";
    orderInformation.amountDetails = orderInformationAmountDetails;

    requestObj.orderInformation = orderInformation;

    const consumerAuthenticationInformation = new cybersourceRestApi.Ptsv2paymentsConsumerAuthenticationInformation();
    consumerAuthenticationInformation.authenticationTransactionId = paymentInstrument.verificationTransactionId;
    requestObj.consumerAuthenticationInformation = consumerAuthenticationInformation;

    requestObj.merchantInformation = this.createMerchantInformation();

    const instance = new cybersourceRestApi.PaymentsApi(this.config, this.apiClient);
    this.logger.info("[createAuthWithPayerAuthValidation] before promisify", {
      requestObj: requestObj,
      instance: instance,
    });
    return this.promisify<cybersourceRestApi.AuthResponse>(instance.createPayment.bind(instance))(requestObj).then(
      (data) => {
        const paymentTx = PaymentTx.fromJson(
          {
            id: randomUUID(),
            gatewayResponse: data,
            tx,
            amount: data.orderInformation?.amountDetails?.authorizedAmount ?? (tx.total ?? 0).toString(),
            gatewayTransactionId: data.id,
            cardNumber: paymentInstrument.cardNumber,
            paymentMethod: data?.paymentAccountInformation?.card?.type
              ? GlobalPaymentCardTypeMap[
                  data.paymentAccountInformation.card.type as keyof typeof GlobalPaymentCardTypeMap
                ]
              : paymentInstrument.cardType,
            gateway: PaymentGatewayTypes.GLOBAL_PAYMENTS,
            createdAt: data.submitTimeUtc ?? new Date().toISOString(),
            status: data.status === "AUTHORIZED" ? PaymentInformationStatus.SUCCESS : PaymentInformationStatus.FAILURE,
            type: PaymentInformationType.AUTH,
            paymentInstrument,
          },
          tx.id,
        );
        return paymentTx;
      },
    );
  }

  async checkPayerAuthEnrollment(paymentInstrument: PaymentInstrument): Promise<CheckPayerAuthEnrollmentResponse> {
    const requestObj = new cybersourceRestApi.CheckPayerAuthEnrollmentRequest();

    const clientReferenceInformation = new cybersourceRestApi.Riskv1decisionsClientReferenceInformation();
    clientReferenceInformation.code = this.configService.getOrThrow("GLOBAL_PAYMENT_CLIENT_REFERENCE_CODE");
    requestObj.clientReferenceInformation = clientReferenceInformation;

    const orderInformation = new cybersourceRestApi.Riskv1authenticationsOrderInformation();
    const orderInformationAmountDetails = new cybersourceRestApi.Riskv1authenticationsOrderInformationAmountDetails();
    orderInformationAmountDetails.currency = "HKD";
    orderInformationAmountDetails.totalAmount = "0";
    orderInformation.amountDetails = orderInformationAmountDetails;

    requestObj.orderInformation = orderInformation;

    const paymentInformation = new cybersourceRestApi.Riskv1authenticationsPaymentInformation();
    const customer = new cybersourceRestApi.Ptsv2paymentsPaymentInformationCustomer();
    customer.customerId = paymentInstrument.token;
    paymentInformation.customer = customer;
    requestObj.paymentInformation = paymentInformation;

    const instance = new cybersourceRestApi.PayerAuthenticationApi(this.config, this.apiClient);
    this.logger.info("[checkPayerAuthEnrollment] before promisify", {
      requestObj: requestObj,
      instance: instance,
    });
    return this.promisify<cybersourceRestApi.CheckPayerAuthEnrollmentResponse>(
      instance.checkPayerAuthEnrollment.bind(instance),
    )(requestObj).then((data) => {
      return {
        isPayerAuthEnroled: data.status === "AUTHENTICATION_SUCCESSFUL",
        raw: data,
      };
    });
  }

  async voidPayment(paymentTx: PaymentTx, requestedBy?: string): Promise<PaymentTx> {
    const voidPaymentTx = this.paymentTxRepository.create({
      ...paymentTx,
      id: randomUUID(),
      gatewayResponse: {},
      gatewayTransactionId: "",
      createdAt: new Date(),
      status: PaymentInformationStatus.PENDING,
      type: PaymentInformationType.VOID,
      tx: paymentTx.tx,
      parent: paymentTx,
      requestedBy,
    });

    const requestObj = new cybersourceRestApi.VoidPaymentRequest();

    const clientReferenceInformation = new cybersourceRestApi.Ptsv2paymentsClientReferenceInformation();
    clientReferenceInformation.code = voidPaymentTx.id;
    requestObj.clientReferenceInformation = clientReferenceInformation;

    const instance = new cybersourceRestApi.VoidApi(this.config, this.apiClient);

    voidPaymentTx.gatewayResponse = { requestObj } as unknown as PaymentGatewayResponse;
    await this.paymentTxRepository.save(voidPaymentTx);
    this.logger.info("[voidPayment] before promisify", {
      requestObj: requestObj,
      instance: instance,
    });
    return this.promisify<cybersourceRestApi.CheckPayerAuthEnrollmentResponse>(
      instance.voidPayment.bind(instance),
      voidPaymentTx,
    )(requestObj, paymentTx.gatewayTransactionId)
      .then((data) => {
        voidPaymentTx.gatewayResponse = { ...data, requestObj } as unknown as PaymentGatewayResponse;
        voidPaymentTx.gatewayTransactionId = data.id;
        voidPaymentTx.createdAt = new Date(data.submitTimeUtc);
        voidPaymentTx.status = ["REVERSED", "VOIDED"].includes(data.status)
          ? PaymentInformationStatus.SUCCESS
          : PaymentInformationStatus.FAILURE;
        return this.paymentTxRepository.save(voidPaymentTx);
      })
      .catch((error) => {
        this.logger.error("Error voiding payment", error);
        voidPaymentTx.gatewayResponse = { ...error, requestObj };
        voidPaymentTx.gatewayTransactionId = "";
        voidPaymentTx.status = PaymentInformationStatus.FAILURE;
        return this.paymentTxRepository.save(voidPaymentTx);
      });
  }

  async doEnquiry(paymentTx: PaymentTx, requestedBy?: string): Promise<PaymentTx> {
    this.logger.info("[GP] doEnquiry", { paymentTxId: paymentTx.id });
    const enquiryPaymentTx = this.paymentTxRepository.create({
      ...paymentTx,
      id: randomUUID(),
      gatewayResponse: {},
      gatewayTransactionId: "",
      createdAt: new Date(),
      status: PaymentInformationStatus.PENDING,
      type: PaymentInformationType.ENQUIRY,
      tx: paymentTx.tx,
      parent: paymentTx,
      requestedBy,
    });
    this.logger.info("[GP] doEnquiry", { enquiryPaymentTxId: enquiryPaymentTx.id });

    const instance = new cybersourceRestApi.TransactionDetailsApi(this.config, this.apiClient);

    this.logger.info("[GP] doEnquiry calling getTransaction");
    this.logger.info("[doEnquiry] before promisify", {
      requestObj: paymentTx.gatewayTransactionId,
      instance: instance,
    });
    return this.promisify<cybersourceRestApi.GetTransactionResponse>(
      instance.getTransaction.bind(instance),
      enquiryPaymentTx,
    )(paymentTx.gatewayTransactionId)
      .then((data) => {
        this.logger.info("[GP] doEnquiry getTransaction success", { data });
        enquiryPaymentTx.status =
          data.applicationInformation.reasonCode === "100"
            ? PaymentInformationStatus.SUCCESS
            : PaymentInformationStatus.FAILURE;

        enquiryPaymentTx.gatewayResponse = data;
        enquiryPaymentTx.gatewayTransactionId = data.id;
        enquiryPaymentTx.createdAt = new Date(data.submitTimeUTC);
        return this.paymentTxRepository.save(enquiryPaymentTx);
      })
      .catch((error) => {
        this.logger.info("[GP] doEnquiry getTransaction failed", { error });
        enquiryPaymentTx.gatewayResponse = error;
        enquiryPaymentTx.gatewayTransactionId = "";
        enquiryPaymentTx.status = PaymentInformationStatus.FAILURE;
        return this.paymentTxRepository.save(enquiryPaymentTx);
      });
  }

  async searchPayment(paymentTx: PaymentTx, requestedBy?: string): Promise<PaymentTx> {
    this.logger.info("[GP] searchPayment", { paymentTxId: paymentTx.id });
    const requestObj = {
      name: "codeSearch",
      query: `clientReferenceInformation.code:${paymentTx.id}`,
    };

    const searchPaymentTx = this.paymentTxRepository.create({
      ...paymentTx,
      id: randomUUID(),
      gatewayResponse: { requestObj },
      gatewayTransactionId: "",
      createdAt: new Date(),
      status: PaymentInformationStatus.PENDING,
      type: PaymentInformationType.SEARCH,
      tx: paymentTx.tx,
      parent: paymentTx,
      requestedBy,
    });
    this.logger.info("[GP] searchPayment", { enquiryPaymentTxId: searchPaymentTx.id });

    const instance = new cybersourceRestApi.SearchTransactionsApi(this.config, this.apiClient);

    this.logger.info("[GP] searchPayment calling getTransaction");
    this.logger.info("[searchPayment] before promisify", {
      requestObj: requestObj,
      instance: instance,
    });
    return this.promisify<cybersourceRestApi.SearchResponse>(
      instance.createSearch.bind(instance),
      searchPaymentTx,
    )(requestObj)
      .then((data) => {
        this.logger.info("[GP] searchPayment getTransaction success", { data });
        searchPaymentTx.gatewayResponse = { ...data, requestObj } as unknown as PaymentGatewayResponse;
        searchPaymentTx.gatewayTransactionId = data.searchId;
        searchPaymentTx.status = PaymentInformationStatus.SUCCESS;
        return this.paymentTxRepository.save(searchPaymentTx);
      })
      .catch((error) => {
        this.logger.info("[GP] searchPayment getTransaction failed", { error });
        searchPaymentTx.gatewayResponse = error;
        searchPaymentTx.gatewayTransactionId = "";
        searchPaymentTx.status = PaymentInformationStatus.FAILURE;
        return this.paymentTxRepository.save(searchPaymentTx);
      });
  }

  createMerchantInformation(): cybersourceRestApi.Ptsv2paymentsMerchantInformation {
    const merchantDescriptor = new cybersourceRestApi.Ptsv2paymentsMerchantInformationMerchantDescriptor();
    merchantDescriptor.name = PAYMENT_MERCHANT_INFORMATION_MERCHANT_DESCRIPTOR_AUTH;
    const merchantInformation = new cybersourceRestApi.Ptsv2paymentsMerchantInformation();
    merchantInformation.merchantDescriptor = merchantDescriptor;
    return merchantInformation;
  }
}
