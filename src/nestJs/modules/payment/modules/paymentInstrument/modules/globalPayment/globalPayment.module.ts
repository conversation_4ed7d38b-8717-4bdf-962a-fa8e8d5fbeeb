import { Module, forwardRef } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";

import { AppDatabaseModule } from "@nest/modules/appDatabase/appDatabase.module";
import { PaymentInstrumentRepository } from "@nest/modules/database/repositories/paymentInstument.repository";

import { PaymentTxRepository } from "../../../../../database/repositories/paymentTx.repository";
import { PaymentInstrumentModule } from "../../paymentInstrument.module";
import { KrakenModule } from "../kraken/kraken.module";

import { GlobalPaymentService } from "./globalPayment.service";
import { GlobalPaymentController } from "./globalPayment.controller";

/**
 * GlobalPayment module
 */
@Module({
  providers: [GlobalPaymentService, GlobalPaymentController, PaymentTxRepository, PaymentInstrumentRepository],
  controllers: [GlobalPaymentController],
  imports: [
    ConfigModule,
    forwardRef(() => PaymentInstrumentModule),
    KrakenModule,
    AppDatabaseModule,
  ],
  exports: [GlobalPaymentService, GlobalPaymentController],
})
export class GlobalPaymentModule {}
