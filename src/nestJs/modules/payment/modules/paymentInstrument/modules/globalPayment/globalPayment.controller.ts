import { Body, Controller, Inject, Post } from "@nestjs/common";
import { ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { getAuth } from "firebase-admin/auth";

import LoggerServiceAdapter from "../../../../../utils/logger/logger.service";
import { apiTags } from "../../../../../utils/utils/swagger.utils";
import { PaymentInstrumentService } from "../../paymentInstrument.service";

import { GlobalPaymentNotificationBody } from "./dto/globalPayment.dto";

const iframeResponse = (data: Record<string, any>) => {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <title>Dash</title>
</head>
<body>
  <script>
    window.top.postMessage(${JSON.stringify(data)}, '*');
  </script>
</body>
</html>`;
};

@Controller("global-payments")
@ApiTags(...apiTags.public)
export class GlobalPaymentController {
  constructor(
    private readonly paymentInstrumentService: PaymentInstrumentService,
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
  ) {}

  @Post("3d-secure/notification")
  @ApiOperation({ summary: "receive notification" })
  @ApiResponse({ status: 201, description: "receive notification" })
  async GlobalPaymentNotification(@Body() body: GlobalPaymentNotificationBody): Promise<string> {
    let paymentInstrumentId;
    let authorization;

    try {
      const data = JSON.parse(body.MD);
      paymentInstrumentId = data.paymentInstrumentId;
      authorization = data.authorization;
    } catch (err: any) {
      this.logger.error(err);
      return iframeResponse({
        type: "error",
        message: err.message,
      });
    }
    let user;

    try {
      user = await getAuth().verifyIdToken(authorization);
    } catch (err: any) {
      this.logger.error(err);
      return iframeResponse({
        type: "error",
        message: err.message,
      });
    }

    let response;

    try {
      const paymentInstrument = await this.paymentInstrumentService.receiveTransaction(
        paymentInstrumentId,
        body.TransactionId,
        user.user_id,
      );
      response = {
        type: "Finalize3DSecure",
        paymentInstrument: paymentInstrument.toJson(),
      };
    } catch (err: any) {
      this.logger.error(err);
      return iframeResponse({
        type: "error",
        message: err.message,
      });
    }

    return iframeResponse(response);
  }
}
