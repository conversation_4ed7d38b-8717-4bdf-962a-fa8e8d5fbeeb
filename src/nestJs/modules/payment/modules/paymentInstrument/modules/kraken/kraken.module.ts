import { Module, forwardRef } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";

import { PaymentInstrumentRepository } from "@nest/modules/database/repositories/paymentInstument.repository";

import { PaymentTxRepository } from "../../../../../database/repositories/paymentTx.repository";
import { PaymentInstrumentModule } from "../../paymentInstrument.module";

import { KrakenService } from "./kraken.service";
import { KrakenApi } from "./kraken.api";

/**
 * Kraken module
 */
@Module({
  providers: [KrakenService, KrakenApi, PaymentTxRepository, PaymentInstrumentRepository],
  imports: [ConfigModule, forwardRef(() => PaymentInstrumentModule)],
  exports: [KrakenService],
})
export class KrakenModule {}
