import { PaymentInformationStatus } from "../../../../../../payment/dto/paymentInformationStatus.dto";
import { PaymentInformationType } from "../../../../../../payment/dto/paymentInformationType.dto";

import { KrakenCardPublic } from "./card.dto";
import { KrakenMerchantPublic } from "./merchant.dto";

export type KrakenTransactionPublic = {
  id: string;
  amount: number;
  status: PaymentInformationStatus;
  type: PaymentInformationType;
  gatewayResponse: any;
  createdAt: Date;
  updatedAt: Date;
  parent?: KrakenTransactionPublic;
  requestedBy: string;
  merchant: KrakenMerchantPublic;
  card: KrakenCardPublic;
};
