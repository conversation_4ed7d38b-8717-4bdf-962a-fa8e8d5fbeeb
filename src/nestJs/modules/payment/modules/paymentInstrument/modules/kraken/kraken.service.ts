import { randomUUID } from "crypto";

import { Inject, Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { InjectRepository } from "@nestjs/typeorm";

import { PaymentInstrumentType } from "@nest/modules/campaign/dto/campaign.dto";
import { PaymentInstrumentRepository } from "@nest/modules/database/repositories/paymentInstument.repository";
import { PaymentTxPaymentMethod } from "@nest/modules/payment/dto/paymentTxPaymentMethod.dto";

import PaymentInstrument from "../../../../../database/entities/paymentInstrument.entity";
import PaymentTx from "../../../../../database/entities/paymentTx.entity";
import Tx from "../../../../../database/entities/tx.entity";
import User from "../../../../../database/entities/user.entity";
import { PaymentTxRepository } from "../../../../../database/repositories/paymentTx.repository";
import {
  CreatePaymentInstrumentBody,
  UpdatePaymentInstrumentBody,
} from "../../../../../me/modules/mePaymentInstrument/mePaymentInstrument.dto";
import { PaymentGatewayResponse, EnquiryResponse } from "../../../../../payment/dto/paymentGatewayResponses.model";
import PaymentTxFromDocument from "../../../../../payment/dto/paymentTxFromDocument.model";
import paymentFactoryInterface from "../../../../../payment/paymentFactory/paymentFactoryInterface";
import LoggerServiceAdapter from "../../../../../utils/logger/logger.service";
import { errorBuilder } from "../../../../../utils/utils/error.utils";
import { PaymentGatewayTypes } from "../../../../dto/paymentGatewayTypes.dto";
import { PaymentInformationStatus } from "../../../../dto/paymentInformationStatus.dto";
import { PaymentInformationType } from "../../../../dto/paymentInformationType.dto";
import GlobalPaymentDefault from "../../../_providers/globalPaymentDefault";
import {
  Initiate3DSecureResponse,
  CreatePaymentResponse,
  ValidateAuthenticationResultResponse,
  CheckPayerAuthEnrollmentResponse,
  PaymentInstrumentState,
} from "../../dto/paymentInstrument.dto";
import { payerAuthenticationStatusMapping, ThreeDSecureOptions } from "../globalPayment/dto/globalPayment.dto";

import { KrakenApi } from "./kraken.api";
import { UpdateCardBody } from "./dto/updateCard.dto";
import { SaveCardBody } from "./dto/saveCard.dto";

/**
 * GlobalPayment service
 */
@Injectable()
export class KrakenService extends GlobalPaymentDefault implements paymentFactoryInterface {
  constructor(
    readonly configService: ConfigService,
    @InjectRepository(PaymentTxRepository) private paymentTxRepository: PaymentTxRepository,
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
    readonly krakenApi: KrakenApi,
    @InjectRepository(PaymentInstrumentRepository) private paymentInstrumentRepository: PaymentInstrumentRepository,
  ) {
    super(configService);
  }

  catchHandler = (paymentTx: PaymentTx) => async (error: Error) => {
    this.logger.error("[Kraken] error caught", {}, error);
    paymentTx.gatewayResponse = error as unknown as PaymentGatewayResponse;
    paymentTx.gatewayTransactionId = "";
    paymentTx.status = PaymentInformationStatus.FAILURE;
    await this.paymentTxRepository.save(paymentTx);
    return paymentTx;
  };

  /**
   * Extract payment transaction information from document
   * @param document
   * @returns PaymentTxFromDocument
   */
  readonly extractPaymentTxInfoFromDocument = (
    document: PaymentGatewayResponse,
    options?: any,
  ): PaymentTxFromDocument => {
    this.logger.error(errorBuilder.factory.notImplemented("KrakenService/extractPaymentTxInfoFromDocument"));
    throw errorBuilder.factory.notImplemented("KrakenService/extractPaymentTxInfoFromDocument");
  };
  async processCapture(authPaymentTx: PaymentTx, total: number, requestedBy?: string): Promise<PaymentTx> {
    this.logger.info("[Kraken] processCapture", {
      id: authPaymentTx.id,
      total,
    });
    if (!authPaymentTx.gatewayTransactionId) {
      throw errorBuilder.payment.captureFailed(authPaymentTx.id, { message: "Gateway transaction id not found" });
    }

    const capturePaymentTx = this.paymentTxRepository.create({
      id: randomUUID(),
      tx: authPaymentTx.tx,
      amount: total,
      gatewayTransactionId: "",
      cardNumber: authPaymentTx.cardNumber,
      paymentMethod: authPaymentTx.paymentMethod,
      gateway: PaymentGatewayTypes.KRAKEN,
      createdAt: new Date().toISOString(),
      status: PaymentInformationStatus.PENDING,
      type: PaymentInformationType.CAPTURE,
      paymentInstrument: authPaymentTx.paymentInstrument,
      parent: authPaymentTx,
      requestedBy,
    });

    await this.paymentTxRepository.save(capturePaymentTx);

    return this.krakenApi
      .capture(authPaymentTx.gatewayTransactionId, {
        amount: total,
      })
      .then(async (data) => {
        capturePaymentTx.amount = data.amount;
        capturePaymentTx.status = data.status;
        capturePaymentTx.gatewayResponse = data;
        capturePaymentTx.gatewayTransactionId = data.id;

        await this.paymentTxRepository.save(capturePaymentTx);

        return capturePaymentTx;
      })
      .catch(this.catchHandler(capturePaymentTx));
  }

  processVoid(paymentTxs: PaymentTx[]): Promise<PaymentTx[]> {
    this.logger.info(
      "[Kraken] processVoid",
      paymentTxs.map((tx) => tx.id),
    );
    return Promise.all(paymentTxs.map((paymentTx) => this.voidPayment(paymentTx)));
  }

  async processRefund(paymentTx: PaymentTx, requestedBy?: string, customAmount?: number): Promise<PaymentTx> {
    this.logger.info("[Kraken] processRefund", { id: paymentTx.id, customAmount, requestedBy });
    const toRefundAmount = customAmount ?? paymentTx.amount ?? 0;
    if (toRefundAmount <= 0) {
      throw errorBuilder.payment.soePay.apiFailure(new Error("cannot refund 0, check database"));
    }
    if (customAmount && paymentTx.amount && customAmount > paymentTx.amount) {
      throw errorBuilder.payment.soePay.apiFailure(new Error("refund amount is greater than paymentTx amount"));
    }
    if (!paymentTx.gatewayTransactionId) {
      throw errorBuilder.payment.refundFailed(paymentTx.id, { message: "Gateway transaction id not found" });
    }

    const refundPaymentTx = this.paymentTxRepository.create({
      id: randomUUID(),
      tx: paymentTx.tx,
      amount: toRefundAmount,
      gatewayTransactionId: "",
      cardNumber: paymentTx.cardNumber,
      paymentMethod: paymentTx.paymentMethod,
      gateway: PaymentGatewayTypes.KRAKEN,
      createdAt: new Date().toISOString(),
      status: PaymentInformationStatus.PENDING,
      type: PaymentInformationType.REFUND,
      paymentInstrument: paymentTx.paymentInstrument,
      parent: paymentTx,
      requestedBy,
    });

    await this.paymentTxRepository.save(refundPaymentTx);
    this.logger.info("[Kraken] processRefund saved", { id: refundPaymentTx.id });

    return this.krakenApi
      .refund(paymentTx.gatewayTransactionId, {
        amount: toRefundAmount,
      })
      .then(async (data) => {
        this.logger.info("[Kraken] processRefund response", { data });
        refundPaymentTx.amount = data.amount;
        refundPaymentTx.status = data.status;
        refundPaymentTx.gatewayResponse = data;
        refundPaymentTx.gatewayTransactionId = data.id;

        await this.paymentTxRepository.save(refundPaymentTx);

        return refundPaymentTx;
      })
      .catch(this.catchHandler(refundPaymentTx));
  }

  async doEnquiry(paymentTx: PaymentTx, requestedBy?: string): Promise<EnquiryResponse> {
    this.logger.info("[Kraken] doEnquiry", { id: paymentTx.id });

    if (!paymentTx || !paymentTx.gatewayTransactionId) {
      throw errorBuilder.payment.enquiryFailed(paymentTx.id, { message: "Payment transaction not found" });
    }

    const enquirePaymentTx = this.paymentTxRepository.create({
      id: randomUUID(),
      tx: paymentTx.tx,
      amount: paymentTx.amount,
      gatewayTransactionId: "",
      cardNumber: paymentTx.cardNumber,
      paymentMethod: paymentTx.paymentMethod,
      gateway: PaymentGatewayTypes.KRAKEN,
      createdAt: new Date().toISOString(),
      status: PaymentInformationStatus.PENDING,
      type: PaymentInformationType.ENQUIRY,
      paymentInstrument: paymentTx.paymentInstrument,
      parent: paymentTx,
      requestedBy,
    });

    await this.paymentTxRepository.save(enquirePaymentTx);
    this.logger.info("[Kraken] doEnquiry saved", { id: enquirePaymentTx.id });

    return this.krakenApi
      .enquire(paymentTx.gatewayTransactionId)
      .then(async (data) => {
        this.logger.info("[Kraken] doEnquiry response", { data });
        enquirePaymentTx.gatewayTransactionId = data.id;
        enquirePaymentTx.status = data.status;

        enquirePaymentTx.gatewayResponse = data;

        await this.paymentTxRepository.save(enquirePaymentTx);

        return enquirePaymentTx;
      })
      .catch(this.catchHandler(enquirePaymentTx));
  }

  async searchPayment(paymentTx: PaymentTx, requestedBy?: string): Promise<EnquiryResponse> {
    this.logger.info("[Kraken] searchPayment", { id: paymentTx.id });

    if (!paymentTx || !paymentTx.gatewayTransactionId) {
      throw errorBuilder.payment.searchFailed(paymentTx.id, { message: "Payment transaction not found" });
    }

    const searchPaymentTx = this.paymentTxRepository.create({
      id: randomUUID(),
      tx: paymentTx.tx,
      amount: paymentTx.amount,
      gatewayTransactionId: "",
      cardNumber: paymentTx.cardNumber,
      paymentMethod: paymentTx.paymentMethod,
      gateway: PaymentGatewayTypes.KRAKEN,
      createdAt: new Date().toISOString(),
      status: PaymentInformationStatus.PENDING,
      type: PaymentInformationType.SEARCH,
      paymentInstrument: paymentTx.paymentInstrument,
      parent: paymentTx,
      requestedBy,
    });

    await this.paymentTxRepository.save(searchPaymentTx);
    this.logger.info("[Kraken] searchPayment", { id: searchPaymentTx.id });

    return this.krakenApi
      .search(paymentTx.gatewayTransactionId)
      .then(async (data) => {
        this.logger.info("[Kraken] searchPayment response", { data });
        searchPaymentTx.gatewayResponse = data;
        searchPaymentTx.gatewayTransactionId = data.id;
        searchPaymentTx.status = PaymentInformationStatus.SUCCESS;
        await this.paymentTxRepository.save(searchPaymentTx);
        return searchPaymentTx;
      })
      .catch(this.catchHandler(searchPaymentTx));
  }

  isStillProcessing(_paymentInfo: EnquiryResponse): boolean {
    this.logger.error(errorBuilder.factory.notImplemented("KrakenService/isStillProcessing"));
    throw errorBuilder.factory.notImplemented("KrakenService/isStillProcessing");
  }

  findAllSalesInProcessingStatus(_paymentTxs: PaymentTx[]): PaymentTx[] {
    this.logger.error(errorBuilder.factory.notImplemented("KrakenService/findAllSalesInProcessingStatus"));
    throw errorBuilder.factory.notImplemented("KrakenService/findAllSalesInProcessingStatus");
  }

  async processSale(
    tx: Tx,
    paymentInstrument: PaymentInstrument,
    overwriteAmount?: number,
    requestedBy?: string,
  ): Promise<PaymentTx> {
    this.logger.info("[Kraken] processSale", {
      id: tx.id,
      paymentInstrumentId: paymentInstrument.id,
    });

    const salePaymentTx = this.paymentTxRepository.create({
      id: randomUUID(),
      tx: tx,
      amount: overwriteAmount ?? tx.total ?? 0,
      gatewayTransactionId: "",
      cardNumber: paymentInstrument.cardNumber,
      paymentMethod: paymentInstrument.cardType as unknown as PaymentTxPaymentMethod,
      gateway: PaymentGatewayTypes.KRAKEN,
      createdAt: new Date().toISOString(),
      status: PaymentInformationStatus.PENDING,
      type: PaymentInformationType.SALE,
      paymentInstrument: paymentInstrument,
      requestedBy,
    });

    await this.paymentTxRepository.save(salePaymentTx);
    this.logger.info("[Kraken] processSale saved", { id: salePaymentTx.id });

    return this.krakenApi
      .sale({
        cardToken: paymentInstrument.tokenKraken,
        amount: salePaymentTx.amount ?? 0,
      })
      .then(async (data) => {
        this.logger.info("[Kraken] processSale response", { data });
        salePaymentTx.amount = data.amount;
        salePaymentTx.status = data.status;
        salePaymentTx.gatewayResponse = data;
        salePaymentTx.gatewayTransactionId = data.id;

        await this.paymentTxRepository.save(salePaymentTx);

        return salePaymentTx;
      })
      .catch(this.catchHandler(salePaymentTx));
  }
  async processAuth(
    tx: Tx,
    paymentInstrument: PaymentInstrument,
    overwriteAmount?: number,
    requestedBy?: string,
  ): Promise<PaymentTx> {
    this.logger.info("[Kraken] processAuth", {
      id: tx.id,
      paymentInstrumentId: paymentInstrument.id,
    });

    const authPaymentTx = this.paymentTxRepository.create({
      id: randomUUID(),
      tx: tx,
      amount: overwriteAmount ?? tx.total ?? 0,
      gatewayTransactionId: "",
      cardNumber: paymentInstrument.cardNumber,
      paymentMethod: paymentInstrument.cardType as unknown as PaymentTxPaymentMethod,
      gateway: PaymentGatewayTypes.KRAKEN,
      createdAt: new Date().toISOString(),
      status: PaymentInformationStatus.PENDING,
      type: PaymentInformationType.AUTH,
      paymentInstrument: paymentInstrument,
      requestedBy,
    });

    await this.paymentTxRepository.save(authPaymentTx);
    this.logger.info("[Kraken] processAuth saved", { id: authPaymentTx.id });

    return this.krakenApi
      .auth({
        cardToken: paymentInstrument.tokenKraken,
        amount: authPaymentTx.amount ?? 0,
      })
      .then(async (data) => {
        this.logger.info("[Kraken] processAuth response", { data });
        authPaymentTx.amount = data.amount;
        authPaymentTx.status = data.status;
        authPaymentTx.gatewayResponse = data;
        authPaymentTx.gatewayTransactionId = data.id;

        await this.paymentTxRepository.save(authPaymentTx);

        return authPaymentTx;
      })
      .catch(async (error) => {
        authPaymentTx.status = PaymentInformationStatus.FAILURE;
        authPaymentTx.gatewayResponse = error;

        await this.paymentTxRepository.save(authPaymentTx);

        return authPaymentTx;
      });
  }

  createPaymentInstrument(
    _paymentInstrument: { cardType: PaymentInstrumentType; instrumentIdentifier: string },
    _content: CreatePaymentInstrumentBody | UpdatePaymentInstrumentBody,
    _user: User,
  ): Promise<{ token: string; state: PaymentInstrumentState }> {
    this.logger.error(errorBuilder.factory.notImplemented("createPaymentInstrument"));
    throw errorBuilder.factory.notImplemented("createPaymentInstrument");
  }
  createPaymentInstrumentIdentifier(
    _content: CreatePaymentInstrumentBody,
  ): Promise<{ instrumentIdentifier: string; state: PaymentInstrumentState }> {
    this.logger.error(errorBuilder.factory.notImplemented("createPaymentInstrumentIdentifier"));
    throw errorBuilder.factory.notImplemented("createPaymentInstrumentIdentifier");
  }

  async deletePaymentInstrument(paymentInstrument: PaymentInstrument, user: User): Promise<PaymentInstrument> {
    this.logger.info("Processing delete payment instrument: ", { id: paymentInstrument.id });

    // Not deleting on Kraken side since multiple users can have the same card
    // and we don't want to delete the card for all users
    // await this.deleteCard(paymentInstrument.token);

    return paymentInstrument;
  }

  async createNewPaymentInstrument(content: CreatePaymentInstrumentBody, user: User): Promise<PaymentInstrument> {
    this.logger.info("[Kraken] createNewPaymentInstrument", { cardType: content.cardType });
    const cardData = await this.createCard({
      cardNumber: content.cardNumber,
      expirationYear: content.expirationYear,
      expirationMonth: content.expirationMonth,
      cardHolderName: content.cardHolderName,
      cvv: content.securityCode,
      type: content.cardType,
      userId: user.id,
    });

    const { token } = cardData;

    let toCreatePaymentInstrument = await this.paymentInstrumentRepository.findOne({
      where: { tokenKraken: token, user: { id: user.id } },
    });

    if (!toCreatePaymentInstrument) {
      this.logger.info("[Kraken] createNewPaymentInstrument create", { token });
      toCreatePaymentInstrument = this.paymentInstrumentRepository.create({
        id: randomUUID(),
        user,
        token: token,
        instrumentIdentifier: token,
        tokenKraken: token,
        expirationYear: content.expirationYear,
        expirationMonth: content.expirationMonth,
        cardHolderName: content.cardHolderName,
        cardType: content.cardType,
        cardPrefix: content.cardNumber.substring(0, 6),
        cardSuffix: content.cardNumber.substring(content.cardNumber.length - 4),
        state: PaymentInstrumentState.ACTIVE,
        paymentGateway: PaymentGatewayTypes.KRAKEN,
      });
      toCreatePaymentInstrument.updateExpirationDate();
    }

    return this.paymentInstrumentRepository.save(toCreatePaymentInstrument);
  }

  async updateNewPaymentInstrument(
    paymentInstrumentId: string,
    content: UpdatePaymentInstrumentBody,
    user: User,
  ): Promise<PaymentInstrument> {
    this.logger.info("[Kraken] updateNewPaymentInstrument", { id: paymentInstrumentId });
    const paymentInstrument = await this.paymentInstrumentRepository.findOne({
      where: { id: paymentInstrumentId, user: { id: user.id } },
    });

    if (!paymentInstrument) {
      throw errorBuilder.payment.instrument.notFound(paymentInstrumentId);
    }

    const cardData = await this.updateCard(paymentInstrument.tokenKraken, {
      expirationYear: content.expirationYear,
      expirationMonth: content.expirationMonth,
      cardHolderName: content.cardHolderName,
      cvv: content.securityCode,
    });

    const { token, state } = cardData;

    paymentInstrument.token = token;
    paymentInstrument.tokenKraken = token;
    paymentInstrument.state = state;
    paymentInstrument.expirationYear = content.expirationYear;
    paymentInstrument.expirationMonth = content.expirationMonth;
    paymentInstrument.cardHolderName = content.cardHolderName;
    paymentInstrument.isPayerAuthEnroled = null;
    paymentInstrument.isPreferred = false;
    paymentInstrument.verificationTransactionId = null;
    paymentInstrument.verifiedAt = null;

    paymentInstrument.updateExpirationDate();

    return this.paymentInstrumentRepository.save(paymentInstrument);
  }

  initiate3DSecure(content: PaymentInstrument): Promise<Initiate3DSecureResponse> {
    this.logger.info("[KrakenService] initiate3DSecure start", {
      id: content.id,
    });

    return this.krakenApi
      .payerAuthSetup(content.tokenKraken)
      .then(async (data) => {
        this.logger.info("[KrakenService] initiate3DSecure end", {
          id: content.id,
          data,
        });

        return {
          accessToken: data.gatewayResponse.consumerAuthenticationInformation.accessToken,
          referenceId: data.gatewayResponse.consumerAuthenticationInformation.referenceId,
          deviceDataCollectionUrl: data.gatewayResponse.consumerAuthenticationInformation.deviceDataCollectionUrl,
          status: data.gatewayResponse.status,
        };
      })
      .catch((error) => {
        this.logger.error(
          "[KrakenService] initiate3DSecure error",
          {
            id: content.id,
            error,
          },
          error,
        );

        return {
          accessToken: "",
          referenceId: "",
          deviceDataCollectionUrl: "",
          status: PaymentInstrumentState.VERIFICATION_FAILED,
        };
      });
  }
  async createPaymentWith3DS(
    tx: Tx,
    paymentInstrument: PaymentInstrument,
    threeDSecureOptions: ThreeDSecureOptions,
    isSale?: boolean,
  ): Promise<CreatePaymentResponse> {
    this.logger.info("[Kraken] createPaymentWith3DS start", {
      id: tx.id,
      paymentInstrumentId: paymentInstrument.id,
    });

    const authPaymentTx = this.paymentTxRepository.create({
      id: randomUUID(),
      tx: tx,
      amount: tx.total ?? 0,
      gatewayTransactionId: "",
      cardNumber: paymentInstrument.cardNumber,
      paymentMethod: paymentInstrument.cardType as unknown as PaymentTxPaymentMethod,
      gateway: PaymentGatewayTypes.KRAKEN,
      createdAt: new Date().toISOString(),
      status: PaymentInformationStatus.PENDING,
      type: PaymentInformationType.AUTH,
      paymentInstrument: paymentInstrument,
    });

    await this.paymentTxRepository.save(authPaymentTx);

    const body = {
      cardToken: paymentInstrument.tokenKraken,
      amount: authPaymentTx.amount ?? 0,
      sessionId: threeDSecureOptions.sessionId,
      returnUrl: `${this.configService.getOrThrow(
        "GLOBAL_PAYMENT_BACKEND_URL",
      )}/global-payments/3d-secure/notification`,
      deviceInformation: {
        ipAddress: threeDSecureOptions.ipAddress,
        httpBrowserScreenHeight: threeDSecureOptions.httpBrowserScreenHeight,
        httpBrowserScreenWidth: threeDSecureOptions.httpBrowserScreenWidth,
      },
    };

    const thenFunction = async (data: any) => {
      this.logger.info("[Kraken] createPaymentWith3DS end", {
        id: tx.id,
        data,
      });
      authPaymentTx.amount = data.amount;
      authPaymentTx.status = data.status;
      authPaymentTx.gatewayResponse = data.gatewayResponse;
      authPaymentTx.gatewayTransactionId = data.id;
      authPaymentTx.type = isSale ? PaymentInformationType.SALE : PaymentInformationType.AUTH;
      authPaymentTx.status =
        data.gatewayResponse.status === "AUTHORIZED"
          ? PaymentInformationStatus.SUCCESS
          : PaymentInformationStatus.FAILURE;

      await this.paymentTxRepository.save(authPaymentTx);

      if (!["PENDING_AUTHENTICATION", "AUTHORIZED"].includes(data.gatewayResponse.status)) {
        throw errorBuilder.payment.instrument.threeDSecure.notCompatible(
          paymentInstrument.id,
          data.gatewayResponse.status,
        );
      }
      return {
        id: data.id,
        accessToken: data.gatewayResponse.consumerAuthenticationInformation.accessToken,
        stepUpUrl: data.gatewayResponse.consumerAuthenticationInformation.stepUpUrl,
        status:
          data.gatewayResponse.status === "AUTHORIZED"
            ? PaymentInstrumentState.VERIFIED
            : PaymentInstrumentState.VERIFICATION_PENDING,
        raw: data.gatewayResponse,
      };
    };

    const catchFunction = async (error: any) => {
      this.logger.error(
        "[Kraken] createPaymentWith3DS error",
        {
          id: tx.id,
          error,
        },
        error,
      );
      authPaymentTx.status = PaymentInformationStatus.FAILURE;
      authPaymentTx.gatewayResponse = error;

      await this.paymentTxRepository.save(authPaymentTx);

      return {
        id: authPaymentTx.id,
        accessToken: "",
        stepUpUrl: "",
        status: PaymentInstrumentState.VERIFICATION_FAILED,
        raw: error,
      };
    };

    if (isSale) {
      return this.krakenApi.sale(body).then(thenFunction).catch(catchFunction);
    }

    return this.krakenApi.auth(body).then(thenFunction).catch(catchFunction);
  }

  validateAuthenticationResults(content: PaymentInstrument): Promise<ValidateAuthenticationResultResponse> {
    this.logger.info("[KrakenService] validateAuthenticationResults start", {
      id: content.id,
    });

    if (!content.verificationTransactionId) {
      throw errorBuilder.global.requiredParam("paymentInstrument.verificationTransactionId");
    }

    return this.krakenApi
      .validateAuthenticationResults(content.tokenKraken, {
        authenticationTransactionId: content.verificationTransactionId,
      })
      .then(async (data) => {
        this.logger.info("[KrakenService] validateAuthenticationResults end", {
          id: content.id,
          data,
        });

        return {
          status:
            payerAuthenticationStatusMapping[data.gatewayResponse.status] ?? PaymentInstrumentState.VERIFICATION_FAILED,
          raw: data.gatewayResponse,
        };
      })
      .catch((error) => {
        this.logger.error(
          "[KrakenService] validateAuthenticationResults error",
          {
            id: content.id,
            error,
          },
          error,
        );

        return {
          status: PaymentInstrumentState.VERIFICATION_FAILED,
          raw: error,
        };
      });
  }
  async createAuthWithPayerAuthValidation(tx: Tx, paymentInstrument: PaymentInstrument): Promise<PaymentTx> {
    this.logger.info("[Kraken] createAuthWithPayerAuthValidation start", {
      id: tx.id,
      paymentInstrumentId: paymentInstrument.id,
    });

    if (!paymentInstrument.verificationTransactionId) {
      throw errorBuilder.global.requiredParam("paymentInstrument.verificationTransactionId");
    }

    const authPaymentTx = this.paymentTxRepository.create({
      id: randomUUID(),
      tx: tx,
      amount: tx.total ?? 0,
      gatewayTransactionId: "",
      cardNumber: paymentInstrument.cardNumber,
      paymentMethod: paymentInstrument.cardType as unknown as PaymentTxPaymentMethod,
      gateway: PaymentGatewayTypes.KRAKEN,
      createdAt: new Date().toISOString(),
      status: PaymentInformationStatus.PENDING,
      type: PaymentInformationType.AUTH,
      paymentInstrument: paymentInstrument,
    });

    await this.paymentTxRepository.save(authPaymentTx);

    return this.krakenApi
      .auth({
        cardToken: paymentInstrument.tokenKraken,
        amount: authPaymentTx.amount ?? 0,
        authenticationTransactionId: paymentInstrument.verificationTransactionId,
      })
      .then(async (data) => {
        this.logger.info("[KrakenService] createAuthWithPayerAuthValidation end", {
          id: tx.id,
          data,
        });
        authPaymentTx.status =
          data.gatewayResponse.status === "AUTHORIZED"
            ? PaymentInformationStatus.SUCCESS
            : PaymentInformationStatus.FAILURE;
        authPaymentTx.gatewayResponse = data.gatewayResponse;
        authPaymentTx.gatewayTransactionId = data.id;

        return this.paymentTxRepository.save(authPaymentTx);
      })
      .catch(this.catchHandler(authPaymentTx));
  }
  checkPayerAuthEnrollment(
    paymentInstrument: PaymentInstrument,
    user: User,
  ): Promise<CheckPayerAuthEnrollmentResponse> {
    this.logger.info("[KrakenService] checkPayerAuthEnrollment start", {
      paymentInstrumentId: paymentInstrument.id,
      userId: user.id,
    });

    return this.krakenApi
      .checkPayerAuthEnrolment(paymentInstrument.tokenKraken)
      .then(async (data) => {
        this.logger.info("[KrakenService] checkPayerAuthEnrollment end", {
          paymentInstrumentId: paymentInstrument.id,
          userId: user.id,
          data,
        });

        return {
          isPayerAuthEnroled: data.gatewayResponse.status === "AUTHENTICATION_SUCCESSFUL",
          raw: data.gatewayResponse,
        };
      })
      .catch((error) => {
        this.logger.error(
          "[KrakenService] checkPayerAuthEnrollment error",
          {
            paymentInstrumentId: paymentInstrument.id,
            userId: user.id,
            error,
          },
          error,
        );

        return {
          isPayerAuthEnroled: false,
          raw: error,
        };
      });
  }

  async createCard(content: SaveCardBody) {
    this.logger.info("[Kraken] createCard", { content });

    return this.krakenApi.saveCard({
      cardNumber: content.cardNumber,
      expirationYear: content.expirationYear,
      expirationMonth: content.expirationMonth,
      cardHolderName: content.cardHolderName,
      cvv: content.cvv,
      type: content.type,
      userId: content.userId,
    });
  }

  async updateCard(token: string, content: UpdateCardBody) {
    this.logger.info("[Kraken] updateCard", { token, content });

    return this.krakenApi.updateCard(
      { token },
      {
        cardNumber: content.cardNumber,
        expirationYear: content.expirationYear,
        expirationMonth: content.expirationMonth,
        cardHolderName: content.cardHolderName,
        cvv: content.cvv,
        type: content.type,
      },
    );
  }

  async deleteCard(token: string) {
    this.logger.info("[Kraken] deleteCard", { token });

    return this.krakenApi.deleteCard({ token });
  }

  async capturePayment(authPaymentTx: PaymentTx, total: number, requestedBy?: string): Promise<PaymentTx> {
    this.logger.info("[Kraken] capturePayment", {
      id: authPaymentTx.id,
      total,
    });

    return this.processCapture(authPaymentTx, total, requestedBy);
  }

  async createPayment(
    tx: Tx,
    paymentInstrument: PaymentInstrument,
    isSale = false,
    overwriteAmount?: number,
  ): Promise<PaymentTx> {
    this.logger.info("[Kraken] createPayment", {
      id: tx.id,
      isSale,
      overwriteAmount,
      paymentInstrumentId: paymentInstrument.id,
    });

    if (isSale) {
      return this.processSale(tx, paymentInstrument, overwriteAmount);
    }

    return this.processAuth(tx, paymentInstrument, overwriteAmount);
  }

  async voidPayment(paymentTx: PaymentTx, requestedBy?: string): Promise<PaymentTx> {
    this.logger.info("[Kraken] voidPayment", { id: paymentTx.id });

    if (!paymentTx.gatewayTransactionId) {
      throw errorBuilder.payment.voidFailed(paymentTx.id, { message: "Gateway transaction id not found" });
    }

    const voidPaymentTx = this.paymentTxRepository.create({
      id: randomUUID(),
      tx: paymentTx.tx,
      amount: paymentTx.amount,
      gatewayTransactionId: "",
      cardNumber: paymentTx.cardNumber,
      paymentMethod: paymentTx.paymentMethod,
      gateway: PaymentGatewayTypes.KRAKEN,
      createdAt: new Date().toISOString(),
      status: PaymentInformationStatus.PENDING,
      type: PaymentInformationType.VOID,
      paymentInstrument: paymentTx.paymentInstrument,
      parent: paymentTx,
      requestedBy,
    });

    await this.paymentTxRepository.save(voidPaymentTx);
    this.logger.info("[Kraken] voidPayment saved", { id: voidPaymentTx.id });

    return this.krakenApi
      .void(paymentTx.gatewayTransactionId)
      .then(async (data) => {
        this.logger.info("[Kraken] voidPayment response", { data });
        voidPaymentTx.status = data.status;
        voidPaymentTx.gatewayResponse = data;
        voidPaymentTx.gatewayTransactionId = data.id;

        await this.paymentTxRepository.save(voidPaymentTx);

        return voidPaymentTx;
      })
      .catch(this.catchHandler(voidPaymentTx));
  }
}
