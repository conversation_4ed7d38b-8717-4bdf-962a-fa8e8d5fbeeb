import { ApiProperty } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";

import { PaymentInstrumentType } from "../../../dto/paymentInstrument.dto";

import { CardType, cardTypeSchema } from "./card.dto";

export class SaveCardBody {
  @ApiProperty({ example: "****************" })
  cardNumber: string;

  @ApiProperty({ example: "31" })
  expirationYear: string;

  @ApiProperty({ example: "01" })
  expirationMonth: string;

  @ApiProperty({ example: "Goku" })
  cardHolderName: string;

  @ApiProperty({ example: "777" })
  cvv: string;

  @ApiProperty({ example: CardType.VISA })
  type: PaymentInstrumentType;

  @ApiProperty({ example: "b584cd9a-93b6-474e-9ba3-2318ed40154c" })
  userId: string;
}

export const saveCardBodySchema = Joi.object<SaveCardBody>({
  cardNumber: Joi.string().required(),
  expirationYear: Joi.string().required(),
  expirationMonth: Joi.string().required(),
  cardHolderName: Joi.string().required(),
  cvv: Joi.string().required(),
  type: cardTypeSchema.required(),
  userId: Joi.string().uuid().required(),
});
