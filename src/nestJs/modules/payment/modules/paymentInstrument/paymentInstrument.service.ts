import { randomUUID } from "crypto";

import { Inject, Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { InjectRepository } from "@nestjs/typeorm";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ull, <PERSON><PERSON><PERSON>, SelectQueryBuilder } from "typeorm";

import { VoidTxJobDocument, VoidTxJobType } from "@nest/modules/appDatabase/documents/voidTxJob.document";
import { TxHailingRequestStatus } from "@nest/modules/transaction/dto/txHailingRequest.dto";
import LoggerServiceAdapter from "@nest/modules/utils/logger/logger.service";

import { AppDatabaseService } from "../../../appDatabase/appDatabase.service";
import { TxAppsNames } from "../../../apps/dto/Apps.dto";
import PaymentInstrument from "../../../database/entities/paymentInstrument.entity";
import PaymentTx from "../../../database/entities/paymentTx.entity";
import User from "../../../database/entities/user.entity";
import { TxAppRepository } from "../../../database/repositories/app.repository";
import { PaymentInstrumentRepository } from "../../../database/repositories/paymentInstument.repository";
import { PaymentTxRepository } from "../../../database/repositories/paymentTx.repository";
import { TxRepository } from "../../../database/repositories/tx.repository";
import { UserRepository } from "../../../database/repositories/user.repository";
import {
  CheckPayerEnrolmentParams,
  CreatePaymentInstrumentBody,
  CreatePaymentInstrumentResponse,
  DeletePaymentInstrumentParams,
  GetPaymentInstrumentParams,
  InitiatePaymentInstrumentVerifyParams,
  ProcessPaymentInstrumentVerifyBody,
  ProcessPaymentInstrumentVerifyParams,
  SetPreferredPaymentInstrumentParams,
  UpdatePaymentInstrumentBody,
  UpdatePaymentInstrumentParams,
} from "../../../me/modules/mePaymentInstrument/mePaymentInstrument.dto";
import { Tx3DSecure } from "../../../transaction/dto/tx.dto";
import { TxTypes } from "../../../transaction/dto/txType.dto";
import { errorBuilder } from "../../../utils/utils/error.utils";
import loggerUtils from "../../../utils/utils/logger.utils";
import { PaymentGatewayTypes } from "../../dto/paymentGatewayTypes.dto";
import { PaymentInformationStatus } from "../../dto/paymentInformationStatus.dto";
import { PaymentInformationType } from "../../dto/paymentInformationType.dto";

import { ManualService } from "./modules/manual/manual.service";
import { CardState } from "./modules/kraken/dto/card.dto";
import { GlobalPaymentService } from "./modules/globalPayment/globalPayment.service";
import { Initiate3DSecureResponse, PaymentInstrumentState, CreatePaymentResponse } from "./dto/paymentInstrument.dto";
import { KrakenService } from "./modules/kraken/kraken.service";

type FactoryServicesMap = {
  service: GlobalPaymentService | ManualService | KrakenService;
};

/**
 * PaymentInstrument service
 */
@Injectable()
export class PaymentInstrumentService {
  paymentInstrumentServicesMap: Record<PaymentGatewayTypes, FactoryServicesMap>;
  AUTH_AMOUNT_WHEN_ADD_CARD = 1;
  constructor(
    private readonly globalPaymentService: GlobalPaymentService,
    @InjectRepository(PaymentInstrumentRepository) private paymentInstrumentRepository: PaymentInstrumentRepository,
    @InjectRepository(UserRepository) private userRepository: UserRepository,
    @InjectRepository(TxAppRepository) private readonly txAppRepository: TxAppRepository,
    @InjectRepository(TxRepository) private readonly txRepository: TxRepository,
    @InjectRepository(PaymentTxRepository) private readonly paymentTxRepository: PaymentTxRepository,
    private readonly appDatabaseService: AppDatabaseService,
    private readonly krakenService: KrakenService,
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
    private readonly configService: ConfigService,
  ) {
    const manualService = new ManualService();
    this.paymentInstrumentServicesMap = {
      [PaymentGatewayTypes.GLOBAL_PAYMENTS]: { service: this.globalPaymentService },
      [PaymentGatewayTypes.MANUAL]: { service: manualService },
      [PaymentGatewayTypes.SOEPAY]: { service: manualService },
      [PaymentGatewayTypes.KRAKEN]: { service: this.krakenService },
    };
  }

  private checkFactory(paymentGateway: PaymentGatewayTypes) {
    const factory = this.paymentInstrumentServicesMap[paymentGateway];

    if (!factory || !factory.service) {
      throw errorBuilder.factory.notImplemented("PaymentInstrumentService.checkFactory");
    }

    return { factory };
  }

  async getTx({
    user,
    instrumentIdentifier,
    token,
    paymentGateway,
  }: {
    user: User;
    instrumentIdentifier: string;
    token: string;
    paymentGateway: PaymentGatewayTypes;
  }): Promise<Tx3DSecure> {
    const txApp = await this.txAppRepository.appByNameOrCreate(TxAppsNames.VISMO);

    const tx = await this.txRepository
      .createQueryBuilder("entity")
      .select()
      .where(
        `entity.metadata ::jsonb @> '{"instrumentIdentifier": "${instrumentIdentifier}", "paymentGateway": "${paymentGateway}", "token": "${token}"}'`,
      )
      .andWhere("entity.user = :user", { user: user.id })
      .andWhere("entity.txApp = :txApp", { txApp: txApp.id })
      .andWhere("entity.type = :type", { type: TxTypes.CARD_VERIFICATION })
      .orderBy("entity.updatedAt", "DESC")
      .getOne();

    if (!tx) {
      const tx = this.txRepository.create({
        id: randomUUID(),
        txApp,
        type: TxTypes.CARD_VERIFICATION,
        metadata: { instrumentIdentifier, paymentGateway, token },
        user,
      }) as Tx3DSecure;

      await this.txRepository.save(tx);
      return tx;
    }
    tx.txApp = txApp;
    return tx as Tx3DSecure;
  }

  async savePaymentInstrument(paymentInstrument: PaymentInstrument, userAppDatabaseId: string) {
    return this.paymentInstrumentRepository.manager
      .transaction(async (transactionalEntityManager) => {
        await transactionalEntityManager.save(paymentInstrument);

        await this.appDatabaseService
          .userPaymentInstrumentRepository(userAppDatabaseId)
          .set(paymentInstrument.toJson());
      })
      .catch((error) => {
        this.logger.error({ error });
        throw errorBuilder.payment.instrument.couldNotSave(paymentInstrument.id, error);
      });
  }

  async createPaymentInstrument(
    content: CreatePaymentInstrumentBody,
    userAppDatabaseId: string,
    paymentGateway: PaymentGatewayTypes,
  ): Promise<CreatePaymentInstrumentResponse> {
    const { factory } = this.checkFactory(paymentGateway);

    const user = await this.userRepository.findOne({ where: { appDatabaseId: userAppDatabaseId } });
    if (!user) {
      throw errorBuilder.user.notFoundInSql(userAppDatabaseId);
    }

    const paymentInstrument = await factory.service.createNewPaymentInstrument(content, user);

    paymentInstrument.verifiedAt = null;
    paymentInstrument.state = PaymentInstrumentState.ACTIVE;
    await this.savePaymentInstrument(paymentInstrument, userAppDatabaseId);

    const tx = await this.getTx({
      user,
      instrumentIdentifier: paymentInstrument.instrumentIdentifier,
      token: paymentInstrument.token,
      paymentGateway,
    });
    tx.total = this.AUTH_AMOUNT_WHEN_ADD_CARD;
    const paymentTx = await factory.service.createPayment(tx, paymentInstrument);
    await this.paymentTxRepository.save(paymentTx);

    if (paymentTx.status !== PaymentInformationStatus.SUCCESS) {
      throw errorBuilder.payment.authFailed(paymentTx.gatewayResponse);
    }

    let response: CreatePaymentInstrumentResponse = {
      ...paymentInstrument.publicData,
      paymentInstrument: paymentInstrument.publicData,
    };

    const enable3DSecure = await this.appDatabaseService.configurationRepository().getEnable3DSecure();

    if (!enable3DSecure) {
      paymentInstrument.verifiedAt = new Date();
      paymentInstrument.state = PaymentInstrumentState.VERIFIED;
      const paymentInstrumentCount = await this.paymentInstrumentRepository.count({
        where: { user: { id: user.id }, isPreferred: true },
      });
      if (paymentInstrumentCount === 0) {
        paymentInstrument.isPreferred = true;
      }

      await this.savePaymentInstrument(paymentInstrument, userAppDatabaseId);
    } else {
      response = {
        ...paymentInstrument.publicData,
        paymentInstrument: paymentInstrument.publicData,
        webviewVerificationUrl: `${this.configService.getOrThrow("WEB_SECURE_URL")}/me/3d-secure/verify-card`,
      };
    }

    tx.metadata.instrumentIdentifierResponse = {};
    tx.metadata.paymentInstrumentResponse = paymentInstrument;
    await this.txRepository.save(tx);
    if (paymentTx.status === PaymentInformationStatus.SUCCESS) {
      const voidTxJob: VoidTxJobDocument = {
        id: paymentTx.id,
        txId: tx.id,
        userId: user.id,
        createdAt: new Date(),
        expectVoidAt: new Date(Date.now() + 1000 * 60 * 60),
        type: VoidTxJobType.VOID_AUTH_AFTER_ADD_NEW_CARD,
        retryTimes: 0,
        voidedAt: null,
      };
      await this.appDatabaseService.voidTxJobsRepository().create(voidTxJob);
    }
    return response;
  }

  async initiate3DSecure(
    content: InitiatePaymentInstrumentVerifyParams,
    userAppDatabaseId: string,
  ): Promise<Initiate3DSecureResponse> {
    const user = await this.userRepository.findOne({ where: { appDatabaseId: userAppDatabaseId } });

    if (!user) {
      throw errorBuilder.user.notFoundInSql(userAppDatabaseId);
    }

    const paymentInstrument = await this.paymentInstrumentRepository.findOne({
      where: { id: content.paymentInstrumentId, user: { id: user.id } },
    });

    if (!paymentInstrument) {
      throw errorBuilder.payment.instrument.notFound(content.paymentInstrumentId);
    }

    const { factory } = this.checkFactory(paymentInstrument.paymentGateway);

    const enable3DSecure = await this.appDatabaseService.configurationRepository().getEnable3DSecure();

    if (!enable3DSecure) {
      return {
        accessToken: "DISABLED",
        referenceId: "DISABLED",
        deviceDataCollectionUrl: "DISABLED",
        status: "DISABLED",
      };
    }

    const result = await factory.service.initiate3DSecure(paymentInstrument);

    const tx = await this.getTx({
      user,
      instrumentIdentifier: paymentInstrument.instrumentIdentifier,
      token: paymentInstrument.token,
      paymentGateway: paymentInstrument.paymentGateway,
    });
    tx.total = 0;
    tx.dashFee = 0;
    tx.payoutAmount = 0;
    tx.adjustment = 0;

    tx.metadata.init = result;
    await this.txRepository.save(tx);

    if (result.status !== "COMPLETED") {
      throw errorBuilder.payment.instrument.threeDSecure.notCompatible(paymentInstrument.id, result.status);
    }

    return result;
  }

  async getPaymentInstrument(
    content: GetPaymentInstrumentParams,
    userAppDatabaseId: string,
  ): Promise<Partial<PaymentInstrument>> {
    const user = await this.userRepository.findOne({ where: { appDatabaseId: userAppDatabaseId } });

    if (!user) {
      throw errorBuilder.user.notFoundInSql(userAppDatabaseId);
    }

    const paymentInstrument = await this.paymentInstrumentRepository.findOne({
      where: { id: content.paymentInstrumentId, user: { id: user.id } },
    });

    if (!paymentInstrument) {
      throw errorBuilder.payment.instrument.notFound(content.paymentInstrumentId);
    }

    return paymentInstrument.publicData;
  }

  async createPayment(
    content: ProcessPaymentInstrumentVerifyParams,
    deviceConfig: ProcessPaymentInstrumentVerifyBody,
    userAppDatabaseId: string,
  ): Promise<CreatePaymentResponse> {
    const user = await this.userRepository.findOne({ where: { appDatabaseId: userAppDatabaseId } });

    if (!user) {
      throw errorBuilder.user.notFoundInSql(userAppDatabaseId);
    }

    const paymentInstrument = await this.paymentInstrumentRepository.findOne({
      where: { id: content.paymentInstrumentId, user: { id: user.id } },
      relations: ["user"],
    });

    if (!paymentInstrument) {
      throw errorBuilder.payment.instrument.notFound(content.paymentInstrumentId);
    }

    const { factory } = this.checkFactory(paymentInstrument.paymentGateway);

    const tx = await this.getTx({
      user,
      instrumentIdentifier: paymentInstrument.instrumentIdentifier,
      token: paymentInstrument.token,
      paymentGateway: paymentInstrument.paymentGateway,
    });
    tx.total = this.AUTH_AMOUNT_WHEN_ADD_CARD;
    const result = await factory.service.createPaymentWith3DS(tx, paymentInstrument, {
      sessionId: content.sessionId,
      ipAddress: deviceConfig.ipAddress,
      httpBrowserScreenHeight: deviceConfig.httpBrowserScreenHeight,
      httpBrowserScreenWidth: deviceConfig.httpBrowserScreenWidth,
    });

    if (result.status === PaymentInstrumentState.VERIFIED) {
      paymentInstrument.verifiedAt = new Date();
      paymentInstrument.state = PaymentInstrumentState.VERIFIED;
      const paymentInstrumentCount = await this.paymentInstrumentRepository.count({
        where: { user: { id: user.id }, state: PaymentInstrumentState.VERIFIED },
      });
      if (paymentInstrumentCount === 0) {
        paymentInstrument.isPreferred = true;
      }

      await this.savePaymentInstrument(paymentInstrument, userAppDatabaseId);

      this.krakenService.updateCard(paymentInstrument.token, { state: CardState.VERIFIED });
    }

    tx.metadata.process = result;
    tx.metadata.deviceConfig = deviceConfig;
    tx.metadata.sessionId = content.sessionId;
    await this.txRepository.save(tx);
    const paymentTx = PaymentTx.fromJson(
      {
        id: randomUUID(),
        amount: tx.total,
        gatewayTransactionId: result.id,
        cardNumber: paymentInstrument.cardNumber,
        paymentMethod: paymentInstrument.cardType,
        tx: tx,
        gateway: paymentInstrument.paymentGateway,
        gatewayResponse: result.raw,
        status:
          result.status === PaymentInstrumentState.VERIFIED
            ? PaymentInformationStatus.SUCCESS
            : PaymentInformationStatus.FAILURE,
        type: PaymentInformationType.AUTH,
        createdAt: new Date().toISOString(),
        paymentInstrument,
      },
      tx.id,
    );
    await this.paymentTxRepository.save(paymentTx);
    if (paymentTx.status === PaymentInformationStatus.SUCCESS) {
      const voidTxJob: VoidTxJobDocument = {
        id: paymentTx.id,
        txId: tx.id,
        userId: user.id,
        createdAt: new Date(),
        expectVoidAt: new Date(Date.now() + 1000 * 60 * 60),
        type: VoidTxJobType.VOID_AUTH_AFTER_ADD_NEW_CARD,
        retryTimes: 0,
        voidedAt: null,
      };
      await this.appDatabaseService.voidTxJobsRepository().create(voidTxJob);
    }
    return result;
  }

  async receiveTransaction(
    paymentInstrumentId: string,
    transactionId: string,
    userAppDatabaseId: string,
  ): Promise<PaymentInstrument> {
    const user = await this.userRepository.findOne({ where: { appDatabaseId: userAppDatabaseId } });

    if (!user) {
      throw errorBuilder.user.notFoundInSql(userAppDatabaseId);
    }

    const paymentInstrument = await this.paymentInstrumentRepository.findOne({
      where: { id: paymentInstrumentId, user: { id: user.id } },
    });

    if (!paymentInstrument) {
      throw errorBuilder.payment.instrument.notFound(paymentInstrumentId);
    }

    paymentInstrument.verificationTransactionId = transactionId;

    await this.savePaymentInstrument(paymentInstrument, userAppDatabaseId);

    try {
      return this.verifyPaymentInstrument(paymentInstrumentId, userAppDatabaseId);
    } catch (error) {
      // we don't want to throw here, because we want to return the paymentInstrument even if it failed to verify
      loggerUtils.error("verifyPaymentInstrument error", { paymentInstrumentId, userAppDatabaseId }, error as Error);
    }

    return paymentInstrument;
  }

  async verifyPaymentInstrument(paymentInstrumentId: string, userAppDatabaseId: string): Promise<PaymentInstrument> {
    const user = await this.userRepository.findOne({ where: { appDatabaseId: userAppDatabaseId } });

    if (!user) {
      throw errorBuilder.user.notFoundInSql(userAppDatabaseId);
    }

    const paymentInstrument = await this.paymentInstrumentRepository.findOne({
      where: { id: paymentInstrumentId, user: { id: user.id } },
    });

    if (!paymentInstrument) {
      throw errorBuilder.payment.instrument.notFound(paymentInstrumentId);
    }

    const tx = await this.getTx({
      user,
      instrumentIdentifier: paymentInstrument.instrumentIdentifier,
      token: paymentInstrument.token,
      paymentGateway: paymentInstrument.paymentGateway,
    });

    const { factory } = this.checkFactory(paymentInstrument.paymentGateway);

    const paymentTx = await factory.service.createAuthWithPayerAuthValidation(tx, paymentInstrument);
    await this.paymentTxRepository.save(paymentTx);

    paymentInstrument.state =
      paymentTx.status === PaymentInformationStatus.SUCCESS
        ? PaymentInstrumentState.VERIFIED
        : PaymentInstrumentState.VERIFICATION_FAILED;

    if (paymentTx.status === PaymentInformationStatus.SUCCESS) {
      paymentInstrument.verifiedAt = new Date();
      const paymentInstrumentCount = await this.paymentInstrumentRepository.count({
        where: { user: { id: user.id }, state: PaymentInstrumentState.VERIFIED },
      });
      if (paymentInstrumentCount === 0) {
        paymentInstrument.isPreferred = true;
      }
    }

    await this.savePaymentInstrument(paymentInstrument, userAppDatabaseId);

    this.krakenService.updateCard(paymentInstrument.token, {
      state: paymentInstrument.state === PaymentInstrumentState.VERIFIED ? CardState.VERIFIED : CardState.NOT_VERIFIED,
    });

    tx.metadata.validateAuthenticationResults = paymentTx;
    await this.txRepository.save(tx);

    return paymentInstrument;
  }

  async checkPayerAuthEnrollment(
    content: CheckPayerEnrolmentParams,
    userAppDatabaseId: string,
  ): Promise<PaymentInstrument> {
    const user = await this.userRepository.findOne({ where: { appDatabaseId: userAppDatabaseId } });

    if (!user) {
      throw errorBuilder.user.notFoundInSql(userAppDatabaseId);
    }

    const paymentInstrument = await this.paymentInstrumentRepository.findOne({
      where: { id: content.paymentInstrumentId, user: { id: user.id } },
    });

    if (!paymentInstrument) {
      throw errorBuilder.payment.instrument.notFound(content.paymentInstrumentId);
    }

    const { factory } = this.checkFactory(paymentInstrument.paymentGateway);

    const data = await factory.service.checkPayerAuthEnrollment(paymentInstrument, user);

    paymentInstrument.isPayerAuthEnroled = data.isPayerAuthEnroled;

    await this.savePaymentInstrument(paymentInstrument, userAppDatabaseId);

    const tx = await this.getTx({
      user,
      instrumentIdentifier: paymentInstrument.instrumentIdentifier,
      token: paymentInstrument.token,
      paymentGateway: paymentInstrument.paymentGateway,
    });
    tx.metadata.checkPayerAuthEnrollment = data.raw;
    tx.metadata.isPayerAuthEnroled = data.isPayerAuthEnroled;
    await this.txRepository.save(tx);

    if (!data.isPayerAuthEnroled) {
      throw errorBuilder.payment.instrument.threeDSecure.notCompatible(paymentInstrument.id, "NOT_ENROLLED");
    }

    return paymentInstrument;
  }

  /**
   * Set Preferred Payment Instrument
   * @param setPreferredPaymentInstrumentParams SetPreferredPaymentInstrumentParams
   * @param userAppDatabaseId string
   * @returns PaymentInstrument
   */
  async setPreferredPaymentInstrument(
    setPreferredPaymentInstrumentParams: SetPreferredPaymentInstrumentParams,
    userAppDatabaseId: string,
  ): Promise<Partial<PaymentInstrument>> {
    const { paymentInstrumentId } = setPreferredPaymentInstrumentParams;

    const user = await this.userRepository.findOne({ where: { appDatabaseId: userAppDatabaseId } });

    if (!user) {
      throw errorBuilder.user.notFoundInSql(userAppDatabaseId);
    }

    const paymentInstruments = await this.paymentInstrumentRepository.find({ where: { user: { id: user.id } } });

    let paymentInstrument;
    for (const instrument of paymentInstruments) {
      instrument.isPreferred = false;
      if (instrument.id === paymentInstrumentId) {
        instrument.isPreferred = true;
        paymentInstrument = instrument;
      }
    }

    if (!paymentInstrument) {
      throw errorBuilder.payment.instrument.notFound(paymentInstrumentId);
    }

    this.paymentInstrumentRepository.manager.transaction(async (transactionalEntityManager) => {
      await transactionalEntityManager.save(paymentInstruments);

      await Promise.all(
        paymentInstruments.map((instrument) =>
          this.appDatabaseService.userPaymentInstrumentRepository(userAppDatabaseId).set(instrument.toJson()),
        ),
      );
    });

    return paymentInstrument.publicData;
  }

  /**
   * delete payment instrument
   * @param params DeletePaymentInstrumentParams
   * @param userAppDatabaseId string
   * @returns PaymentInstrument
   */
  async delete(params: DeletePaymentInstrumentParams, userAppDatabaseId: string): Promise<Partial<PaymentInstrument>> {
    const user = await this.userRepository.findOne({ where: { appDatabaseId: userAppDatabaseId } });

    if (!user) {
      throw errorBuilder.user.notFoundInSql(userAppDatabaseId);
    }

    const paymentInstrument = await this.paymentInstrumentRepository.findOne({
      where: { id: params.paymentInstrumentId, user: { id: user.id } },
    });

    if (!paymentInstrument) {
      throw errorBuilder.payment.instrument.notFound(params.paymentInstrumentId);
    }

    //check if there is an ongoing trip or hailing order for for this user with this payment instrument(within 1 week)
    const isOngoingTxExists = await this.txRepository
      .createQueryBuilder("tx")
      .where("tx.user = :user", { user: user.id })
      .andWhere(
        new Brackets((qb) => {
          qb.where(
            // Have ongoing Hailing_Request: not paired with trip and status is still pending
            new Brackets((qbHailingRequest) => {
              qbHailingRequest
                .where("tx.type = :hailingRequestType", { hailingRequestType: TxTypes.HAILING_REQUEST })
                .andWhere("tx.parentTxId IS NULL")
                .andWhere(
                  "to_timestamp(tx.metadata->>'createdAt', 'YYYY-MM-DD\"T\"HH24:MI:SS') >= date_trunc('day', now()) - interval '1 week' - interval '8 hours'",
                )
                .andWhere("tx.metadata->>'status' = :hailingStatus", {
                  hailingStatus: TxHailingRequestStatus.PENDING,
                })
                .andWhere("tx.metadata->'request'->>'paymentInstrumentId' = :paymentInstrumentId", {
                  paymentInstrumentId: paymentInstrument.id,
                });
            }),
          ).orWhere(
            // Having ongoing trip: trip without tripEnd
            new Brackets((qbTrip) => {
              qbTrip
                .where("tx.type = :tripType", { tripType: TxTypes.TRIP })
                .andWhere(
                  "to_timestamp(tx.metadata->>'creationTime', 'YYYY-MM-DD\"T\"HH24:MI:SS') >= date_trunc('day', now()) - interval '1 week' - interval '8 hours'",
                )
                .andWhere("tx.metadata->>'tripEnd' IS NULL")
                .andWhere((qbPaymentTx: SelectQueryBuilder<PaymentTx>) => {
                  return `EXISTS ${qbPaymentTx
                    .subQuery()
                    .select("1")
                    .from("payment_tx", "paymentTx")
                    .where("paymentTx.txId = tx.id")
                    .andWhere("paymentTx.paymentInstrumentId = :paymentInstrumentId ::uuid", {
                      paymentInstrumentId: paymentInstrument.id,
                    })
                    .getQuery()}`;
                });
            }),
          );
        }),
      )
      .getCount();
    if (isOngoingTxExists > 0) {
      throw errorBuilder.payment.instrument.couldNotDeleteWithOngoingTx(paymentInstrument.id);
    }

    const { factory } = this.checkFactory(paymentInstrument.paymentGateway);

    await factory.service.deletePaymentInstrument(paymentInstrument, user);

    await this.paymentInstrumentRepository.manager
      .transaction(async (transactionalEntityManager) => {
        const result = await transactionalEntityManager.softDelete(PaymentInstrument, {
          id: paymentInstrument.id,
          user: { id: user.id },
          deletedAt: IsNull(),
        });

        if (!result.affected || result.affected === 0) {
          throw errorBuilder.payment.instrument.notFound(params.paymentInstrumentId);
        }

        if (paymentInstrument.isPreferred) {
          await transactionalEntityManager.update(PaymentInstrument, { user: { id: user.id } }, { isPreferred: false });

          const toUpdate = await transactionalEntityManager.findOne(PaymentInstrument, {
            where: {
              user: { id: user.id },
              state: PaymentInstrumentState.VERIFIED,
              isPayerAuthEnroled: true,
              expirationDate: MoreThan(new Date()),
            },
          });

          if (toUpdate) {
            toUpdate.isPreferred = true;
            await transactionalEntityManager.update(PaymentInstrument, { id: toUpdate.id }, { isPreferred: true });
            await this.appDatabaseService.userPaymentInstrumentRepository(userAppDatabaseId).set(toUpdate.toJson());
          }
        }

        return this.appDatabaseService
          .userPaymentInstrumentRepository(userAppDatabaseId)
          .collection.doc(paymentInstrument.id)
          .delete();
      })
      .catch((error) => {
        throw errorBuilder.payment.instrument.couldNotDelete(paymentInstrument.id, error);
      });

    return paymentInstrument.publicData;
  }

  /**
   * update payment instrument
   * @param params UpdatePaymentInstrumentParams
   * @param paymentInstrumentData UpdatePaymentInstrumentBody
   * @param userAppDatabaseId string
   * @returns PaymentInstrument
   */
  async update(
    params: UpdatePaymentInstrumentParams,
    paymentInstrumentData: UpdatePaymentInstrumentBody,
    userAppDatabaseId: string,
  ): Promise<Partial<PaymentInstrument>> {
    const user = await this.userRepository.findOne({ where: { appDatabaseId: userAppDatabaseId } });

    if (!user) {
      throw errorBuilder.user.notFoundInSql(userAppDatabaseId);
    }

    const paymentInstrument = await this.paymentInstrumentRepository.findOne({
      where: { id: params.paymentInstrumentId, user: { id: user.id } },
    });

    if (!paymentInstrument) {
      throw errorBuilder.payment.instrument.notFound(params.paymentInstrumentId);
    }

    const { factory } = this.checkFactory(paymentInstrument.paymentGateway);

    const newPaymentInstrument = await factory.service.updateNewPaymentInstrument(
      params.paymentInstrumentId,
      paymentInstrumentData,
      user,
    );

    const tx = await this.getTx({
      user,
      instrumentIdentifier: newPaymentInstrument.instrumentIdentifier,
      token: newPaymentInstrument.token,
      paymentGateway: paymentInstrument.paymentGateway,
    });
    tx.metadata.paymentInstrumentResponse = newPaymentInstrument;
    await this.txRepository.save(tx);

    const paymentTx = await factory.service.createPayment(tx, paymentInstrument);
    await this.paymentTxRepository.save(paymentTx);

    if (paymentTx.status !== PaymentInformationStatus.SUCCESS) {
      paymentInstrument.state = PaymentInstrumentState.ACTIVE;
    } else {
      paymentInstrument.state = PaymentInstrumentState.VERIFIED;
      paymentInstrument.verifiedAt = new Date();
      const paymentInstrumentCount = await this.paymentInstrumentRepository.count({
        where: { user: { id: user.id }, isPreferred: true },
      });
      if (paymentInstrumentCount === 0) {
        paymentInstrument.isPreferred = true;
      }
    }

    await this.savePaymentInstrument(paymentInstrument, userAppDatabaseId);

    return paymentInstrument.publicData;
  }

  /**
   * Get Payment Instruments For User
   * @returns payment instruments for user including deleted ones.
   */
  async getPaymentInstrumentsForUser(userId: string): Promise<PaymentInstrument[]> {
    return this.paymentInstrumentRepository.find({ where: { user: { id: userId } }, withDeleted: true });
  }
}
