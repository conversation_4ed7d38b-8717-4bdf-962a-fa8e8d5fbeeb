import { Inject, Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Filter } from "firebase-admin/firestore";
import { MoreThan, Raw } from "typeorm";

import { AppDatabaseService } from "../appDatabase/appDatabase.service";
import { VoidTxJobDocument } from "../appDatabase/documents/voidTxJob.document";
import PaymentInstrument from "../database/entities/paymentInstrument.entity";
import PaymentTx from "../database/entities/paymentTx.entity";
import Tx from "../database/entities/tx.entity";
import TxTag from "../database/entities/txTag.entity";
import { PaymentInstrumentRepository } from "../database/repositories/paymentInstument.repository";
import { PaymentTxRepository } from "../database/repositories/paymentTx.repository";
import { TxRepository } from "../database/repositories/tx.repository";
import { TxTagRepository } from "../database/repositories/txTag.repository";
import { TxTagType } from "../transaction/dto/txTagType.dto";
import { CorrelationContext } from "../utils/context/decorators/correlation-context.decorator";
import { errorBuilder } from "../utils/utils/error.utils";
import LoggerServiceAdapter from "../utils/logger/logger.service";

import { PaymentInstrumentState } from "./modules/paymentInstrument/dto/paymentInstrument.dto";
import { PaymentFactoryService } from "./paymentFactory/paymentFactory.service";
import PaymentTxFromDocument from "./dto/paymentTxFromDocument.model";
import { PaymentInformationType } from "./dto/paymentInformationType.dto";
import { PaymentInformationStatus } from "./dto/paymentInformationStatus.dto";
import { PaymentGatewayTypes } from "./dto/paymentGatewayTypes.dto";
import { EnquiryResponse, PaymentGatewayResponse } from "./dto/paymentGatewayResponses.model";

/**
 * Payment service
 * This service is used to extract payment transaction information from document
 * and process payment transaction
 */
@Injectable()
export class PaymentService {
  constructor(
    private readonly paymentFactoryService: PaymentFactoryService,
    @InjectRepository(PaymentTxRepository) private paymentTxRepository: PaymentTxRepository,
    @InjectRepository(TxTagRepository) private txTagRepository: TxTagRepository,
    @InjectRepository(PaymentInstrumentRepository) private paymentInstrumentRepository: PaymentInstrumentRepository,
    private readonly txRepository: TxRepository,
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
    private readonly appDatabaseService: AppDatabaseService,
  ) {}

  /**
   * Process capture
   */
  processCapture(paymentTx: PaymentTx, total: number, requestedBy?: string) {
    return this.paymentFactoryService.processCapture(paymentTx, total, requestedBy);
  }

  /**
   * Process sale
   */
  async processSale(foundTx: Tx, paymentInstrumentId: string, overwriteAmount?: number, requestedBy?: string) {
    const paymentInstrument = await this.paymentInstrumentRepository.findOneBy({
      id: paymentInstrumentId,
      state: PaymentInstrumentState.VERIFIED,
      expirationDate: MoreThan(new Date()),
    });

    if (!paymentInstrument) {
      throw errorBuilder.payment.instrument.notFound(paymentInstrumentId);
    }

    const alreadySoldAmount =
      foundTx.paymentTx
        ?.filter((ptx) => ptx.type === PaymentInformationType.SALE && ptx.status === PaymentInformationStatus.SUCCESS)
        .reduce((total, currentPaymentTx) => total + (currentPaymentTx.amount ?? 0), 0) ?? 0;

    if (alreadySoldAmount !== 0 && (foundTx.total ?? 0) >= alreadySoldAmount) {
      // Eventually handle to sale the difference
      await this.txTagRepository.addTagsToTx(foundTx, [
        TxTag.createTxTag(TxTagType.ALREADY_SOLD, foundTx.id, "SYSTEM", "The tx is already partially sold"),
      ]);
      throw errorBuilder.payment.alreadySold(foundTx.id, paymentInstrument.id);
    } else if (alreadySoldAmount !== 0 && (foundTx.total ?? 0) < alreadySoldAmount) {
      // Eventually handle to refund user
      await this.txTagRepository.addTagsToTx(foundTx, [
        TxTag.createTxTag(TxTagType.ALREADY_SOLD, foundTx.id, "SYSTEM", "The tx is already overly sold"),
      ]);
      throw errorBuilder.payment.alreadySold(foundTx.id, paymentInstrument.id);
    }

    const paymentTx = await this.paymentFactoryService.processSale(
      foundTx,
      paymentInstrument,
      overwriteAmount,
      requestedBy,
    );

    await this.paymentTxRepository.save(paymentTx);

    if (paymentTx.status === PaymentInformationStatus.FAILURE) {
      await this.txTagRepository.addTagsToTx(foundTx, [
        TxTag.createTxTag(TxTagType.UNABLE_TO_SELL, foundTx.id, "SYSTEM", "Unable to sell tx"),
      ]);
      throw errorBuilder.payment.unableToSell(foundTx.id, paymentInstrument.id);
    }

    return paymentTx;
  }

  /**
   * Process auth
   */
  processAuth(tx: Tx, paymentInstrument: PaymentInstrument, overwriteAmount?: number, requestedBy?: string) {
    return this.paymentFactoryService.processAuth(tx, paymentInstrument, overwriteAmount, requestedBy);
  }

  /**
   * Extract payment transaction information from document
   * @param document
   * @param paymentGatewayType PaymentGatewayTypes
   * @returns PaymentTxFromDocument
   */
  extractPaymentTxInfoFromDocument(
    document: PaymentGatewayResponse,
    paymentGatewayType: PaymentGatewayTypes,
    options?: any,
  ): PaymentTxFromDocument {
    return this.paymentFactoryService.extractPaymentTxInfoFromDocument(document, paymentGatewayType, options);
  }

  /**
   * Find last success auth without capture
   * @param paymentTxs PaymentTx[]
   * @returns PaymentTx | undefined
   */
  findLastSuccessAuthWithoutCaptureOrVoid(paymentTxs?: PaymentTx[]): PaymentTx | undefined {
    const allAuthsWithoutCaptureOrVoid: PaymentTx[] | undefined = paymentTxs
      ?.sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime())
      .reduce((currentAuths: PaymentTx[], paymentTx: PaymentTx) => {
        if (paymentTx.type === PaymentInformationType.AUTH && paymentTx.status === PaymentInformationStatus.SUCCESS) {
          currentAuths.push(paymentTx);
        }
        if (
          (paymentTx.type === PaymentInformationType.CAPTURE || paymentTx.type === PaymentInformationType.VOID) &&
          paymentTx.status === PaymentInformationStatus.SUCCESS
        ) {
          const findIndex = currentAuths.findIndex((authOrSale) => {
            return authOrSale.id == paymentTx.parent?.id;
          });
          if (findIndex !== -1) {
            currentAuths.splice(findIndex, 1);
          }
        }
        return currentAuths;
      }, []);

    return allAuthsWithoutCaptureOrVoid?.length
      ? allAuthsWithoutCaptureOrVoid[allAuthsWithoutCaptureOrVoid.length - 1]
      : undefined;
  }

  /**
   * Find last success sale without void
   * @param paymentTxs PaymentTx[]
   * @returns PaymentTx | undefined
   */
  findLastSuccessSaleWithoutVoid(paymentTxs: PaymentTx[]): PaymentTx | undefined {
    const filteredSalePaymentTxs = paymentTxs
      .filter((item) => item.type === PaymentInformationType.SALE && item.status === PaymentInformationStatus.SUCCESS)
      .sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime());
    const filteredVoidPaymentTxs = paymentTxs.filter(
      (item) => item.type === PaymentInformationType.VOID && item.status === PaymentInformationStatus.SUCCESS,
    );
    const salesLength = filteredSalePaymentTxs.length;
    if (!salesLength) {
      return undefined;
    }
    const voidsLength = filteredVoidPaymentTxs.length;
    let lastSuccessSaleAudit: PaymentTx | undefined;
    // loop from last
    for (let index: number = salesLength - 1; index >= 0; index--) {
      const fileContent = filteredSalePaymentTxs[index];
      if (!voidsLength) {
        lastSuccessSaleAudit = fileContent;
        break;
      }
      if (filteredVoidPaymentTxs.every((voidTx) => voidTx.parent?.id !== fileContent.id)) {
        lastSuccessSaleAudit = fileContent;
        break;
      }
    }
    return lastSuccessSaleAudit;
  }

  /**
   * Find all auth without capture or void successfully, and all sale without void(for sale we call it "refund")
   * @param paymentTxs PaymentTx[]
   * @returns PaymentTx[] | undefined
   */
  findAllPreviousAuthAndSaleWithoutCaptureAndVoid(paymentTxs?: PaymentTx[], isAfterCaptured = false): PaymentTx[] {
    if (paymentTxs === undefined) return [];

    const auditsLength = paymentTxs.length;
    if (!auditsLength) {
      return [];
    }
    const allAuthAndSalesNeedToVoid: PaymentTx[] = [];
    const sortedPaymentTxs = paymentTxs.sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime());
    for (let index = 0; index <= auditsLength - 1; index++) {
      const paymentTx = sortedPaymentTxs[index];
      if (
        (paymentTx.type === PaymentInformationType.AUTH || paymentTx.type === PaymentInformationType.SALE) &&
        paymentTx.status === PaymentInformationStatus.SUCCESS
      ) {
        allAuthAndSalesNeedToVoid.push(paymentTx);
      }
      if (
        (paymentTx.type === PaymentInformationType.CAPTURE || paymentTx.type === PaymentInformationType.VOID) &&
        paymentTx.status === PaymentInformationStatus.SUCCESS
      ) {
        const findIndex = allAuthAndSalesNeedToVoid.findIndex((authOrSale) => {
          return authOrSale.id == paymentTx.parent?.id;
        });
        if (findIndex !== -1) {
          allAuthAndSalesNeedToVoid.splice(findIndex, 1);
        }
      }
    }

    if (isAfterCaptured) {
      return allAuthAndSalesNeedToVoid;
    }

    if (allAuthAndSalesNeedToVoid.length < 2) {
      return [];
    } else {
      allAuthAndSalesNeedToVoid.pop();
    }
    return allAuthAndSalesNeedToVoid;
  }

  /**
   * Find all sale with tranStatus=processing
   * @param paymentTxs PaymentTx[]
   * @returns PaymentTx[]
   */
  findAllSalesInProcessingStatus(paymentTxs?: PaymentTx[]): PaymentTx[] {
    if (paymentTxs === undefined) return [];
    const auditsLength = paymentTxs.length;
    if (!auditsLength) {
      return [];
    }
    return this.paymentFactoryService.findAllSalesInProcessingStatus(paymentTxs);
  }

  /**
   * Process Void
   * @param paymentTxs: PaymentTx[]
   */
  async processVoid(paymentTxs: PaymentTx[], requestedBy?: string): Promise<PaymentTx[]> {
    return this.paymentFactoryService.processVoid(paymentTxs, requestedBy);
  }

  /**
   * Process refund
   * @param paymentTx: PaymentTx
   * @param requestedBy: string
   * @param customAmount: number
   * @returns PaymentTx
   */
  async processRefund(paymentTx: PaymentTx, requestedBy?: string, customAmount?: number): Promise<PaymentTx> {
    return this.paymentFactoryService.processRefund(paymentTx, requestedBy, customAmount);
  }

  /**
   * Process Auths to Void
   * @param tx: Tx
   * @param paymentTxs: PaymentTx[]
   * @param requestedBy: string
   * @returns PaymentTx[]
   *
   */
  async processAuthsToVoid(tx: Tx, authsNeedToVoid: PaymentTx[], requestedBy?: string): Promise<PaymentTx[]> {
    if (!authsNeedToVoid || authsNeedToVoid.length === 0) return [];
    let voidedPaymentTxs;
    try {
      voidedPaymentTxs = await this.processVoid(authsNeedToVoid, requestedBy);
      const foundVoidedPaymentTxs = voidedPaymentTxs.filter((item) => item.status === PaymentInformationStatus.FAILURE);
      if (foundVoidedPaymentTxs && foundVoidedPaymentTxs.length) {
        await this.txTagRepository.addTagsToTx(tx, [
          TxTag.createTxTag(
            TxTagType.UNABLE_TO_VOID,
            tx.id,
            "SYSTEM",
            `Failed to void paymentTx: ${foundVoidedPaymentTxs.map((pTx) => pTx.id).join(",")}`,
          ),
        ]);
      }
    } catch (error: any) {
      await this.txTagRepository.addTagsToTx(tx, [
        TxTag.createTxTag(
          TxTagType.UNABLE_TO_VOID,
          tx.id,
          "SYSTEM",
          `Error when void paymentTx: "${error?.message ?? "UNKNOWN"}"`,
        ),
      ]);
      throw error;
    }

    return voidedPaymentTxs;
  }

  /**
   * Get payment_tx
   * @param paymentTx PaymentTx
   * @returns Paymenttx
   */
  async enquirePaymentTx(paymentTx: PaymentTx, requestedBy?: string): Promise<EnquiryResponse> {
    return this.paymentFactoryService.enquirePayment(paymentTx, requestedBy);
  }

  /**
   * Search payment_tx
   * @param paymentTx PaymentTx
   * @returns Paymenttx
   */
  async searchPaymentTx(paymentTx: PaymentTx, requestedBy?: string): Promise<EnquiryResponse> {
    return this.paymentFactoryService.searchPayment(paymentTx, requestedBy);
  }

  /**
   * Get payment_tx by id
   * @param paymentTxId string
   * @returns Paymenttx
   */
  async getPaymentTxById(paymentTxId: string, relations?: string[]): Promise<PaymentTx> {
    const foundPaymentTx = await this.paymentTxRepository.findOne({
      where: { id: Raw((alias: string) => `LOWER(${alias}) ILike '${paymentTxId}'`) },
      relations: ["tx", ...(relations ?? [])],
      loadEagerRelations: false,
    });
    if (!foundPaymentTx) {
      throw errorBuilder.payment.paymentTxNotFound(paymentTxId);
    }
    return foundPaymentTx;
  }

  /**
   * Check if paymentTx is still processing
   * @param gateway PaymentGatewayTypes
   * @param paymentInfo EnquiryResponse
   * @returns Paymenttx
   */
  isStillProcessing(gateway: PaymentGatewayTypes, paymentInfo: EnquiryResponse): boolean {
    return this.paymentFactoryService.isStillProcessing(gateway, paymentInfo);
  }

  /**
   * Void an authorised payment, Called in Portal
   * @param requestedBy string
   * @param paymentTxId string
   * @return PaymentTx
   */
  async voidPayment(paymentTxId: string, requestedBy?: string): Promise<PaymentTx> {
    const foundPaymentTx = await this.getPaymentTxById(paymentTxId, ["paymentInstrument"]);
    if (
      foundPaymentTx.type &&
      !(
        [
          PaymentInformationType.SALE,
          PaymentInformationType.AUTH,
          PaymentInformationType.CAPTURE,
          PaymentInformationType.REFUND,
        ].includes(foundPaymentTx.type) && foundPaymentTx.status === PaymentInformationStatus.SUCCESS
      )
    ) {
      throw errorBuilder.payment.paymentTxWrongTypeOrStatus(
        foundPaymentTx,
        [PaymentInformationType.AUTH, PaymentInformationType.SALE],
        PaymentInformationStatus.SUCCESS,
      );
    }
    const voidedPaymentTxs = await this.processAuthsToVoid(foundPaymentTx.tx, [foundPaymentTx], requestedBy);
    return voidedPaymentTxs[0];
  }

  /**
   * Refund an authorised payment, Called in Portal
   * @param requestedBy string
   * @param paymentTxId string
   * @return PaymentTx
   */
  async refundPayment(paymentTxId: string, requestedBy?: string, amount?: number): Promise<PaymentTx> {
    const foundPaymentTx = await this.getPaymentTxById(paymentTxId, ["paymentInstrument"]);
    return this.processRefund(foundPaymentTx, requestedBy, amount);
  }

  /**
   * Capture an authorised payment, Called in Portal
   * @param requestedBy string
   * @param paymentTxId string
   * @param paymentBodyDto PaymentBodyDto
   * @return PaymentTx
   */
  async capturePayment(paymentTxId: string, amount: number, requestedby: string): Promise<PaymentTx> {
    const foundPaymentTx = await this.getPaymentTxById(paymentTxId, ["paymentInstrument"]);
    if (
      !(
        foundPaymentTx.type === PaymentInformationType.AUTH &&
        foundPaymentTx.status === PaymentInformationStatus.SUCCESS
      )
    ) {
      throw errorBuilder.payment.paymentTxWrongTypeOrStatus(
        foundPaymentTx,
        [PaymentInformationType.AUTH],
        PaymentInformationStatus.SUCCESS,
      );
    }
    if (foundPaymentTx.amount && foundPaymentTx.amount < amount) {
      throw errorBuilder.payment.amountExceedsAuth(foundPaymentTx, amount);
    }
    try {
      return this.processCapture(foundPaymentTx, amount, requestedby);
    } catch (error: any) {
      await this.txTagRepository.addTagsToTx(foundPaymentTx.tx, [
        TxTag.createTxTag(TxTagType.UNABLE_TO_CAPTURE, foundPaymentTx.tx.id, "SYSTEM", error?.message ?? "UNKNOWN"),
      ]);
      throw error;
    }
  }

  @CorrelationContext()
  async processVoidJobs(now: Date): Promise<any> {
    this.logger.info("voidTxJobScheduler ", { now });
    const toBeVoids = await this.appDatabaseService
      .voidTxJobsRepository()
      .find(Filter.where("voided_at", "==", null), Filter.where("expect_void_at", "<=", new Date()));
    const paymentTxIdsNeedToBeVoid = toBeVoids.map((item) => item.id);
    this.logger.info("paymentTxIdsNeedToBeVoid", { paymentTxIdsNeedToBeVoid });

    const markJobAsVoided = async (paymentTxId: string, voidedAt: Date): Promise<void> => {
      await this.appDatabaseService.voidTxJobsRepository().collection.doc(paymentTxId).update({ voided_at: voidedAt });
    };

    const markJobForRetry = async (paymentTx: VoidTxJobDocument): Promise<void> => {
      await this.appDatabaseService
        .voidTxJobsRepository()
        .collection.doc(paymentTx.id)
        .update({
          retry_times: paymentTx.retryTimes + 1,
          expect_void_at: new Date(Date.now() + 1000 * 60 * 60),
        });
    };

    const processSingleVoidJob = async (paymentTx: VoidTxJobDocument) => {
      this.logger.info("start void", { id: paymentTx.id });
      try {
        const voidPaymentTx = await this.voidPayment(paymentTx.id);
        this.logger.info("voidPaymentTx status", { id: voidPaymentTx.id, status: voidPaymentTx.status });

        if (voidPaymentTx.status === PaymentInformationStatus.SUCCESS) {
          await markJobAsVoided(paymentTx.id, voidPaymentTx.createdAt);
        } else {
          const doesExistVoided = await this.paymentTxRepository
            .createQueryBuilder("paymentTx")
            .where("paymentTx.parentId = :parentId", {
              parentId: paymentTx.id,
            })
            .andWhere("paymentTx.type = :type", {
              type: PaymentInformationType.VOID,
            })
            .andWhere("paymentTx.status = :status", {
              status: PaymentInformationStatus.SUCCESS,
            })
            .getOne();
          this.logger.debug("doesExistVoided", { doesExistVoided });
          if (doesExistVoided) {
            this.logger.info("voidPaymentTx already voided before", { id: paymentTx.id });
            await markJobAsVoided(paymentTx.id, doesExistVoided.createdAt);
          } else {
            await markJobForRetry(paymentTx);
          }
        }
      } catch (error: any) {
        this.logger.error(`voidPayment error for ID ${paymentTx.id}: ${error.message || "Unknown error"}`, {
          errorName: error.name,
          errorStack: error.stack?.split("\n")[0] || "No stack trace",
        });
        await markJobForRetry(paymentTx);
      }
      this.logger.info("void finish", { id: paymentTx.id });
    };

    const concurrentLimit = 5;
    for (let i = 0; i < toBeVoids.length; i += concurrentLimit) {
      const batch = toBeVoids.slice(i, i + concurrentLimit);
      this.logger.info(
        `Processing batch ${Math.floor(i / concurrentLimit) + 1}/${Math.ceil(toBeVoids.length / concurrentLimit)}`,
        { batchSize: batch.length },
      );

      await Promise.all(batch.map((paymentTx) => processSingleVoidJob(paymentTx)));
    }

    this.logger.info("All void jobs processed", { totalCount: toBeVoids.length });
  }

  async voidPaymentWithTxId(txId: string): Promise<PaymentTx | null> {
    const tx = await this.txRepository.findOne({
      where: { id: txId },
      relations: ["paymentTx"],
    });

    for (const paymentTx of tx?.paymentTx ?? []) {
      try {
        await this.voidPayment(paymentTx.id, "SYSTEM");
      } catch (error: any) {
        this.logger.error(
          `Error voiding paymentTx ${paymentTx.id}: ${error.message || "Unknown error"}`,
          { paymentTx },
          error,
        );
        continue;
      }
    }

    return tx?.paymentTx?.[0] ?? null;
  }
}
