import { Modu<PERSON> } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";

import { AppDatabaseModule } from "../appDatabase/appDatabase.module";
import { ProfileAuditRepository } from "../database/repositories/profileAudit.repository";
import { UserModule } from "../user/user.module";

import { IdentityService } from "./identity.service";
import { IdentityController } from "./identity.controller";

@Module({
  providers: [IdentityService, IdentityController, ProfileAuditRepository],
  imports: [ConfigModule, AppDatabaseModule, UserModule],
  controllers: [IdentityController],
  exports: [IdentityService, IdentityController],
})
export class IdentityModule {}
