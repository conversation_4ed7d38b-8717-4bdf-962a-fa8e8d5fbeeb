import { Module } from "@nestjs/common";

import { PaymentTxRepository } from "../../database/repositories/paymentTx.repository";
import { TxRepository } from "../../database/repositories/tx.repository";

import { TransactionEventService } from "./transactionEvent.service";
import { TransactionEventHailingRequestModule } from "./transactionEventHailingRequest.module";

/**
 * Transaction module
 */
@Module({
  imports: [TransactionEventHailingRequestModule],
  providers: [PaymentTxRepository, TxRepository, TransactionEventService],
  exports: [TransactionEventService],
})
export class TransactionEventModule {}
