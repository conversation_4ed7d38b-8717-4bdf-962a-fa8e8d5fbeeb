import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";

import loggerUtils from "@nest/modules/utils/utils/logger.utils";

import TxEvent from "../../database/entities/txEvent.entity";
import { TxRepository } from "../../database/repositories/tx.repository";
import { AddEventBody } from "../../me/modules/meTransaction/dto/addEvent.dto";
import { errorBuilder } from "../../utils/utils/error.utils";
import { TxHailingRequest } from "../dto/tx.dto";
import { txEventTypeTxTypeMapping } from "../dto/txEventType.dto";
import { TxTypes } from "../dto/txType.dto";

import { TransactionEventHailingRequestService } from "./transactionEventHailingRequest.service";

/**
 * Transaction service
 */
@Injectable()
export class TransactionEventService {
  constructor(
    @InjectRepository(TxRepository) private txRepository: TxRepository,
    private transactionEventHailingRequest: TransactionEventHailingRequestService,
  ) {}

  /**
   * Add event
   * @param transactionId string
   * @param createdBy string
   * @param addEventBody AddEventBody
   * @returns Promise<TxEvent>
   */
  async addEvent(transactionId: string, createdBy: string, addEventBody: AddEventBody): Promise<TxEvent> {
    const tx = await this.txRepository.findOne({
      where: { id: transactionId },
      relations: ["txEvents", "paymentTx", "paymentTx.paymentInstrument", "txApp", "user", "merchant"],
    });

    if (!tx) {
      throw errorBuilder.transaction.notFound(transactionId);
    }

    if (tx.type !== txEventTypeTxTypeMapping[addEventBody.type]) {
      throw errorBuilder.transaction.eventTypeMismatch(
        transactionId,
        tx.type,
        txEventTypeTxTypeMapping[addEventBody.type],
      );
    }

    switch (tx.type) {
      case TxTypes.HAILING_REQUEST:
        return this.transactionEventHailingRequest.addHailingEvent(tx as TxHailingRequest, createdBy, addEventBody);
      default:
        // eslint-disable-next-line no-case-declarations
        const dashError = errorBuilder.transaction.eventTypeNotSupported(transactionId, tx.type);
        loggerUtils.error(
          `[transactionEvent.addEvent] ERROR - Event type not supported for tx type: ${tx.type}`,
          {
            transactionId,
            txType: tx.type,
            eventType: addEventBody.type,
          },
          dashError,
        );
        throw dashError;
    }
  }
}
