import { Module } from "@nestjs/common";

import { AppDatabaseModule } from "@nest/modules/appDatabase/appDatabase.module";
import { MeFleetTaxiModule } from "@nest/modules/me/modules/meFleetTaxi/meFleetTaxi.module";

import { MerchantRepository } from "../../database/repositories/merchant.repository";
import { PaymentTxRepository } from "../../database/repositories/paymentTx.repository";
import { TxRepository } from "../../database/repositories/tx.repository";
import { PaymentModule } from "../../payment/payment.module";
import { PubSubModule } from "../../pubsub/pubsub.module";
import { TransactionFactoryModule } from "../transactionFactory/transactionFactory.module";

import { TransactionEventHailingRequestService } from "./transactionEventHailingRequest.service";

/**
 * Transaction module
 */
@Module({
  imports: [PaymentModule, PubSubModule, TransactionFactoryModule, AppDatabaseModule, MeFleetTaxiModule],
  providers: [
    PaymentTxRepository,
    MerchantRepository,
    TxRepository,
    PaymentTxRepository,
    TransactionEventHailingRequestService,
    AppDatabaseModule,
  ],
  exports: [TransactionEventHailingRequestService],
})
export class TransactionEventHailingRequestModule {}
