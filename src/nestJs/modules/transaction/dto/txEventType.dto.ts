import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";

import { PlatformMerchantType } from "@nest/modules/database/entities/merchant.entity";

import TxEvent from "../../database/entities/txEvent.entity";

import { TxTypes } from "./txType.dto";

export enum TxEventType {
  HAILING_USER_CREATES_ORDER = "HAILING_USER_CREATES_ORDER",
  HAILING_USER_CANCELS_ORDER = "HAILING_USER_CANCELS_ORDER",
  HAILING_MERCHANT_ACCEPTS_ORDER = "HAILING_MERCHANT_ACCEPTS_ORDER",
  HAILING_MERCHANT_CANCELS_ORDER = "HAILING_MERCHANT_CANCELS_ORDER",
  HAILING_MERCHANT_PICK_UP_CONFIRMED = "HAILING_MERCHANT_PICK_UP_CONFIRMED",
  HAILING_SCHEDULED_ORDER_TIMEOUT = "HAILING_SCHEDULED_ORDER_TIMEOUT",
  HAILING_USER_UPDATES_ORDER = "HAILING_USER_UPDATES_ORDER",
  HAILING_ADMIN_CANCELS_ORDER = "HAILING_ADMIN_CANCELS_ORDER",
  HAILING_MERCHANT_ARRIVED_DESTINATION = "HAILING_MERCHANT_ARRIVED_DESTINATION",
  HAILING_ORDER_COMPLETED = "HAILING_ORDER_COMPLETED",
  HAILING_MERCHANT_APPROACHING_DESTINATION = "HAILING_MERCHANT_APPROACHING_DESTINATION",
}

export const txEventTypeTxTypeMapping: Record<TxEventType, TxTypes> = {
  [TxEventType.HAILING_USER_CREATES_ORDER]: TxTypes.HAILING_REQUEST,
  [TxEventType.HAILING_USER_CANCELS_ORDER]: TxTypes.HAILING_REQUEST,
  [TxEventType.HAILING_MERCHANT_ACCEPTS_ORDER]: TxTypes.HAILING_REQUEST,
  [TxEventType.HAILING_MERCHANT_CANCELS_ORDER]: TxTypes.HAILING_REQUEST,
  [TxEventType.HAILING_MERCHANT_PICK_UP_CONFIRMED]: TxTypes.HAILING_REQUEST,
  [TxEventType.HAILING_SCHEDULED_ORDER_TIMEOUT]: TxTypes.HAILING_REQUEST,
  [TxEventType.HAILING_USER_UPDATES_ORDER]: TxTypes.HAILING_REQUEST,
  [TxEventType.HAILING_ADMIN_CANCELS_ORDER]: TxTypes.HAILING_REQUEST,
  [TxEventType.HAILING_MERCHANT_ARRIVED_DESTINATION]: TxTypes.HAILING_REQUEST,
  [TxEventType.HAILING_ORDER_COMPLETED]: TxTypes.HAILING_REQUEST,
  [TxEventType.HAILING_MERCHANT_APPROACHING_DESTINATION]: TxTypes.HAILING_REQUEST,
};

export type TxEventContent =
  | HailingMerchantAcceptsOrderEventContent
  | HailingUserCancelsOrderEventContent
  | HailingMerchantPickUpConfirmedOrderEventContent
  | HailingAdminCancelsOrderEventContent
  | Record<string, any>;

export class Heartbeat {
  lat: number;
  lng: number;
  heading: number | null;
  speed: number | null;
}

/**
 * DTO for TxEvent response
 */
export class TxEventDto {
  @ApiProperty({ description: "The unique ID of the event" })
  id: string;

  @ApiProperty({ description: "The type of the event", enum: TxEventType })
  type: TxEventType;

  @ApiProperty({ description: "The user who created the event", required: false })
  createdBy?: string;

  @ApiProperty({ description: "Additional content for the event", type: Object, required: false })
  content?: TxEventContent;

  @ApiProperty({ description: "The timestamp when the event was created" })
  createdAt: Date;

  @ApiProperty({ description: "The timestamp when the event was last updated" })
  updatedAt: Date;
}

/**
 * HAILING_MERCHANT_ACCEPTS_ORDER
 */
export class HailingMerchantAcceptsOrderEventContent {
  @ApiProperty({ description: "Phone number", example: "1234567890" })
  phoneNumber: string;

  @ApiProperty({ description: "Driver's heartbeat when hail was accepted" })
  heartBeatOnAccepted?: Heartbeat;

  @ApiProperty({ description: "Meter", example: "DASH123" })
  meter: string;

  @ApiProperty({ description: "Fleet mock meter id", example: "FLEET123" })
  fleetMockMeterId?: string;

  @ApiProperty({ description: "Estimated distance in km from driver to pick-up point", example: 12.34 })
  distance?: number;

  @ApiProperty({ description: "Estimated time to arrival from driver to pick-up point", example: 300 })
  eta?: number;

  @ApiProperty({ description: "The boost amount when hail was accepted", example: 150 })
  boostAmount?: number;

  @ApiProperty({ description: "License plate", example: "DASH123" })
  licensePlate?: string;

  @ApiPropertyOptional({ description: "Platform merchant type", enum: PlatformMerchantType })
  platformMerchantType?: PlatformMerchantType;
}

export type HailingMerchantAcceptsOrderEvent = TxEvent & {
  type: TxEventType.HAILING_MERCHANT_ACCEPTS_ORDER;
  content: HailingMerchantAcceptsOrderEventContent;
};

/**
 * HAILING_MERCHANT_CANCELS_ORDER
 */
export type HailingMerchantCancelsOrderEvent = TxEvent & {
  type: TxEventType.HAILING_MERCHANT_CANCELS_ORDER;
};

/**
 * HAILING_SCHEDULED_ORDER_TIMEOUT
 */
export type HailingHailingScheduledOrderTimeoutOrderEvent = TxEvent & {
  type: TxEventType.HAILING_SCHEDULED_ORDER_TIMEOUT;
};

/**
 * HAILING_MERCHANT_ARRIVED_DESTINATION
 */

export type HailingMerchantArrivedDestinationEvent = TxEvent & {
  type: TxEventType.HAILING_MERCHANT_ARRIVED_DESTINATION;
};

/**
 * HAILING_ORDER_COMPLETED
 */

export type HailingOrderCompletedEvent = TxEvent & {
  type: TxEventType.HAILING_ORDER_COMPLETED;
};

/**
 * HAILING_MERCHANT_APPROACHING_DESTINATION
 */
export type HailingMerchantApproachingDestinationEvent = TxEvent & {
  type: TxEventType.HAILING_MERCHANT_APPROACHING_DESTINATION;
};

/**
 * HAILING_USER_CANCELS_ORDER
 */
export type HailingUserCancelsOrderEventContent = {
  charges: {
    cancellationFee: number;
    cancellationFeeBreakdown?: {
      total: number;
      driverPayout: number;
      dashFee: number;
    };
  };
};
export type HailingUserCancelsOrderEvent = TxEvent & {
  type: TxEventType.HAILING_USER_CANCELS_ORDER;
  content: HailingUserCancelsOrderEventContent;
};

/**
 * HAILING_USER_UPDATES_ORDER
 */
export type HailingUserUpdatesOrderEvent = TxEvent & {
  type: TxEventType.HAILING_USER_UPDATES_ORDER;
  content: Record<string, any>;
};

/**
 * HAILING_MERCHANT_PICK_UP_CONFIRMED
 */
export type HailingMerchantPickUpConfirmedOrderEventContent = {
  txId: string;
  meterId: string;
  fleetMockMeterId?: string;
};
export type HailingMerchantPickUpConfirmedOrderEvent = TxEvent & {
  type: TxEventType.HAILING_MERCHANT_PICK_UP_CONFIRMED;
  content: HailingMerchantPickUpConfirmedOrderEventContent;
};

export type HailingAdminCancelsOrderEventContent = {
  reason: string;
  charges?: {
    cancellationFee: number;
    cancellationFeeBreakdown?: {
      total: number;
      driverPayout: number;
      dashFee: number;
    };
  };
};

export type HailingAdminCancelsOrderEvent = TxEvent & {
  type: TxEventType.HAILING_ADMIN_CANCELS_ORDER;
  content: HailingAdminCancelsOrderEventContent;
};

export const isHailingUserCancelsOrderEvent = (event: TxEvent): event is HailingUserCancelsOrderEvent =>
  event.type === TxEventType.HAILING_USER_CANCELS_ORDER;

export const isHailingUserUpdatesOrderEvent = (event: TxEvent): event is HailingUserUpdatesOrderEvent =>
  event.type === TxEventType.HAILING_USER_UPDATES_ORDER;

export const isHailingMerchantAcceptsOrderEvent = (event: TxEvent): event is HailingMerchantAcceptsOrderEvent =>
  event.type === TxEventType.HAILING_MERCHANT_ACCEPTS_ORDER;

export const isHailingMerchantCancelsOrderEvent = (event: TxEvent): event is HailingMerchantCancelsOrderEvent =>
  event.type === TxEventType.HAILING_MERCHANT_CANCELS_ORDER;

export const isHailingMerchantPickUpConfirmedOrderEvent = (
  event: TxEvent,
): event is HailingMerchantPickUpConfirmedOrderEvent => event.type === TxEventType.HAILING_MERCHANT_PICK_UP_CONFIRMED;

export const isHailingHailingScheduledOrderTimeoutOrderEvent = (
  event: TxEvent,
): event is HailingHailingScheduledOrderTimeoutOrderEvent => event.type === TxEventType.HAILING_SCHEDULED_ORDER_TIMEOUT;

export const isHailingAdminCancelsOrderEvent = (event: TxEvent): event is HailingAdminCancelsOrderEvent =>
  event.type === TxEventType.HAILING_ADMIN_CANCELS_ORDER;

export const isHailingMerchantArrivedDestinationEvent = (
  event: TxEvent,
): event is HailingMerchantArrivedDestinationEvent => event.type === TxEventType.HAILING_MERCHANT_ARRIVED_DESTINATION;

export const isHailingOrderCompletedEvent = (event: TxEvent): event is HailingOrderCompletedEvent =>
  event.type === TxEventType.HAILING_ORDER_COMPLETED;

export const isHailingMerchantApproachingDestinationEvent = (
  event: TxEvent,
): event is HailingMerchantApproachingDestinationEvent =>
  event.type === TxEventType.HAILING_MERCHANT_APPROACHING_DESTINATION;
