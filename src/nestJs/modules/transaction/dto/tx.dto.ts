import Joi from "joi";

import { TripDocument } from "../../appDatabase/documents/trip.document";
import Tx from "../../database/entities/tx.entity";
import { merchantSchema } from "../../validation/dto/merchant.dto";

import {
  TxAdjustmentMetadata,
  TxHailingMetadata,
  TxPaymentInstrumentMetadata,
  txMetadataSchema,
} from "./txMetadata.dto";
import { txPayoutStatusSchema } from "./txPayoutStatus.dto";
import { TxTypes, txTypesSchema } from "./txType.dto";

/**
 * Derivated of Tx for trip transaction
 */
export class TxTrip extends Tx {
  type: TxTypes.TRIP;
  metadata: TripDocument;
}

/**
 * Derivated of Tx for fare adjustment transaction
 */
export class TxAdjustment extends Tx {
  type: TxTypes.TX_ADJUSTMENT;
  metadata: TxAdjustmentMetadata;
}

/**
 * Derivated of Tx for 3D Secure transaction
 */
export class Tx3DSecure extends Tx {
  type: TxTypes.CARD_VERIFICATION;
  metadata: TxPaymentInstrumentMetadata;
}

/**
 * Derivated of Tx for hailing request
 */
export class TxHailingRequest extends Tx {
  type: TxTypes.HAILING_REQUEST;
  metadata: TxHailingMetadata;
}

/**
 * TxTrip schema
 */
export const txSchema: Joi.Schema<Tx> = Joi.object<Tx>({
  type: txTypesSchema.required(),
  id: Joi.string().required(),
  metadata: txMetadataSchema.required(),
  merchant: merchantSchema.optional(),
  payoutStatus: txPayoutStatusSchema.optional(),
});

export const txIdSchema = Joi.string().uuid();

/**
 * function to check if the tx is a TxTrip
 * @param tx Tx
 * @returns TxTrip
 */
export const isTxTrip = (tx: Tx): tx is TxTrip => tx.type === TxTypes.TRIP;

/**
 * function to check if the tx is a TxHailingRequest
 * @param tx Tx
 * @returns TxHailingRequest
 */
export const isTxHailing = (tx: Tx): tx is TxHailingRequest => tx.type === TxTypes.HAILING_REQUEST;

/**
 * function to check if the tx is a TxAdjustment
 * @param tx Tx
 * @returns TxAdjustment
 */
export const isTxAdjustment = (tx: Tx): tx is TxAdjustment => tx.type === TxTypes.TX_ADJUSTMENT;

/**
 * function to check if the tx is a TxAdjustment
 * @param tx Tx
 * @returns TxAdjustment
 */
export const isTx3DSecure = (tx: Tx): tx is Tx3DSecure => tx.type === TxTypes.CARD_VERIFICATION;
