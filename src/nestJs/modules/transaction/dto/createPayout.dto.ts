import { ApiProperty } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";

import { BankNames, bankNameSchema } from "../../bank/dto/bankName.dto";

import { txIdSchema } from "./tx.dto";

export type MerchantInfo = {
  id: string;
  name: string;
  phoneNumber: string;
  bankAccount: string;
  bankAccountOwnerName: string;
  bankId: string;
  txIds: string[];
  total: number;
};

export class CreateAutoPayoutDto {
  @ApiProperty()
  bankName: BankNames;

  @ApiProperty()
  payoutMerchantId?: string;
}

export class CreatePayoutDto {
  @ApiProperty()
  txIds: string[];

  @ApiProperty()
  bankName: BankNames;
}

export const createAutoPayoutSchema = Joi.object<CreateAutoPayoutDto>({
  bankName: bankNameSchema.required(),
  payoutMerchantId: Joi.string().optional(),
}).required();

export const createPayoutSchema = Joi.object<CreatePayoutDto>({
  txIds: Joi.array().items(txIdSchema).required(),
  bankName: bankNameSchema.required(),
}).required();
