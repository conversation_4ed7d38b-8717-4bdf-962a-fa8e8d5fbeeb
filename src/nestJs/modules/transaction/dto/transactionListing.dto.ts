import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";

import Tx from "../../database/entities/tx.entity";
import { PaymentStatus } from "../../payment/dto/paymentStatus.dto";
import { PaymentType, paymentTypeSchema } from "../../payment/dto/paymentType.dto";
import { BaseListingQueryDto, baseListingQuerySchema } from "../../validation/dto/listingSchema.dto";

import { txIdSchema } from "./tx.dto";
import { TxPayoutStatus, txPayoutStatusSchema } from "./txPayoutStatus.dto";
import { TxTagType } from "./txTagType.dto";
import { TxTypes } from "./txType.dto";

export enum TxSortableType {
  TRIP_START = "tripStart",
  TRIP_END = "tripEnd",
  TRIP_TOTAL = "tripTotal",
  PAYMENT_STATUS = "paymentStatus",
  PAYOUT_STATUS = "payoutStatus",
  TYPE = "type",
  TOTAL = "total",
  CREATED_AT = "createdAt",
}

export enum CompareType {
  EQUAL_TO = "=",
  GREATER_THAN_OR_EQUAL_TO = ">=",
  LESS_THAN_OR_EQUAL_TO = "<=",
}

export enum MetadataFilterKey {
  TRIP_START = "tripStart",
  TRIP_END = "tripEnd",
  TOTAL = "total",
  FARE = "fare",
  EXTRA = "extra",
  DASHTIPS = "dashTips",
  LICENSE_PLATE = "licensePlate",
}

export const transactionListingSchema = baseListingQuerySchema<TransactionListingQueryDto>().keys({
  sort: Joi.string<TxSortableType>()
    .valid(...Object.values(TxSortableType))
    .optional(),

  txId: txIdSchema.trim().optional(),
  type: Joi.string<TxTypes>()
    .valid(...Object.values(TxTypes))
    .optional(),
  payoutStatus: txPayoutStatusSchema.optional().allow("NONE"),
  paymentStatus: Joi.string<PaymentStatus>()
    .valid(...Object.values(PaymentStatus))
    .optional(),
  metadataFilterKeys: Joi.array()
    .items(Joi.string<MetadataFilterKey>().valid(...Object.values(MetadataFilterKey)))
    .optional(),
  metadataFilterValues: Joi.array().items(Joi.alternatives().try(Joi.string(), Joi.number())).optional(),
  compareTypes: Joi.array()
    .items(Joi.string<CompareType>().valid(...Object.values(CompareType)))
    .optional(),
  paymentType: paymentTypeSchema.optional(),
  merchantPhone: Joi.string().trim().optional(),
  merchantId: Joi.string().uuid().optional(),
  merchantIds: Joi.array().items(Joi.string().uuid()).optional(),
  tag: Joi.string<TxTagType>()
    .valid(...Object.values(TxTagType))
    .optional()
    .allow("NONE"),
  total: Joi.number().positive().optional(),
  fromDate: Joi.date().optional(),
  toDate: Joi.date().optional(),
  userId: Joi.string().uuid().optional(),
  noDriver: Joi.boolean().optional(),
  noPayoutAmount: Joi.boolean().optional(),
  noTripEnd: Joi.boolean().optional(),
});

export class TransactionListingQueryDto extends BaseListingQueryDto {
  @ApiPropertyOptional()
  sort?: TxSortableType;

  // keys for filtering
  @ApiPropertyOptional()
  txId?: string;
  @ApiPropertyOptional()
  type?: TxTypes;
  @ApiPropertyOptional()
  payoutStatus?: TxPayoutStatus | "NONE";
  @ApiPropertyOptional()
  paymentStatus?: PaymentStatus;
  //metatada keys
  @ApiPropertyOptional()
  metadataFilterKeys?: MetadataFilterKey[];
  @ApiPropertyOptional()
  metadataFilterValues?: (number | string)[];
  @ApiPropertyOptional()
  compareTypes?: CompareType[];
  @ApiPropertyOptional()
  paymentType?: PaymentType;
  //merchant keys
  @ApiPropertyOptional()
  merchantPhone?: string;

  @ApiPropertyOptional()
  merchantId?: string;

  @ApiPropertyOptional()
  merchantIds?: string[];

  //tag keys
  @ApiPropertyOptional()
  tag?: TxTagType | "NONE";

  @ApiPropertyOptional()
  total?: number;

  @ApiPropertyOptional()
  fromDate?: string;

  @ApiPropertyOptional()
  toDate?: string;

  @ApiPropertyOptional()
  userId?: string;

  @ApiPropertyOptional()
  noDriver?: boolean; // Filter for transactions with no driver (metadata.driver.id is null or empty)

  @ApiPropertyOptional()
  noPayoutAmount?: boolean; // Filter for transactions with no payout amount (payoutAmount is null or zero)

  @ApiPropertyOptional()
  noTripEnd?: boolean; // Filter for transactions with no trip end (metadata.tripEnd is null or empty)
}

export class TransactionListingResponseDto {
  @ApiProperty()
  transactions: Tx[];

  @ApiProperty()
  count: number;
}
