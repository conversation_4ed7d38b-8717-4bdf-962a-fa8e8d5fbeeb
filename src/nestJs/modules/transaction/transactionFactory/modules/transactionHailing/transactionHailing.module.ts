import { Module } from "@nestjs/common";

import { CampaignModule } from "@nest/modules/campaign/campaign.module";

import { AppDatabaseModule } from "../../../../appDatabase/appDatabase.module";

import { TransactionHailingService } from "./transactionHailing.service";

/**
 * TransactionHailing module
 */
@Module({
  imports: [AppDatabaseModule, CampaignModule],
  providers: [TransactionHailingService],
  controllers: [],
  exports: [TransactionHailingService],
})
export class TransactionHailingModule {}
