import { Controller, Inject } from "@nestjs/common";
import { diff } from "deep-diff";
import { DocumentSnapshot, FieldValue, GeoPoint } from "firebase-admin/firestore";
import { Change, FirestoreEvent } from "firebase-functions/v2/firestore";

import { MeterDocument } from "@nest/modules/appDatabase/documents/meter.document";

import { TripDocument } from "../../../../appDatabase/documents/trip.document";
import { CorrelationContext } from "../../../../utils/context/decorators/correlation-context.decorator";
import LoggerServiceAdapter from "../../../../utils/logger/logger.service";
import { CamelToSnakeCaseNested } from "../../../../utils/utils/case/caseType.dto";
import { errorBuilder } from "../../../../utils/utils/error.utils";
import { isGreaterOrEqualThan } from "../../../../utils/utils/version.utils";

import { TripType } from "./types";
import { TripService } from "./trip.service";

/**
 * Trip controller
 */
@Controller()
export class TripController {
  constructor(private tripService: TripService, @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter) {}

  /**
   * Meter trip change event handler
   * @param change Change<DocumentSnapshot>
   * @param context EventContext
   * @returns Promise<string>
   */
  @CorrelationContext()
  async meterTripChange(
    event: FirestoreEvent<
      Change<DocumentSnapshot<CamelToSnakeCaseNested<TripDocument>>> | undefined,
      {
        tripId: string;
        meterId: string;
      }
    >,
  ) {
    const { tripId, meterId } = event.params;
    try {
      this.logger.info("tripController/meterTripChange-start", {
        tx: tripId,
        meterId,
        tripBefore: event.data?.before.data(),
        tripAfter: event.data?.after.data(),
      });
      const tripData = event.data?.after.data();
      if (
        (!tripData?.meter_software_version ||
          !isGreaterOrEqualThan(tripData?.meter_software_version, MeterDocument.newFlowMeterSoftwareVersion)) &&
        tripData?.is_dash_meter
      ) {
        this.logger.info(`nest trigger: meters/${meterId}/trips/${tripId} skipped`, {
          tx: tripId,
          data: { meter_software_version: tripData?.meter_software_version },
        });
        return;
      }

      const data = event.data?.after.data() as any;
      const difference = diff(event.data?.before.data(), event.data?.after.data());
      this.logger.info(`nest trigger: meters/${meterId}/trips/${tripId} changed`, {
        tx: tripId,
        data: { meter_software_version: tripData?.meter_software_version, difference },
      });
      if (!data) {
        throw errorBuilder.transaction.trip.meterChangeNoData();
      }
      this.logger.info(`current meters/${meterId}/trips/${tripId} data`, { data });
      if (!event.data?.before.data()?.trip_end && data.trip_end) {
        this.logger.debug("trip_end triggered");
        let dataToUpdate = {};
        if (data.type !== TripType.HAIL) {
          const discounts = await this.tripService.createDiscountsWhenTripEnd(tripId, data);
          if (!data?.billing?.discount_settings) {
            let discountSettings = {};
            if (discounts.thirdParty) {
              discountSettings = {
                ...discountSettings,
                discount_rules_third_party: discounts.thirdParty.campaign.discountRules,
              };
            }
            if (discounts.dash) {
              discountSettings = {
                ...discountSettings,
                discount_rules_dash: discounts.dash.campaign.discountRules,
              };
            }
            data.billing = {
              ...data.billing,
              discount_settings: discountSettings,
            };
          }
        }

        data.billing = this.tripService.generateBillingObject(data);
        dataToUpdate = { ...dataToUpdate, billing: data.billing };
        if (data.session && data.session.id && (!data.type || data.type !== "HAIL")) {
          try {
            let latitude = 0;
            let longitude = 0;
            if (data.location_end && !event.data?.before.data()?.location_end) {
              const geoPoint = data.location_end as GeoPoint;
              latitude = geoPoint.latitude;
              longitude = geoPoint.longitude;
            }
            const locationEnd = await this.tripService.convertLocation(
              meterId,
              tripId,
              "location_end",
              latitude,
              longitude,
              1,
              data.trip_itinerary as Record<string, any>[],
              data.app_user_language,
            );
            data.location_end_address = locationEnd.address;
            dataToUpdate = { ...dataToUpdate, location_end_address: locationEnd.address ?? "" };
            dataToUpdate = { ...dataToUpdate, trip_itinerary: locationEnd.trip_itinerary ?? [] };
          } catch (e) {
            this.logger.error(
              `nest trigger: meters/${meterId}/trips/${tripId} location_end conversion failed`,
              {
                tx: tripId,
              },
              e as Error,
            );
          }
        }
        await this.tripService.copyMeterTripToUserTrip(tripId, data);

        // dataToUpdate = { ...dataToUpdate, last_update_time: new Date() };
        if (!data["should_update_last_update_time"] && !data["shouldUpdateLastUpdateTime"]) {
          dataToUpdate = { ...dataToUpdate, last_update_time: new Date() };
        }

        dataToUpdate = {
          ...dataToUpdate,
          should_update_last_update_time: FieldValue.delete(),
          shouldUpdateLastUpdateTime: FieldValue.delete(),
        };
        this.logger.debug("trip_end triggered", dataToUpdate);
        await this.tripService.updateTripWhenTripEnd(meterId, tripId, dataToUpdate);
      } else {
        const before_last_update_time = event.data?.before.data()?.last_update_time;
        const after_last_update_time = data.last_update_time;
        if (
          !before_last_update_time ||
          (after_last_update_time && before_last_update_time && after_last_update_time > before_last_update_time)
        ) {
          //copy to user/trip if there is user_id
          this.logger.debug("dataAfter.last_update_time > dataBefore.last_update_time, copy");
          await this.tripService.copyMeterTripToUserTrip(tripId, data);
        }
      }
      const result = await this.tripService.meterTripChange(meterId, tripId, data, event.data?.before.data());

      this.logger.info("tripController/meterTripChange-end", { tx: tripId, meterId, result });

      return result;
    } catch (error) {
      this.logger.error(
        "tripController/meterTripChange-end",
        {
          tx: tripId,
          meterId,
        },
        error as Error,
      );
      throw error;
    }
  }

  /**
   * Meter trip create event handler
   * @param event FirestoreEvent<DocumentSnapshot>
   */
  @CorrelationContext()
  async meterTripCreate(
    event: FirestoreEvent<
      DocumentSnapshot<CamelToSnakeCaseNested<TripDocument>> | undefined,
      {
        meterId: string;
        tripId: string;
      }
    >,
  ) {
    const { meterId, tripId } = event.params;
    this.logger.info(`nest trigger: meters/${meterId}/trips/${tripId} created`, { tx: tripId });
    const data = event.data?.data();
    if (!data) {
      throw errorBuilder.transaction.trip.meterChangeNoData();
    }
    this.logger.info(`current meters/${meterId}/trips/${tripId} data`, { data });
    // if (data.location_start && data.session && data.session.id) {
    //   const geoPoint = data.location_start as GeoPoint;
    //   await this.tripService.convertLocation(
    //     meterId,
    //     tripId,
    //     "location_start",
    //     geoPoint.latitude,
    //     geoPoint.longitude,
    //     0,
    //   );
    // }
    return this.tripService.meterTripCreate(meterId, tripId, data);
  }
}
