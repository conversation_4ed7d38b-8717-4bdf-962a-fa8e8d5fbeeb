import Joi from "joi";

import { SessionDocument } from "@nest/modules/appDatabase/documents/session.document";

import { MeterTripDriver } from "../../../../../appDatabase/documents/meterTripDriver.document";
import {
  Location,
  TripDocumentPaymentInstrument,
  TripDocumentUser,
} from "../../../../../appDatabase/documents/trip.document";
import {
  PaymentMethodSelected,
  paymentMethodSelectedSchema,
} from "../../../../../payment/dto/paymentMethodSelected.dto";
import { PaymentStatus, paymentStatusSchema } from "../../../../../payment/dto/paymentStatus.dto";
import { PaymentType, paymentTypeSchema } from "../../../../../payment/dto/paymentType.dto";

import { TripStatus, tripStatusSchema } from "./tripStatus.dto";

interface PassengerInformationReceipt {
  phone: string;
}

export type TripDocumentReceipt = {
  id: string;
  passengerInformation: PassengerInformationReceipt;
  licensePlate: string;
  language: string;
  tripEnd: Date;
  lastUpdateTime: Date;

  dashFee?: number;
  dashFeeConstant?: number;
  dashFeeRate?: number;
  dashTipsEnable?: boolean;
  dashTips?: number;
  extra?: number;
  fare?: number;
  paymentType?: PaymentType;
  total?: number;
  tripTotal?: number;
  paymentStatus?: PaymentStatus;
  distance?: number;
  locationEnd?: Location;
  locationStart?: Location;
  locationEndAddress?: string;
  locationStartAddress?: string;
  tripStart?: Date;
  tripStatus?: TripStatus;
  waitTime?: number;
  driverId?: string;
  meterId?: string;
  sessionId?: string;
  showToDriver?: boolean;
  session?: SessionDocument;
  driver?: MeterTripDriver;
  isTipsCalculated?: boolean;
  paymentMethodSelected?: PaymentMethodSelected;
  paymentInstrument?: TripDocumentPaymentInstrument;

  tripInfoAppVersion?: string;
  meterSoftwareVersion: string;
  discountId?: string;
  discountRules?: string;
  bonusRules?: string;
  applicationRules?: string;
  discountIdDash?: string;
  discountRulesDash?: string;
  user?: TripDocumentUser;
};

export const tripDocumentReceiptSchema = Joi.object<TripDocumentReceipt>({
  id: Joi.string().required(),
  passengerInformation: Joi.object<PassengerInformationReceipt>().required(),
  licensePlate: Joi.string().required(),
  language: Joi.string().default("en").optional(),
  tripEnd: Joi.date().required(),
  lastUpdateTime: Joi.date().required(),
  meterSoftwareVersion: Joi.string().required(),

  tripInfoAppVersion: Joi.string().allow(null, "").optional(),
  dashFee: Joi.number().allow(null).optional(),
  dashFeeConstant: Joi.number().allow(null).optional(),
  dashFeeRate: Joi.number().allow(null).optional(),
  dashTipsEnable: Joi.boolean().allow(null).optional(),
  dashTips: Joi.number().allow(null).optional(),
  extra: Joi.number().allow(null).optional(),
  fare: Joi.number().allow(null).optional(),
  paymentType: paymentTypeSchema.optional(),
  total: Joi.number().allow(null).optional(),
  tripTotal: Joi.number().allow(null).optional(),
  paymentStatus: paymentStatusSchema.allow(null).optional(),
  distance: Joi.number().allow(null).optional(),
  locationEnd: Joi.object<Location>().allow(null).optional(),
  locationStart: Joi.object<Location>().allow(null).optional(),
  locationEndAddress: Joi.string().allow(null, "").optional(),
  locationStartAddress: Joi.string().allow(null, "").optional(),
  tripStart: Joi.date().allow(null).optional(),
  tripStatus: tripStatusSchema.optional(),
  waitTime: Joi.number().allow(null).optional(),
  driverId: Joi.string().allow(null).optional(),
  meterId: Joi.string().allow(null).optional(),
  sessionId: Joi.string().allow(null).optional(),
  showToDriver: Joi.boolean().allow(null).optional(),
  session: Joi.object<SessionDocument>().optional(),
  driver: Joi.object<MeterTripDriver>().optional(),
  isTipsCalculated: Joi.boolean().allow(null).optional(),
  paymentMethodSelected: paymentMethodSelectedSchema.allow(null).optional(),
  discountId: Joi.string().allow(null).optional(),
  discountRules: Joi.string().optional(),
  bonusRules: Joi.string().optional(),
  applicationRules: Joi.string().optional(),
  discountIdDash: Joi.string().allow(null).optional(),
  discountRulesDash: Joi.string().allow(null).optional(),
  user: Joi.object().optional(),
  paymentInstrument: Joi.object().optional(),
});
