import { Module, forwardRef } from "@nestjs/common";
import { ConfigModule, ConfigService } from "@nestjs/config";

import { AppDatabaseModule } from "../../../../appDatabase/appDatabase.module";
import { CampaignModule } from "../../../../campaign/campaign.module";
import { TxAppRepository } from "../../../../database/repositories/app.repository";
import { CampaignRepository } from "../../../../database/repositories/campaign.repository";
import { DiscountRepository } from "../../../../database/repositories/discount.repository";
import { MerchantRepository } from "../../../../database/repositories/merchant.repository";
import { MerchantNotificationTokenRepository } from "../../../../database/repositories/merchantNotificationToken.repository";
import { PaymentTxRepository } from "../../../../database/repositories/paymentTx.repository";
import { TxRepository } from "../../../../database/repositories/tx.repository";
import { UserRepository } from "../../../../database/repositories/user.repository";
import { UserNotificationTokenRepository } from "../../../../database/repositories/userNotificationToken.repository";
import { FcmModule } from "../../../../fcm/fcm.module";
import { FcmService } from "../../../../fcm/fcm.service";
import { LocationModule } from "../../../../location/location.module";
import { PaymentModule } from "../../../../payment/payment.module";
import { PubSubModule } from "../../../../pubsub/pubsub.module";
import { PubSubService } from "../../../../pubsub/pubsub.service";

import { TripService } from "./trip.service";
import { TripController } from "./trip.controller";

/**
 * Trip module
 */
@Module({
  imports: [
    PubSubModule,
    forwardRef(() => PaymentModule),
    ConfigModule,
    AppDatabaseModule,
    FcmModule,
    LocationModule,
    CampaignModule,
  ],
  providers: [
    TripService,
    PubSubService,
    ConfigService,
    AppDatabaseModule,
    TxRepository,
    TxAppRepository,
    PaymentTxRepository,
    FcmService,
    UserRepository,
    UserNotificationTokenRepository,
    DiscountRepository,
    MerchantRepository,
    MerchantNotificationTokenRepository,
    CampaignRepository,
  ],
  controllers: [TripController],
  exports: [TripService],
})
export class TripModule {}
