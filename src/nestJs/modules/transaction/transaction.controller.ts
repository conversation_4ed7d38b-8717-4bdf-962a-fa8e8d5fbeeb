import { <PERSON>, Get, Inject, Param, Req } from "@nestjs/common";
import { ApiHeader, ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { Request } from "express";
import { CloudEvent } from "firebase-functions/v2";
import { MessagePublishedData } from "firebase-functions/v2/pubsub";

import { AppDatabaseService } from "../appDatabase/appDatabase.service";
import Tx from "../database/entities/tx.entity";
import { PublishMessageForTripProcessingParams } from "../pubsub/dto/publishMessageForTripProcessing.dto";
import { CorrelationContext } from "../utils/context/decorators/correlation-context.decorator";
import LoggerServiceAdapter from "../utils/logger/logger.service";
import { apiTags } from "../utils/utils/swagger.utils";
import { JoiValidationPipe } from "../validation/validationPipe.service";
import { UserService } from "../user/user.service";

import { TxEventDto } from "./dto/txEventType.dto";
import { TransactionService } from "./transaction.service";
import { ReceiptLanguageType, receiptLanguageReplaceRegex, TxReceipt } from "./dto/txReceipt.dto";
import { txIdSchema } from "./dto/tx.dto";

/**
 * Transaction controller
 */
@Controller("transactions")
@ApiTags(...apiTags.public)
export class TransactionController {
  constructor(
    private transactionService: TransactionService,
    private readonly appDatabaseService: AppDatabaseService,
    private readonly userService: UserService,
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
  ) {}

  /**
   * Process transaction
   * @param context functions.EventContext
   * @param message functions.pubsub.Message
   * @returns Tx
   */
  @CorrelationContext()
  async processTransaction(
    event: CloudEvent<MessagePublishedData<PublishMessageForTripProcessingParams>>,
  ): Promise<Tx> {
    const data = event.data.message.json;
    this.logger.info("nest trigger: txProcessing", { tx: data.tx.id, messageId: event.data.message.messageId, data });
    return this.transactionService.processTransaction(data, event.data.message.messageId);
  }

  /**
   * Get transaction's receipt
   * @param txId string
   * @returns TxReceipt
   */
  @Get(":txId/receipt")
  @ApiOperation({ summary: "Get transaction by id for receipt" })
  @ApiHeader({
    name: "accept-language",
    description: "zh-HK or en-HK",
  })
  @ApiResponse({ status: 200, description: "Get transaction by id for receipt", type: TxReceipt })
  async getReceipt(
    @Param("txId", new JoiValidationPipe(txIdSchema.required())) txId: string,
    @Req() req: Request,
  ): Promise<TxReceipt> {
    const acceptLanguage = req.headers["accept-language"];
    const replacedLanguage = acceptLanguage?.replace(
      receiptLanguageReplaceRegex,
      ReceiptLanguageType.ENHK,
    ) as ReceiptLanguageType;
    const tx = await this.transactionService.getTransactionById(txId);
    if (!tx) {
      return this.appDatabaseService.tripRepository().getReceiptFromTrip(txId);
    }
    return this.transactionService.getReceiptByTx(tx, replacedLanguage);
  }

  /**
   * Get tx_event records by transaction ID
   * @param txId string
   * @returns A list of TxEvent objects
   */
  @Get(":txId/events")
  @ApiOperation({ summary: "Get tx_event records by transaction ID" })
  @ApiResponse({ status: 200, description: "A list of TxEvent objects", type: [TxEventDto] })
  @ApiResponse({ status: 404, description: "No tx_events found for the given txId" })
  async getTxEvents(@Param("txId", new JoiValidationPipe(txIdSchema.required())) txId: string): Promise<TxEventDto[]> {
    const txEvents = await this.transactionService.getTxEventsByTxId(txId);
    const userIds = Array.from(
      new Set(txEvents.map((event) => event.createdBy).filter((id) => id && !["SYSTEM", "ADMIN"].includes(id))),
    ) as string[];

    const userPhoneMap: Record<string, string> = {};
    if (userIds.length > 0) {
      const userPromises = userIds.map((id) => this.userService.getUserById(id));
      const users = await Promise.all(userPromises);
      users.forEach((user) => {
        if (user) {
          userPhoneMap[user.id] = user.phoneNumber;
        }
      });
    }
    return txEvents.map((event) => ({
      id: event.id,
      type: event.type,
      createdBy: event.createdBy && userPhoneMap[event.createdBy] ? userPhoneMap[event.createdBy] : event.createdBy,
      content: event.content,
      createdAt: event.createdAt,
      updatedAt: event.updatedAt,
    }));
  }
}
