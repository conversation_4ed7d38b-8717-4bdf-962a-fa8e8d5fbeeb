import { forwardRef, Module } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";

import { AppDatabaseModule } from "../appDatabase/appDatabase.module";
import { BankModule } from "../bank/bank.module";
import { DiscountRepository } from "../database/repositories/discount.repository";
import { MerchantRepository } from "../database/repositories/merchant.repository";
import { PaymentInstrumentRepository } from "../database/repositories/paymentInstument.repository";
import { PaymentTxRepository } from "../database/repositories/paymentTx.repository";
import { PayoutRepository } from "../database/repositories/payout.repository";
import { TxRepository } from "../database/repositories/tx.repository";
import { TxEventRepository } from "../database/repositories/txEvent.repository";
import { TxTagRepository } from "../database/repositories/txTag.repository";
import { UserRepository } from "../database/repositories/user.repository";
import { EmailModule } from "../email/email.module";
import { MessageTeamsModule } from "../messageTeams/messageTeams.module";
import { PaymentModule } from "../payment/payment.module";
import { PubSubModule } from "../pubsub/pubsub.module";
import { PubSubService } from "../pubsub/pubsub.service";
import { StorageModule } from "../storage/storage.module";
import { UserModule } from "../user/user.module";

import { TransactionEventModule } from "./modules/transactionEvent.module";
import { TransactionController } from "./transaction.controller";
import { TransactionService } from "./transaction.service";
import { TransactionFactoryModule } from "./transactionFactory/transactionFactory.module";

/**
 * Transaction module
 */
@Module({
  imports: [
    BankModule,
    StorageModule,
    PaymentModule,
    PubSubModule,
    TransactionFactoryModule,
    AppDatabaseModule,
    MessageTeamsModule,
    TransactionEventModule,
    EmailModule,
    forwardRef(() => UserModule),
  ],
  providers: [
    TransactionService,
    TxRepository,
    TxTagRepository,
    PayoutRepository,
    PubSubService,
    ConfigService,
    PaymentTxRepository,
    MerchantRepository,
    DiscountRepository,
    PaymentInstrumentRepository,
    TxEventRepository,
    UserRepository,
  ],
  controllers: [TransactionController],
  exports: [TransactionService],
})
export class TransactionModule {}
