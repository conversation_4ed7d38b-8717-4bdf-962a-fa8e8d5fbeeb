import { <PERSON>du<PERSON> } from "@nestjs/common";

import { AppDatabaseModule } from "@nest/modules/appDatabase/appDatabase.module";
import { UserRepository } from "@nest/modules/database/repositories/user.repository";
import { UserNotificationTokenRepository } from "@nest/modules/database/repositories/userNotificationToken.repository";
import { LoggerModule } from "@nest/modules/utils/logger/logger.module";

import { NotificationMethodsModule } from "../notification-method/notification-method.module";

import { AppUserNotificationService } from "./app-user-notification.service";

@Module({
  imports: [LoggerModule, AppDatabaseModule, NotificationMethodsModule],
  providers: [AppUserNotificationService, UserRepository, UserNotificationTokenRepository],
  exports: [AppUserNotificationService],
})
export class AppUserNotificationModule {}
