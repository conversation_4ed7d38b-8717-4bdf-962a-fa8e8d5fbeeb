import { Inject, Injectable } from "@nestjs/common";
import { getMessaging, Message } from "firebase-admin/messaging";

import NotificationHistory, { NotificationHistoryStatusEnum } from "../../database/entities/notificationHistory.entity";
import { NotificationHistoryRepository } from "../../database/repositories/notificationHistory.repository";
import { FcmConfig } from "../../fcm/fcm.config";
import { PreferredLanguageType } from "../../identity/dto/user.dto";
import LoggerServiceAdapter from "../../utils/logger/logger.service";
import { NotificationRequestDto } from "../dto/notification.dto";

import { INotificationService, INotificationUserTokenParam } from "./notification-method.interface";

type SuccessfulNotification = {
  status: NotificationHistoryStatusEnum.SUCCESS;
  messageId: string;
};

type FailedNotification = {
  status: NotificationHistoryStatusEnum.FAILURE;
  messageId?: string;
  failureReason: string;
};

type NotificationResult = SuccessfulNotification | FailedNotification;

// final localized message content
type NotificationMessage = {
  title: string;
  body: string;
  clickAction?: string;
};

@Injectable()
export class PushNotificationService implements INotificationService {
  constructor(
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
    private readonly notificationHistoryRepository: NotificationHistoryRepository,
  ) {}

  async notifyMany(tokens: INotificationUserTokenParam[], notification: NotificationRequestDto): Promise<string[]> {
    const promises = tokens.map((token) => this.notifyEach(token, notification));
    return Promise.all(promises);
  }

  async notify(token: INotificationUserTokenParam, notification: NotificationRequestDto): Promise<string> {
    return this.notifyEach(token, notification);
  }

  private async notifyEach(userToken: INotificationUserTokenParam, notification: NotificationRequestDto) {
    const userId = userToken.userId;

    const message: NotificationMessage = {
      title: notification.titleEn,
      body: notification.bodyEn,
    };
    if (userToken.preferredLanguage === PreferredLanguageType.ZHHK) {
      message.title = notification.titleHk;
      message.body = notification.bodyHk;
    }

    try {
      const messageId = await this.sendNotification(userToken.token, message);

      const notificationHistory = await this.createNotificationHistory(
        userToken,
        {
          messageId,
          status: NotificationHistoryStatusEnum.SUCCESS,
        },
        message,
      );

      return notificationHistory.id;
    } catch (err) {
      this.logger.error("Error sending notification", { userId }, err as Error);
      const notificationHistory = await this.createNotificationHistory(
        userToken,
        {
          status: NotificationHistoryStatusEnum.FAILURE,
          failureReason: (err as Error).message,
        },
        message,
      );
      return notificationHistory.id;
    }
  }

  private async sendNotification(token: string, notification: NotificationMessage): Promise<string> {
    const androidConfig = {
      ...FcmConfig.androidConfig,
      notification: {
        ...FcmConfig.androidConfig.notification,
        clickAction: notification.clickAction,
      },
    };

    const iosConfig = {
      ...FcmConfig.apnsConfig,
      payload: {
        ...FcmConfig.apnsConfig.payload,
        aps: {
          ...FcmConfig.apnsConfig.payload?.aps,
          category: notification.clickAction,
        },
      },
    };

    const message: Message = {
      notification: notification,
      android: androidConfig,
      apns: iosConfig,
      token: token,
    };

    return getMessaging().send(message);
  }

  private async createNotificationHistory(
    userToken: INotificationUserTokenParam,
    notificationResult: NotificationResult,
    notification: NotificationMessage,
  ): Promise<NotificationHistory> {
    const history = this.notificationHistoryRepository.create({
      title: notification.title,
      body: notification.body,
      userId: userToken.userId,
      userType: userToken.userType,
      metadata: {
        userTokenId: userToken.tokenId,
        messageId: notificationResult?.messageId,
      },
      status: notificationResult.status,
      failureReason:
        notificationResult.status === NotificationHistoryStatusEnum.FAILURE
          ? notificationResult.failureReason
          : undefined,
    });

    return this.notificationHistoryRepository.save(history);
  }
}
