import { <PERSON>du<PERSON> } from "@nestjs/common";

import { DatabaseModule } from "../database/database.module";
import { MerchantRepository } from "../database/repositories/merchant.repository";
import { MerchantNotificationTokenRepository } from "../database/repositories/merchantNotificationToken.repository";
import { NotificationHistoryRepository } from "../database/repositories/notificationHistory.repository";
import { NotificationTaskRepository } from "../database/repositories/notificationTask.repository";
import { UserRepository } from "../database/repositories/user.repository";
import { UserNotificationTokenRepository } from "../database/repositories/userNotificationToken.repository";
import { LoggerModule } from "../utils/logger/logger.module";

import { NotificationFactory } from "./notification-method/notification.factory";
import { UserNotificationFactory } from "./user-notification.factory";
import { NotificationMethodsModule } from "./notification-method/notification-method.module";
import { MerchantNotificationService } from "./merchant-notification/merchant-notification.service";
import { MerchantNotificationModule } from "./merchant-notification/merchant-notification.module";
import { AppUserNotificationService } from "./app-user-notification/app-user-notification.service";
import { AppUserNotificationModule } from "./app-user-notification/app-user-notification.module";

@Module({
  imports: [
    LoggerModule,
    DatabaseModule,
    NotificationMethodsModule,
    AppUserNotificationModule,
    MerchantNotificationModule,
  ],
  providers: [
    // from other modules
    UserRepository,
    UserNotificationTokenRepository,
    MerchantRepository,
    MerchantNotificationTokenRepository,

    // module repositories
    NotificationHistoryRepository,
    NotificationTaskRepository,

    // module services
    AppUserNotificationService,
    MerchantNotificationService,

    // module service delegators
    UserNotificationFactory,
    NotificationFactory,
  ],
  exports: [
    UserRepository,
    UserNotificationTokenRepository,
    MerchantRepository,
    MerchantNotificationTokenRepository,

    AppUserNotificationService,
    MerchantNotificationService,

    NotificationHistoryRepository,
    NotificationTaskRepository,

    NotificationFactory,
    UserNotificationFactory,
  ],
})
export class NotificationModule {}
