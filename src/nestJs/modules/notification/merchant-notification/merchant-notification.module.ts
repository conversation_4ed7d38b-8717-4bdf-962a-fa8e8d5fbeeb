import { Modu<PERSON> } from "@nestjs/common";

import { AppDatabaseModule } from "@nest/modules/appDatabase/appDatabase.module";
import { MerchantRepository } from "@nest/modules/database/repositories/merchant.repository";
import { MerchantNotificationTokenRepository } from "@nest/modules/database/repositories/merchantNotificationToken.repository";
import { LoggerModule } from "@nest/modules/utils/logger/logger.module";

import { NotificationMethodsModule } from "../notification-method/notification-method.module";

import { MerchantNotificationService } from "./merchant-notification.service";

@Module({
  imports: [LoggerModule, AppDatabaseModule, NotificationMethodsModule],
  providers: [MerchantNotificationService, MerchantRepository, MerchantNotificationTokenRepository],
  exports: [MerchantNotificationService],
})
export class MerchantNotificationModule {}
