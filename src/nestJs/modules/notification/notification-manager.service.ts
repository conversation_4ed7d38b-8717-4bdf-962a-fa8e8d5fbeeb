import { Inject, Injectable, InternalServerErrorException } from "@nestjs/common";
import { DataSource, FindManyOptions } from "typeorm";

import {
  CreateManyNotificationRequestDto,
  NotificationTaskRequestStatus,
  UpdateManyNotificationRequestDto,
} from "@nest/modules/cloud-task-notification-handler/dto/request.dto";
import NotificationTask, { NotificationTaskStatus } from "@nest/modules/database/entities/notificationTask.entity";
import { NotificationTaskRepository } from "@nest/modules/database/repositories/notificationTask.repository";
import LoggerServiceAdapter from "@nest/modules/utils/logger/logger.service";
import { errorBuilder } from "@nest/modules/utils/utils/error.utils";

import { PaginatedResponseDto } from "../utils/paginated.dto";

import {
  NotificationTaskFilterDto,
  NotificationTaskSortKey,
  NotificationTaskSortOrder,
} from "./dto/notification-filters.dto";

@Injectable()
export class NotificationManagerService {
  constructor(
    @Inject(LoggerServiceAdapter) protected readonly logger: LoggerServiceAdapter,
    protected readonly notificationTaskRepository: NotificationTaskRepository,
    protected readonly dataSource: DataSource,
  ) {}

  async getNotificationTasks(
    notificationTaskFilter: NotificationTaskFilterDto = {
      page: 1,
      pageSize: 10,
      sort: NotificationTaskSortOrder.DESC,
      sortBy: NotificationTaskSortKey.CREATED_AT,
    },
  ): Promise<PaginatedResponseDto<NotificationTask>> {
    try {
      const { page, pageSize, sort, sortBy, ...filters } = notificationTaskFilter;

      const findOptions: FindManyOptions<NotificationTask> = {
        where: { ...filters },
        order: { [sortBy]: sort },
        skip: (page - 1) * pageSize,
        take: pageSize,
      };

      const [tasks, count] = await this.notificationTaskRepository.findAndCount(findOptions);

      return {
        data: tasks,
        pagination: {
          count,
          page,
          pageSize,
        },
      };
    } catch (error) {
      this.logger.error("[AppUserNotificationService] Error fetching notification tasks", {}, error as Error);
      throw new InternalServerErrorException("Failed to fetch notification tasks");
    }
  }

  async createNotificationTask(
    createManyNotificationRequestDto: CreateManyNotificationRequestDto,
    createdBy: string,
    callback: (task: NotificationTask) => Promise<string>,
  ): Promise<NotificationTask> {
    const { scheduledAt, status, userType, ...payload } = createManyNotificationRequestDto;

    const notificationHistory = this.notificationTaskRepository.create({
      name: payload.name,
      type: payload.type,
      scheduledAt,
      createdBy,
      userType,
      status:
        createManyNotificationRequestDto.status === NotificationTaskRequestStatus.DRAFT
          ? NotificationTaskStatus.DRAFT
          : NotificationTaskStatus.SCHEDULED,
      payload,
    });

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const newNotificationHistory = await queryRunner.manager.save(notificationHistory);
      this.logger.info(`[NotificationManagerService] Created notification task id '${newNotificationHistory.id}'`);

      if (status === NotificationTaskRequestStatus.SCHEDULED) {
        const cloudTaskName = await callback(newNotificationHistory);
        newNotificationHistory.cloudTaskReference = cloudTaskName;
        await queryRunner.manager.save(newNotificationHistory);

        this.logger.info(
          `[NotificationManagerService] Created cloud task for notification task id ${newNotificationHistory.id}`,
        );
      }

      await queryRunner.commitTransaction();
      return newNotificationHistory;
    } catch (error) {
      this.logger.error(
        "[NotificationManagerService] Error creating notification task. Rolling back transaction...",
        {},
        error as Error,
      );

      await queryRunner.rollbackTransaction();

      throw errorBuilder.notification.failedToCreateTask(error);
    } finally {
      await queryRunner.release();
    }
  }

  // strict type ensures we are explicitly declaring all enumerable states of NotificationTaskRequestStatus
  private readonly editableRequestStatusMap: Record<NotificationTaskRequestStatus, NotificationTaskStatus> = {
    [NotificationTaskRequestStatus.DRAFT]: NotificationTaskStatus.DRAFT,
    [NotificationTaskRequestStatus.SCHEDULED]: NotificationTaskStatus.SCHEDULED,
  };

  async updateNotificationAfterCallback(
    taskId: string,
    updatedBy: string,
    updateManyNotificationRequestDto: UpdateManyNotificationRequestDto,
    callback: (task: NotificationTask) => Promise<string>,
  ): Promise<NotificationTask> {
    const task = await this.notificationTaskRepository.findOneBy({
      id: taskId,
    });

    if (!task) {
      this.logger.error(`[NotificationManagerService] Notification task not found for id: ${taskId}`);
      throw errorBuilder.notification.taskNotFound(taskId);
    }

    const { name, type, scheduledAt, status, userType, ...updatePayload } = updateManyNotificationRequestDto;

    // fail early if state change is not allowed
    if (status && task.status != this.editableRequestStatusMap[status]) {
      const newStatus = this.editableRequestStatusMap[status];
      if (!task.canTransition(newStatus)) {
        this.logger.error(
          `[NotificationManagerService] Cannot update task ${taskId} from status ${task.status} to status ${newStatus}`,
        );
        throw errorBuilder.notification.cannotUpdateTaskWithStatus(taskId, task.status);
      }

      task.transition(newStatus);
    }

    task.payload = Object.assign({}, task.payload, updatePayload, {
      updatedBy,
    });

    if (name) {
      task.name = name;
    }

    if (type) {
      task.type = type;
    }

    if (scheduledAt) {
      task.scheduledAt = scheduledAt;
    }

    if (userType) {
      task.userType = userType;
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      if (task.status === NotificationTaskStatus.SCHEDULED) {
        const newTaskName = await callback(task);
        task.cloudTaskReference = newTaskName;
      }

      const updatedTask = await queryRunner.manager.save(task);
      await queryRunner.commitTransaction();
      return updatedTask;
    } catch (err) {
      await queryRunner.rollbackTransaction();
      throw err;
    } finally {
      await queryRunner.release();
    }
  }

  async deleteNotificationAfterCallback(
    taskId: string,
    deletedBy: string,
    callback: (task: NotificationTask) => Promise<string>,
  ): Promise<NotificationTask> {
    const task = await this.notificationTaskRepository.findOneBy({
      id: taskId,
    });

    if (!task) {
      this.logger.error(`[NotificationManagerService] Notification task not found for id: ${taskId}`);
      throw errorBuilder.notification.taskNotFound(taskId);
    }

    // short circuit for idempotency
    if (task.status === NotificationTaskStatus.DELETED) {
      this.logger.info(`[NotificationManagerService] Notification task already deleted: ${taskId}`);
      return task;
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      task.payload = {
        ...task.payload,
        deletedBy,
      };
      task.forDeletion();
      await queryRunner.manager.save(task);

      if (task.cloudTaskReference) {
        await callback(task);
      }

      task.delete();
      const deletedTask = await queryRunner.manager.save(task);

      await queryRunner.commitTransaction();

      return deletedTask;
    } catch (err) {
      await queryRunner.rollbackTransaction();
      throw err;
    } finally {
      await queryRunner.release();
    }
  }
}
