import { Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";

import { CampaignModule } from "../campaign/campaign.module";
import { TxAppRepository } from "../database/repositories/app.repository";
import { PaymentTxRepository } from "../database/repositories/paymentTx.repository";
import { TxRepository } from "../database/repositories/tx.repository";
import { UserRepository } from "../database/repositories/user.repository";
import { LocationModule } from "../location/location.module";
import { PaymentModule } from "../payment/payment.module";
import { PubSubModule } from "../pubsub/pubsub.module";

import { HailingService } from "./hailing.service";

@Module({
  imports: [ConfigModule, PaymentModule, LocationModule, CampaignModule, PubSubModule],
  providers: [HailingService, PaymentTxRepository, TxRepository, TxAppRepository, UserRepository],
  exports: [HailingService],
})
export class HailingModule {}
