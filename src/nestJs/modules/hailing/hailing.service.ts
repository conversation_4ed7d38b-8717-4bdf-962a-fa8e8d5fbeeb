import { Inject, Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { InjectRepository } from "@nestjs/typeorm";
import axios from "axios";
import { DecodedIdToken } from "firebase-admin/auth";

import { AppDatabaseService } from "../appDatabase/appDatabase.service";
import { FleetVehicleType } from "../appDatabase/documents/fleetVehicleType.document";
import { TxAppsNames } from "../apps/dto/Apps.dto";
import { CampaignService } from "../campaign/campaign.service";
import Tx from "../database/entities/tx.entity";
import { TxAppRepository } from "../database/repositories/app.repository";
import { TxRepository } from "../database/repositories/tx.repository";
import { UserRepository } from "../database/repositories/user.repository";
import { LocalizedLanguage, LocationLanguage } from "../location/dto/location.dto";
import { LocationService } from "../location/location.service";
import { HailingCreateOrderBody } from "../me/modules/meHailing/dto/meHailing.dto";
import { ChannelTypes } from "../message/dto/channelType.dto";
import { TemplateTypesText } from "../message/dto/templateType.dto";
import { PaymentInformationStatus } from "../payment/dto/paymentInformationStatus.dto";
import { PaymentInformationType } from "../payment/dto/paymentInformationType.dto";
import { PaymentInstrumentState } from "../payment/modules/paymentInstrument/dto/paymentInstrument.dto";
import { PaymentService } from "../payment/payment.service";
import { PubSubService } from "../pubsub/pubsub.service";
import { TxHailingRequest } from "../transaction/dto/tx.dto";
import { TxHailingMetadata } from "../transaction/dto/txMetadata.dto";
import { TxTypes } from "../transaction/dto/txType.dto";
import LoggerServiceAdapter from "../utils/logger/logger.service";
import { errorBuilder } from "../utils/utils/error.utils";
import { roundUpOneDecimal } from "../utils/utils/number.utils";
import { UtilsService } from "../utils/utils.service";

import { UpdateDriverHailCompletedDto } from "./dto/updateHail.dto";
import { HailingItineraryStepResponse } from "./dto/hailing.dto";
import {
  HailingApiCreateOrderResponse,
  HailingApiGetPriceEstimationResponse,
  HailingApiBoostUpdateResponse,
  BoostUpdateResponse,
} from "./dto/hailing.api.dto";

@Injectable()
export class HailingService {
  constructor(
    private paymentService: PaymentService,
    private locationService: LocationService,
    readonly configService: ConfigService,
    private campaignService: CampaignService,
    @InjectRepository(TxRepository) private txRepository: TxRepository,
    @InjectRepository(TxAppRepository) private readonly txAppRepository: TxAppRepository,
    @InjectRepository(UserRepository) private readonly userRepository: UserRepository,
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
    private readonly pubsubService: PubSubService,
    @Inject(UtilsService) private readonly utilsService: UtilsService,
    private readonly appDatabaseService: AppDatabaseService,
  ) {}

  async createOrder(
    body: HailingCreateOrderBody,
    itinerary: HailingItineraryStepResponse[],
    user: DecodedIdToken,
    token: string,
  ): Promise<{ tx: TxHailingRequest; authedAmount: number }> {
    const appUser = await this.userRepository.findAppUserById(user.uid, ["paymentInstruments"]);
    const userHasHailRequests = await this.txRepository.exists({
      where: { user: { id: appUser.id }, type: TxTypes.HAILING_REQUEST },
    });

    this.logger.debug(
      userHasHailRequests
        ? `User ${appUser.id} has made previous hailing requests`
        : `User ${appUser.id} is making first hailing request`,
    );

    body.time = body.time ?? new Date();

    // Create Auth for the user
    const validPaymentInstrument = appUser.paymentInstruments.find((paymentInstrument) =>
      body.paymentInstrumentId
        ? paymentInstrument &&
          paymentInstrument.expirationDate > new Date() &&
          paymentInstrument.state === PaymentInstrumentState.VERIFIED &&
          paymentInstrument.id === body.paymentInstrumentId
        : paymentInstrument &&
          paymentInstrument.expirationDate > new Date() &&
          paymentInstrument.state === PaymentInstrumentState.VERIFIED &&
          paymentInstrument.isPreferred,
    );

    if (!validPaymentInstrument) {
      throw errorBuilder.payment.instrument.notFound(body.paymentInstrumentId ?? "preferred");
    }

    // Save order in SQL in Tx as new Tx type "HailingRequest"
    const txApp = await this.txAppRepository.appByNameOrCreate(TxAppsNames.TAPXI);
    const tx = Tx.fromHailingRequest(body, appUser, txApp);
    await this.txRepository.save(tx);

    const amountToAuth = Math.max(await this.getPriceEstimation(body, token), 20);
    const paymentTx = await this.paymentService.processAuth(tx, validPaymentInstrument, amountToAuth);

    paymentTx.requestedBy = "SYSTEM";

    if (
      !paymentTx ||
      paymentTx.type !== PaymentInformationType.AUTH ||
      paymentTx.status !== PaymentInformationStatus.SUCCESS
    ) {
      throw errorBuilder.payment.authFailed(paymentTx.gatewayResponse);
    }

    const tripEstimation = await this.locationService.computeRoutes({
      originPlaceId: body.itinerary[0].placeId,
      destinationPlaceId: body.itinerary[body.itinerary.length - 1].placeId,
      language: body.language === LocalizedLanguage.EN ? LocationLanguage.EN : LocationLanguage.ZH_HK,
    });

    let discountIdThirdParty: string | undefined;
    let discountRulesThirdParty: string | undefined;
    let discountIdDash: string | undefined;
    let discountRulesDash: string | undefined;
    // Create/apply discounts
    try {
      if (body.campaignIdThirdParty) {
        const discountThirdParty = await this.campaignService.applyCampaignByIdAndUserId(
          body.campaignIdThirdParty,
          tx,
          user.uid,
        );
        discountIdThirdParty = discountThirdParty.id;
        discountRulesThirdParty = discountThirdParty.campaign.discountRules;
      }
    } catch (e) {
      this.logger.error("[createOrder] Error applying third party campaign", { e });
    }
    try {
      if (body.campaignIdDash) {
        const discountDash = await this.campaignService.applyCampaignByIdAndUserId(body.campaignIdDash, tx, user.uid);
        discountIdDash = discountDash.id;
        discountRulesDash = discountDash.campaign.discountRules;
      }
    } catch (e) {
      this.logger.error("[createOrder] Error applying dash campaign", { e });
    }

    let fleetVehicle: FleetVehicleType | undefined;

    if (body.fleetVehicleType) {
      fleetVehicle = await this.appDatabaseService
        .fleetVehicleTypesRepository()
        .getFleetVehicleType(body.fleetVehicleType);

      if (!fleetVehicle) {
        throw errorBuilder.fleetTaxi.noFleetVehicleTypeFound(body.fleetVehicleType);
      }
    }

    // Call Hailing API to create the order
    const hailingResponse = await axios
      .post<HailingApiCreateOrderResponse>(
        `${this.configService.getOrThrow("HAILING_BASE_URL")}v1/hails`,
        {
          platformType: body.platformType,
          fleetPartnerKey: body.fleetPartnerKey,
          fleetVehicleTypeKey: body.fleetVehicleType,
          fleetVehicleType: fleetVehicle,
          fleetQuoteVehicleId: body.fleetQuoteVehicleId,
          txId: tx.id,
          vehiclePreferences: body.fleetVehicleClass ?? { STANDARD: [] },
          time: body.time,
          filters: body.options,
          tripEstimation,
          language: body.language,
          itinerary,
          paymentDetails: {
            cardPrefix: paymentTx.paymentInstrument?.cardPrefix,
            cardSuffix: paymentTx.paymentInstrument?.cardSuffix,
            cardType: paymentTx.paymentInstrument?.cardType,
          },
          prioritizeFavoriteDrivers: body.prioritizeFavoriteDrivers,
          operatingAreas: body.operatingAreas,
          doubleTunnelFee: body.doubleTunnelFee,
          applicableDiscounts: {
            discountIdThirdParty: discountIdThirdParty,
            discountRulesThirdParty: discountRulesThirdParty,
            discountIdDash: discountIdDash,
            discountRulesDash: discountRulesDash,
          },
        },
        {
          headers: {
            Authorization: token,
          },
        },
      )
      .catch((error) => {
        this.logger.error("hailingService/createOrder-end Error creating hailing order", { error });
        throw errorBuilder.dependency.failed(error, "Hailing API");
      });

    tx.metadata = {
      ...hailingResponse.data,
      charges: {
        cancellationFee: 0,
      },
      discounts: {
        discountIdThirdParty,
        discountRulesThirdParty,
        discountIdDash,
        discountRulesDash,
      },
      request: body,
    } as TxHailingMetadata;

    tx.paymentTx = [paymentTx];
    await this.txRepository.save(tx);

    if (!userHasHailRequests && appUser.phoneNumber) {
      try {
        await this.pubsubService.publishMessageForMessageProcessingParams({
          metadata: {
            schemeVersion: "1.0",
            createdAt: body.time ?? new Date(),
          },
          recipient: {
            phone: appUser.phoneNumber,
          },
          tranId: tx.id,
          channel: ChannelTypes.WHATSAPP,
          language: this.utilsService.language.getLanguageType(body.language),
          params: {
            amount: amountToAuth,
          },
          messageId: tx.id,
          template: TemplateTypesText.FIRST_HAIL_ORDER,
        });
      } catch (err) {
        this.logger.error("[createOrder] Error publishing message for message processing params", { err });
      }
    }

    return { tx: tx, authedAmount: amountToAuth };
  }

  async getPriceEstimation(body: HailingCreateOrderBody, token: string): Promise<number> {
    const request = {
      platformType: body.platformType,
      placeIds: body.itinerary.map((itinerary) => itinerary.placeId),
      time: body.time,
      language: body.language,
      sessionToken: body.sessionToken,
      vehiclePreferences: body.fleetVehicleClass,
      fleetVehicleType: body.fleetVehicleType,
      fleetQuoteVehicleId: body.fleetQuoteVehicleId,
      applicableCampaigns: {
        campaignIdThirdParty: body.campaignIdThirdParty,
        campaignIdDash: body.campaignIdDash,
        campaignRulesThirdParty: body.campaignRulesThirdParty,
        campaignRulesDash: body.campaignRulesDash,
      },
      doubleTunnelFee: body.doubleTunnelFee,
    };
    this.logger.info("HailingService/getPriceEstimation", { request });
    return axios
      .post<HailingApiGetPriceEstimationResponse>(
        `${this.configService.getOrThrow("HAILING_BASE_URL")}v1/pricing/estimated-total-fee-range`,
        request,
        {
          headers: {
            Authorization: token,
          },
        },
      )
      .then((response) => roundUpOneDecimal(response.data.max ?? 0))
      .catch((error) => {
        throw errorBuilder.dependency.failed(error, "Hailing API");
      });
  }

  async updateDriverHail(
    hailId: string,
    updateDriverHailDto: UpdateDriverHailCompletedDto,
    token: string,
  ): Promise<void> {
    try {
      await axios.patch(
        `${this.configService.getOrThrow("HAILING_BASE_URL")}v1/hails/${hailId}/drivers`,
        updateDriverHailDto,
        {
          headers: {
            Authorization: token,
          },
        },
      );
    } catch (error) {
      throw errorBuilder.dependency.failed(error, "Hailing API");
    }
  }

  async updateHailBoost(hailId: string, boostAmount: number, token: string): Promise<BoostUpdateResponse> {
    try {
      const response = await axios.patch<HailingApiBoostUpdateResponse>(
        `${this.configService.getOrThrow("HAILING_BASE_URL")}v1/hails/${hailId}/boost`,
        { boost_amount: boostAmount },
        {
          headers: {
            Authorization: token,
          },
        },
      );
      console.log("response", response.data);

      // Transform the response to match our expected format
      const data = response.data;
      return {
        success: data.success,
        totalBoostAmount: data.totalBoostAmount,
        addedBoostAmount: data.addedBoostAmount,
        message: data.message,
        updatedAt: data.updated_at,
      };
    } catch (error: any) {
      // Check if it's an axios error with response data
      if (error?.response?.status === 400 && error?.response?.data) {
        const errorData = error.response.data;

        // Check for specific hailing API error codes
        if (errorData.code === HAILING_API_ERROR_CODES.INVALID_BOOST_AMOUNT) {
          throw errorBuilder.hailing.invalidBoostAmount(errorData.description || "Invalid boost amount");
        }

        if (errorData.code === HAILING_API_ERROR_CODES.BOOST_LIMIT_EXCEEDED) {
          const data = errorData.data || {};
          throw errorBuilder.hailing.boostLimitExceeded(data.current_boost || 0, data.max_limit || 500);
        }

        if (errorData.code === HAILING_API_ERROR_CODES.NOT_UPDATABLE) {
          const data = errorData.data || {};
          throw errorBuilder.hailing.hailNotUpdatable(
            errorData.description?.match(/status (\w+)/)?.[1] || "unknown",
            data.hail_id || hailId,
          );
        }
      }

      // For any other errors, fall back to the generic dependency error
      throw errorBuilder.dependency.failed(error, "Hailing API");
    }
  }
}

const HAILING_API_ERROR_CODES = {
  INVALID_BOOST_AMOUNT: "HAIL__INVALID_BOOST_AMOUNT",
  BOOST_LIMIT_EXCEEDED: "HAIL__BOOST_LIMIT_EXCEEDED",
  NOT_UPDATABLE: "HAIL__NOT_UPDATABLE",
} as const;
