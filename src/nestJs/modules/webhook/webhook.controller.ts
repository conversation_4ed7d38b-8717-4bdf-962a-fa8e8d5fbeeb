import { Body, Controller, Delete, Get, Inject, Param, Patch, Post, Req, Res } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { AuthenticatedRequest, Response } from "express";

import { MerchantRoleData } from "../admin/adminAuthUser/dto/updateUserRole.dto";
import Webhook from "../database/entities/webhook.entity";
import {
  PublishMessageForWebhookProcessing,
  WebhookEventType,
} from "../pubsub/dto/PublishMessageForWebhookProcessing.dto";
import { CorrelationContext } from "../utils/context/decorators/correlation-context.decorator";
import LoggerServiceAdapter from "../utils/logger/logger.service";
import { apiTags } from "../utils/utils/swagger.utils";
import { JoiValidationPipe } from "../validation/validationPipe.service";

import { CreateWebhookRequestDto, createWebhookRequestSchema } from "./dto/createWebhookRequest.dto";
import { UpdateWebhookRequestDto, updateWebhookRequestSchema } from "./dto/updateWebhookRequest.dto";
import { WebhookResponseDto } from "./dto/webhookResponse.dto";
import { WebhookService } from "./webhook.service";

@ApiBearerAuth()
@Controller("webhooks")
@ApiTags(...apiTags.system)
export class WebhookController {
  constructor(
    private readonly webhookService: WebhookService,
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
  ) {}

  @Get()
  @ApiOperation({ summary: "Get all webhooks" })
  @ApiResponse({ status: 200, description: "Get all webhooks" })
  async getWebhooks(@Req() req: AuthenticatedRequest<MerchantRoleData>): Promise<WebhookResponseDto[]> {
    const merchantId = req.user.merchantId;
    this.logger.info(`Getting all webhooks for merchant: ${merchantId}`);
    const webhooks = await this.webhookService.getWebhooksByMerchantId(merchantId);
    return webhooks.map((webhook) => this.mapWebhookResponse(webhook));
  }

  @Post()
  @ApiOperation({ summary: "Create webhook" })
  @ApiResponse({ status: 201, description: "Register new webhook" })
  async registerWebhook(
    @Req() req: AuthenticatedRequest<MerchantRoleData>,
    @Body(new JoiValidationPipe(createWebhookRequestSchema)) registerWebhookRequestDto: CreateWebhookRequestDto,
  ): Promise<WebhookResponseDto> {
    this.logger.info("Registering new webhook", registerWebhookRequestDto);
    // TODO: implement api key middleware to get merchant id
    const merchantId = req.user.merchantId;
    const webhook = await this.webhookService.createWebhook(merchantId, registerWebhookRequestDto);
    return this.mapWebhookResponse(webhook);
  }

  @Patch(":id")
  @ApiOperation({ summary: "Update webhook" })
  @ApiResponse({ status: 200, description: "Update webhook" })
  async updateWebhook(
    @Param("id") webhookId: string,
    @Req() req: AuthenticatedRequest<MerchantRoleData>,
    @Body(new JoiValidationPipe(updateWebhookRequestSchema)) updateWebhookRequestDto: UpdateWebhookRequestDto,
  ): Promise<WebhookResponseDto> {
    this.logger.info("Updating webhook", updateWebhookRequestDto);
    // TODO: implement api key middleware to get merchant id
    const merchantId = req.user.merchantId;
    const webhook = await this.webhookService.updateWebhook(webhookId, merchantId, updateWebhookRequestDto);
    return this.mapWebhookResponse(webhook);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete webhook" })
  @ApiResponse({ status: 200, description: "Delete webhook" })
  async deleteWebhook(
    @Param("id") webhookId: string,
    @Req() req: AuthenticatedRequest<MerchantRoleData>,
    @Res() res: Response,
  ): Promise<void> {
    this.logger.info(`Deleting webhook: ${webhookId}`);
    this.webhookService.deleteWebhook(webhookId, req.user.merchantId);
    res.sendStatus(200);
  }

  @Get("events")
  @ApiOperation({ summary: "Get all possible webhook events" })
  @ApiResponse({ status: 200, description: "Get all possible webhook events" })
  async getWebhookEventTypes(): Promise<WebhookEventType[]> {
    return Object.values(WebhookEventType);
  }

  @CorrelationContext()
  async processWebhookMessage(message: PublishMessageForWebhookProcessing) {
    return this.webhookService.processWebhookMessage(message);
  }

  private mapWebhookResponse(webhook: Webhook): WebhookResponseDto {
    return {
      id: webhook.id,
      url: webhook.url,
      signatureKeyId: webhook.signatureKey.id,
      events: webhook.events?.map((event) => event.type) ?? [],
    };
  }
}
