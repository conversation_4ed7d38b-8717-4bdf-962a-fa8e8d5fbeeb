import Joi from "joi";

import PaymentTx from "../../database/entities/paymentTx.entity";
import Tx from "../../database/entities/tx.entity";
import { merchantSchema } from "../../merchant/dto/merchant.dto";

import {
  baseTxSchemaForPublishMessage,
  basePaymentTxSchemaForPublishMessage,
  baseTxTagSchemaForPublishMessage,
} from "./publishMessageForCaptureProcessingParams.dto";

/**
 * Params for publish message for void-processing
 */
export interface PublishMessageForVoidProcessingParams {
  tx: Tx;
  paymentTxs: PaymentTx[];
}

/**
 * Joi Publish message for void-processing schema
 */
export const publishMessageForVoidProcessingSchema = Joi.object<PublishMessageForVoidProcessingParams>({
  tx: baseTxSchemaForPublishMessage
    .keys({
      paymentTx: Joi.array()
        .items(
          basePaymentTxSchemaForPublishMessage.keys({
            parent: basePaymentTxSchemaForPublishMessage.allow(null).optional(),
          }),
        )
        .optional(),
      txTag: Joi.array().items(baseTxTagSchemaForPublishMessage).optional(),
      merchant: merchantSchema.keys({ txs: Joi.array().items(Joi.object().optional()).optional() }).optional(),
      payoutMerchant: merchantSchema.keys({ txs: Joi.array().items(Joi.object().optional()).optional() }).optional(),
      user: Joi.object().optional(),
    })
    .required(),
  paymentTxs: Joi.array()
    .items(
      basePaymentTxSchemaForPublishMessage.keys({
        parent: basePaymentTxSchemaForPublishMessage.allow(null).optional(),
      }),
    )
    .required(),
});
