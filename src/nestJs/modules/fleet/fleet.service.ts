import { Injectable } from "@nestjs/common";

import { AppDatabaseService } from "../appDatabase/appDatabase.service";
import { errorBuilder } from "../utils/utils/error.utils";

import { FleetVehicleClass } from "./dto/fleet.dto";

@Injectable()
export class FleetService {
  constructor(private readonly appDatabaseService: AppDatabaseService) {}

  async verifyFleetVehicleClass(fleetVehicleClass: FleetVehicleClass): Promise<boolean> {
    const fleetIds = Object.values(fleetVehicleClass).reduce((result: string[], fleetIds) => {
      return fleetIds.reduce((acc: string[], fleetId) => {
        if (!acc.includes(fleetId)) {
          return [...acc, fleetId];
        }
        return acc;
      }, result);
    }, []);

    const errors = (
      await Promise.all(
        fleetIds.map((fleetId) =>
          this.appDatabaseService
            .fleetRepository()
            .findOneById(fleetId)
            .then((fleet) => {
              return fleet ? undefined : `Fleet ${fleetId} not found`;
            }),
        ),
      )
    ).filter(Boolean);

    if (errors.length > 0) {
      throw errorBuilder.validation.failed(errors.join(", "), { errors });
    }

    return errors.length === 0;
  }
}
