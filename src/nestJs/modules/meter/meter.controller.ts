import { Body, Controller, Get, Inject, Injectable, Param, Patch, Req, Scope, UseInterceptors } from "@nestjs/common";
import { SchedulerRegistry } from "@nestjs/schedule";
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { Request } from "express";
import { DocumentSnapshot, FirestoreEvent } from "firebase-functions/v2/firestore";
import Joi from "joi";

import { MeterSecurityDocument, SecurityType } from "../appDatabase/documents/meterSecurity.document";
import { TripDocument } from "../appDatabase/documents/trip.document";
import { PublishMessageForHeartbeatProcessing } from "../pubsub/dto/publishMessageForHeartbeatProcessing.dto";
import { CorrelationContext } from "../utils/context/decorators/correlation-context.decorator";
import LoggerServiceAdapter from "../utils/logger/logger.service";
import { errorBuilder } from "../utils/utils/error.utils";
import { apiTags } from "../utils/utils/swagger.utils";
import { JoiValidationPipe } from "../validation/validationPipe.service";

import { MeterService } from "./meter.service";
import { UpdateTimeoutInterceptor } from "./interceptors/updateTimeout.interceptor";
import { MeterTripBodyDto, meterTripRequestSchema } from "./dto/meterTripRequest.dto";

@ApiBearerAuth()
@Controller("meters")
@ApiTags(...apiTags.meter)
@Injectable({ scope: Scope.REQUEST })
export class MeterController {
  constructor(
    private readonly meterService: MeterService,
    private readonly schedulerRegistry: SchedulerRegistry,
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
  ) {}

  @Get(":meterId/trips/:tripId")
  @ApiOperation({ summary: "Get meter trip by id" })
  @ApiResponse({ status: 200, description: "Get meter trip by id", type: TripDocument })
  async findMeterTripById(@Param("meterId") meterId: string, @Param("tripId") tripId: string): Promise<TripDocument> {
    const trip = await this.meterService.findMeterTripById(meterId, tripId);
    if (!trip) throw errorBuilder.meter.tripNotFound(meterId, tripId);
    delete trip.session;
    delete trip.paymentInformation;
    delete trip.paymentStatus;
    delete trip.paymentType;
    return trip;
  }

  @Patch(":meterId/trips/:tripId/tip")
  @ApiOperation({ summary: "Send tip to meter trip by id" })
  @ApiResponse({ status: 200, description: "Send tip to meter trip by id", type: TripDocument })
  @UseInterceptors(UpdateTimeoutInterceptor)
  async updateMeterTripTipById(
    @Param("meterId", new JoiValidationPipe(Joi.string().required())) meterId: string,
    @Param("tripId", new JoiValidationPipe(Joi.string().required())) tripId: string,
    @Body(new JoiValidationPipe(meterTripRequestSchema)) meterTripBodyDto: MeterTripBodyDto,
    @Req() req: Request,
  ): Promise<TripDocument> {
    const user = req.user;
    if (!user) {
      throw errorBuilder.user.missing();
    }
    await this.meterService.lockTx(tripId, user.user_id);
    const updatedTrip = await this.meterService.updateMeterTripById(
      meterId,
      tripId,
      meterTripBodyDto.tip,
      meterTripBodyDto.paymentMethodSelected,
    );
    this.logger.debug(
      `update MeterTrip with meterId: ${meterId}, tripId: ${tripId}, dashTips: ${updatedTrip.dashTips}, isTipsCalculated: ${updatedTrip.isTipsCalculated} successfully`,
    );

    return this.startCheckIsTipsCalculatedInterval(meterId, tripId);
  }

  async startCheckIsTipsCalculatedInterval(meterId: string, tripId: string): Promise<TripDocument> {
    return new Promise((resolve, reject) => {
      const intervalCallback = async () => {
        const trip = await this.meterService.findMeterTripById(meterId, tripId);
        if (!trip) {
          this.deleteCheckIsTipsCalculatedInterval(meterId, tripId);
          reject(errorBuilder.meter.tripNotFound(meterId, tripId));
          return;
        }
        this.logger.debug(
          `check Meter Trip with meterId: ${meterId}, tripId: ${tripId}, dashTips: ${trip.dashTips}, isTipsCalculated: ${trip.isTipsCalculated}`,
        );
        if (trip.isTipsCalculated) {
          this.deleteCheckIsTipsCalculatedInterval(meterId, tripId);
          resolve(trip);
          return;
        }
      };

      const interval = setInterval(intervalCallback, 200);
      this.schedulerRegistry.addInterval(`checkIsTipsCalculatedInterval_${meterId}_${tripId}`, interval);
    });
  }

  async meterCreateEvent(
    event: FirestoreEvent<
      DocumentSnapshot | undefined,
      {
        meterId: string;
      }
    >,
  ) {
    const meterId = event.params.meterId;
    this.logger.info(`Meter created with meterId: ${meterId}`);
    await this.meterService.createSecurityByMeterId(meterId, SecurityType.TOTP);
  }

  deleteCheckIsTipsCalculatedInterval(meterId: string, tripId: string) {
    this.schedulerRegistry.deleteInterval(`checkIsTipsCalculatedInterval_${meterId}_${tripId}`);
  }

  /**
   * Get meter secret by meter id
   * @param meterId
   * @param securityType SecurityType enum
   * @returns meterSecretDocument
   */
  @Get(":meterId/security/:securityType")
  @ApiOperation({ summary: "Get meter security by meter id" })
  @ApiResponse({ status: 200, description: "Get meter security by meter id", type: MeterSecurityDocument })
  async getLatestSecurityByMeterId(
    @Param("meterId", new JoiValidationPipe(Joi.string().required())) meterId: string,
    @Param("securityType", new JoiValidationPipe(Joi.string().required())) securityType: SecurityType,
  ): Promise<MeterSecurityDocument> {
    const security = await this.meterService.getLatestSecurityByMeterIdAndType(meterId, securityType);
    return security;
  }

  @CorrelationContext()
  async processMeterHeartbeat(heartbeat: PublishMessageForHeartbeatProcessing) {
    return this.meterService.processMeterHeartbeat(heartbeat);
  }
}
