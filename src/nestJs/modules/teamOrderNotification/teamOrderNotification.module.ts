import { Modu<PERSON> } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";

import { AppDatabaseModule } from "../appDatabase/appDatabase.module";
import { CloudTaskClientModule } from "../cloud-task-client/cloud-task-client.module";
import { PaymentTxRepository } from "../database/repositories/paymentTx.repository";
import { TxRepository } from "../database/repositories/tx.repository";
import { TeamsWebhookModule } from "../teams-webhook/teams-webhook.module";

import { TeamOrderNotificationService } from "./teamOrderNotification.service";
import { TeamOrderNotificationController } from "./teamOrderNotification.controller";

@Module({
  imports: [TeamsWebhookModule, ConfigModule, CloudTaskClientModule, AppDatabaseModule],
  providers: [TeamOrderNotificationService, TxRepository, PaymentTxRepository],
  controllers: [TeamOrderNotificationController],
  exports: [TeamOrderNotificationService],
})
export class TeamOrderNotificationModule {}
