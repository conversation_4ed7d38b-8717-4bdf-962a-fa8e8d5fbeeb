import { Controller, Get, Param } from "@nestjs/common";
import * as functions from "firebase-functions";

import { Configuration } from "../model/configuration";

import { ConfigurationService } from "./configuration.service";

@Controller("configuration")
/**
 * ConfigurationController
 */
export class ConfigurationController {
  /**
   * constructor
   * @param {ConfigurationService} configurationService
   */
  constructor(private configurationService: ConfigurationService) {}
  /**
   * findAll
   * @param {string} system
   * @return {string} system
   */
  @Get("/:system")
  async getConfigurationBySystem(@Param("system") system: string): Promise<Configuration | undefined> {
    functions.logger.info("get config %s", system);
    return await this.configurationService.getConfigurationBySystem(system);
  }

  /**
   * findAll
   * @param {string} system
   * @param {string} id
   * @return {string} system
   */
  @Get("/:system/:id")
  async getConfigurationById(@Param("system") system: string, @Param("id") id: string): Promise<any> {
    functions.logger.info("get config %s, %s", system, id);
    return await this.configurationService.getConfigurationByKey(system, id);
  }
}
