import * as functions from "firebase-functions";
import readXlsxFile from "read-excel-file/node";

import repo from "@legacy/repository/repo";

import { BankResponseLineItem, BankResponseLineItemStatus } from "../model/batch/bank-response";
import { NotificationType } from "../model/notification";
import { PayoutInformationAudit, PayoutInformationAuditType, PayoutStatus, Trip } from "../model/trip";
import NotificationService from "../service/notification";

import StorageService from "./storage";
import { db } from "./driver";

/**
 * BankFileService
 * A xlsx is given back to us and we need to process it
 */
class BankFileService {
  /**
   * processResponseFile
   * @param {string} path path to the file
   */
  async processResponseFile(path: string) {
    functions.logger.info("processResponseFile %s", path);
    const buffer = await StorageService.getFile(path);
    const data = await readXlsxFile(buffer, {
      schema,
      // A transformer to filter out rows that has col 0, 1 empty
      // remaining rows are required to process
      transformData(data) {
        return data.filter((row) => row[0] != null && row[1] != null);
      },
    });
    for (const row of data.rows) {
      const bankResponseLineItem = row as unknown as BankResponseLineItem;
      functions.logger.info(JSON.stringify(bankResponseLineItem));
      // Check status and process accordingly
      if (bankResponseLineItem.status == BankResponseLineItemStatus.COMPLETED) {
        functions.logger.info("completed line found, driverId: %s", bankResponseLineItem.driverId);
        const batchId = batchFileNameToId(bankResponseLineItem.originalBatchFileName);
        functions.logger.debug("batchId: %s", batchId);
        const batchRef = repo.batch.doc(batchId);
        const batch = await batchRef.get();
        functions.logger.info("original batch doc%s:", JSON.stringify(batch));
        const batchResult = (await batch).data()?.result || [];
        // find driver by id
        functions.logger.info("original batch result %s:", JSON.stringify(batchResult));
        for (const funeReleaseItem of batchResult) {
          if (funeReleaseItem.driverId == bankResponseLineItem.driverId) {
            functions.logger.info("found driver in batch %s:", funeReleaseItem.driverId);
            // update all trips
            db.runTransaction(async (tx) => {
              const refList = funeReleaseItem.trips.map((id) => repo.trip.doc(id));
              const tripDocs = await tx.getAll(...refList);
              for (const trip of tripDocs) {
                const payoutInformation = trip.data()?.payoutInformation || [];
                functions.logger.info(" ", trip.id);
                functions.logger.info("payoutInformation: %s", JSON.stringify(payoutInformation));
                // to-do: need to check if current status is allow to perform
                // pre-release request, e.g. if it is already released,
                // cannot release again.
                const releaseAudit: PayoutInformationAudit = {
                  type: PayoutInformationAuditType.RELEASE_REQUEST,
                  createdOn: new Date(),
                };
                payoutInformation.push(releaseAudit);
                const updatTripDoc: Partial<Trip> = {
                  payoutInformation: payoutInformation,
                  payoutStatus: PayoutStatus.RELEASED,
                };
                // cannot use update as converter won't work, so need to use set
                functions.logger.info("Update trip... ");
                tx.set(repo.trip.doc(trip.id), updatTripDoc, { merge: true });
              }
            });
            functions.logger.info("send notfication to %s", funeReleaseItem.driverId);
            NotificationService.sendNotification(
              funeReleaseItem.driverId,
              "Your payout is ready",
              "Please check DASH Driver app",
              NotificationType.PAYOUT,
              "zh-hk",
            );
            break;
          }
        }
      } else if (bankResponseLineItem.status == BankResponseLineItemStatus.REJECTED) {
        // skip
        functions.logger.info("rejected: %s", JSON.stringify(bankResponseLineItem));
      }
    }
  }
}

/**
 * Schema to map the fields
 */
const schema = {
  "File Uploaded date": {
    prop: "fileUploadDate",
    type: Date,
  },
  "Payment Date": {
    prop: "paymentDate",
    type: Date,
  },
  "Customer Reference": {
    prop: "driverId",
    type: String,
  },
  Filename: {
    prop: "originalBatchFileName",
    type: String,
  },
  Amount: {
    prop: "amount",
    type: Number,
  },
  Status: {
    prop: "status",
    type: String,
  },
};

/**
 * batchFileNameToId
 * @param {string} filename
 * @return {string} id
 */
function batchFileNameToId(filename: string): string {
  return filename.split(".")[0];
}

export default new BankFileService();
