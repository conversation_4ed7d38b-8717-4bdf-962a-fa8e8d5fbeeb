import * as admin from "firebase-admin";
import * as functions from "firebase-functions";
import moment from "moment";

import { Session } from "@legacy/model/session";
import repo from "@legacy/repository/repo";

import { ConfigurationService } from "../configuration/configuration.service";
import { Driver } from "../model/driver";
import { Meter, MeterSettings } from "../model/meter";
import { ServerConfigrationKey } from "../model/serverConfiguration";
import { PaymentType, PayoutStatus, Trip } from "../model/trip";
import { parseDuration } from "../utils";

import { db } from "./driver";

/**
 * Driver Services
 */
class DriverService {
  private configurationService = new ConfigurationService();
  /**
   * Get driver by Id
   * @param {string} driverId
   * @return {Driver} driver
   */
  async getDriverById(driverId: string): Promise<Driver | undefined> {
    try {
      const driverRef = repo.driver.doc(driverId);
      const driverDoc = await driverRef.get();
      if (!driverDoc.exists) {
        throw new ReferenceError();
      }
      const driver = driverDoc.data();
      return driver;
    } catch (err) {
      if (err instanceof ReferenceError) {
        functions.logger.error("Reference Error: ", err);
        throw err;
      } else {
        functions.logger.error("Reference Error: ", err);
        throw err;
      }
    }
  }
  /**
   * pair
   * @param {string} driverId
   * @param {string} licensePlate
   * @return {Session} session
   */
  async pair(driverId: string, licensePlate: string): Promise<string> {
    functions.logger.info("Function: pair driverId: " + driverId);
    return db
      .runTransaction(async (tx) => {
        const driverRef = repo.driver.doc(driverId);
        const driverDoc = await tx.get(driverRef);
        const driver = driverDoc.data();
        if (driver == undefined) {
          throw new Error(`Driver dose not exist: ${driverId}`);
        }

        // check if current driver has an existing session;
        const existingDriverSession = driver?.session;
        functions.logger.debug("existing driver: %s", JSON.stringify(driver));
        functions.logger.debug("existing driver session: %s", JSON.stringify(existingDriverSession));
        if (existingDriverSession != undefined) {
          functions.logger.error("There is an active session: %s", JSON.stringify(existingDriverSession));
          throw new Error("There is an active session");
        }

        // check if meter has an existing session
        const meterRef = repo.meter.doc(licensePlate);
        const meterDoc = await tx.get(meterRef);
        if (!meterDoc.exists) {
          throw new ReferenceError();
        }

        const meter = meterDoc.data();
        const existingMeterSession = meter?.session;

        if (existingMeterSession != undefined) {
          // there is an active session, then we need to unpair the existing
          // driver and meter first
          functions.logger.debug(
            "There is an existing session on this meter %s with session id %s",
            licensePlate,
            existingMeterSession.id,
          );
          // Get existing session object
          const existingSessionRef = repo.session.doc(existingMeterSession.id);
          const existingSessionDoc = await tx.get(existingSessionRef);
          const existingSession = existingSessionDoc.data();
          // Get existing session's driver
          const existingDriverRef = existingSession?.driverRef;
          functions.logger.debug("existing session driver: %s", existingDriverRef?.path);
          if (existingDriverRef == undefined) {
            throw Error("Cannot find driver's reference ");
          }

          // add end time to existing session and remove existing driver session
          functions.logger.info("End existing session %s", existingSessionRef.path);
          tx.set(
            existingSessionRef,
            {
              endTime: admin.firestore.FieldValue.serverTimestamp(),
            },
            { merge: true },
          );
          // remove existing driver session
          functions.logger.info("Delete existing driver's session: %s", existingDriverRef.path);
          tx.update(existingDriverRef, {
            session: admin.firestore.FieldValue.delete(),
          });
        } else {
          functions.logger.info("There is no existing session on meter");
        }

        // params
        const shift = getShift(new Date());
        const timestamp = admin.firestore.FieldValue.serverTimestamp();

        // create session
        functions.logger.info("create new session");
        functions.logger.debug("driver data: %s", JSON.stringify(driver));
        // expected end time:
        // if driver has set before, then it is the one on driver level
        // else it depends on shift i.e.
        // day - 1700
        // night - 0700
        let expectedEndTime;
        // driver has last expected end time
        if (driver.lastExpectedEndTime) {
          expectedEndTime = this.calculateExpectedEndtime(driver.lastExpectedEndTime);
        } else {
          // driver has never set one
          const defaultShiftEndTime = moment()
            .utcOffset(8)
            .set({
              hour: shift == "day" ? 17 : 7,
              minute: 0,
              second: 0,
              millisecond: 0,
            })
            .toDate();
          functions.logger.info("use default shift end time (%s): %s", defaultShiftEndTime);
          expectedEndTime = this.calculateExpectedEndtime(defaultShiftEndTime);
        }
        const sessionRef = repo.session.doc();
        const sessionId = sessionRef.id;
        // create meter related map
        const meterSettings: MeterSettings = {
          operatingArea: meter?.settings.operatingArea,
        };

        const sessionPayload: Session = {
          id: sessionId,
          shift: shift,
          //meterId: licensePlate,
          meterRef: meterRef,
          meterSettings: meterSettings,
          licensePlate: licensePlate,
          startTime: timestamp,
          endTime: null,
          expectedEndTime: expectedEndTime,
          driverId: driverId,
          driverRef: driverRef,
        };
        tx.set(sessionRef, sessionPayload);
        functions.logger.info("session created: " + sessionId);

        // add session to driver
        functions.logger.debug("create driver session map");
        const driverSessionPayload = {
          session: sessionPayload,
        };
        functions.logger.debug("add session to driver %s", driverRef.path);
        tx.set(driverRef, driverSessionPayload, { merge: true });
        functions.logger.debug("driver session map created");

        // create session to driver's session
        functions.logger.debug("create driver session doc");
        const driverSessionRef = repo.driverSession(driverId).doc(sessionId);
        const driverSessionCollectionPayload = sessionPayload;

        functions.logger.debug("add session to driver session %s", driverSessionRef.path);
        tx.set(driverSessionRef, driverSessionCollectionPayload);
        functions.logger.info("driver session doc created");

        // Add session to meter
        // Add driver info to session (for trip Info screen)
        sessionPayload.driver = driver;
        const meterSession: Partial<Meter> = {
          session: sessionPayload,
        };
        functions.logger.info("add session to meter %s", meterRef);
        tx.set(meterRef, meterSession, { merge: true });
        return sessionPayload.id;
      })
      .catch((error) => {
        functions.logger.error("Transaction Error:" + error);
        throw error;
      });
  }

  /**
   * calculateExpectedEndtime (sync)
   * @param {Date} lastExpectedEndTime
   * @return {Date} expectedEndTime is the next time which is
   * the same time as lastExpectedEndTime input
   */
  calculateExpectedEndtime(lastExpectedEndTime: Date): Date {
    const currentTime = moment();
    const inputTime = moment({
      hour: lastExpectedEndTime.getHours(),
      minute: lastExpectedEndTime.getMinutes(),
      second: lastExpectedEndTime.getSeconds(),
      millisecond: lastExpectedEndTime.getMilliseconds(),
    });
    let expectedEndTime = currentTime.clone().set({
      hour: inputTime.hour(),
      minute: inputTime.minute(),
      second: inputTime.second(),
      millisecond: inputTime.millisecond(),
    });
    if (expectedEndTime.isBefore(currentTime)) {
      expectedEndTime = expectedEndTime.add(1, "day");
    }
    return expectedEndTime.toDate();
  }

  /**
   * calculateSessionTotal - loop through all trips and calculate the
   * total number of trips and total amount and return the session
   * @param {string} driverId
   * @param {string} sessionId
   * @return {object} session after calculation
   */
  async calculateSessionTotal(driverId: string, sessionId: string): Promise<Session | null> {
    functions.logger.info("calculateSessionTotal called driverId: %s ; sessionId: %s", driverId, sessionId);
    const result = await db.runTransaction(async (tx) => {
      const driverRef = repo.driver.doc(driverId);
      const driverDoc = (await tx.get(driverRef)).data();
      const sessionRef = repo.driverSession(driverId).doc(sessionId);
      const tripSubcollectionsRef = repo.driverSessionTrip(driverId, sessionId);

      // get all trips
      const snapshot = await tx.get(tripSubcollectionsRef);

      const existingTrips = snapshot.docs;
      functions.logger.info("process # of trips: %s", existingTrips.length);
      let cashTotal = 0;
      let dashTotal = 0;
      let dashTipsTotal = 0;
      let total = 0;
      let cashCount = 0;
      let dashCount = 0;
      let totalDistance = 0;
      let count = 0;
      for (const existingTrip of existingTrips) {
        const trip = existingTrip.data();
        if (trip.paymentType == PaymentType.DASH) {
          dashTotal += trip.tripTotal || 0;
          dashTotal += trip.adjustment || 0;
          dashTipsTotal += trip.dashTips || 0;
          dashCount++;
        } else {
          cashTotal += trip.tripTotal || 0;
          cashCount++;
        }
        totalDistance = trip.distance || 0;
      }
      total = cashTotal + dashTotal + dashTipsTotal;
      count = cashCount + dashCount;
      functions.logger.debug("final total & count : %s, %s for session %s", total, count, sessionId);
      // save total to session
      const newTotalAndCount: Partial<Session> = {
        cashTotal: cashTotal,
        dashTotal: dashTotal,
        dashTipsTotal: dashTipsTotal,
        total: total,
        cashCount: cashCount,
        dashCount: dashCount,
        totalDistance: totalDistance,
        count: count,
      };
      tx.set(sessionRef, newTotalAndCount, { merge: true });
      // if session not end then update session in driver too
      if (driverDoc?.session != undefined) {
        const sessionTotalAndCount = {
          session: { ...driverDoc?.session, ...newTotalAndCount },
        };
        functions.logger.info("session update: %s", JSON.stringify(sessionTotalAndCount));
        tx.set(driverRef, sessionTotalAndCount, { merge: true });
      }
      return "completed";
    });

    functions.logger.info("return: %s", result);
    if (result == "completed") {
      return this.getDriverSessionById(driverId, sessionId);
    } else {
      return null;
    }
  }
  /**
   * calculateHasPayout
   * @param {string} driverId
   * @param {string} sessionId
   */
  async calculateHasPayout(driverId: string, sessionId: string) {
    functions.logger.info("calculateHasPayout called driverId: %s ; sessionId: %s", driverId, sessionId);
    db.runTransaction(async (tx) => {
      const sessionRef = db.collection("drivers").doc(driverId).collection("sessions").doc(sessionId);
      const tripSubcollectionsRef = sessionRef.collection("trips");

      // get all trips
      const snapshot = await tx.get(tripSubcollectionsRef);

      const existingTrips = snapshot.docs;
      functions.logger.info("process # of trips: %s", existingTrips.length);
      let hasUnreleasedTrip = false;
      for (const existingTrip of existingTrips) {
        if (
          existingTrip.data().payment_type == PaymentType.DASH &&
          existingTrip.data().payout_status != PayoutStatus.RELEASED
        ) {
          functions.logger.debug("trip id %s has not been payout", existingTrip.get("payout_status"));
          hasUnreleasedTrip = true;
          break;
        }
      }

      // save total to session
      const payoutUpdate = {
        has_unreleased_trip: hasUnreleasedTrip,
      };
      tx.set(sessionRef, payoutUpdate, { merge: true });
    });
  }

  /**
   * patch ExpireAt -
   * @param {string} driverId
   */
  async patchExpireAt(driverId: string) {
    // get ttl
    const dashTripTtl = await this.configurationService.getServerConfigurationByKey(
      ServerConfigrationKey.DASH_TRIP_TTL,
    );
    const cashTripTtl = await this.configurationService.getServerConfigurationByKey(
      ServerConfigrationKey.CASH_TRIP_TTL,
    );
    const dashTripDuration = parseDuration(dashTripTtl);
    const cashTripDuration = parseDuration(cashTripTtl);
    const allSessions = await repo.driverSession(driverId).get();
    functions.logger.info("total sessions %d", allSessions.size);
    const updateSessionPromises = allSessions.docs.map(async (sessionDoc) => {
      // Processing session
      const session = sessionDoc.data();
      const sessionId = session.id;
      functions.logger.debug("patching session start: %s", sessionId);
      const allTrips = await repo.driverSessionTrip(driverId, sessionId).get();
      const updateTripPromise = allTrips.docs.map((tripDoc) => {
        const trip = tripDoc.data();
        const tripId = trip.id;
        functions.logger.debug("patching session %s - trip %s", sessionId, tripId);
        const tripRef = repo.driverSessionTrip(driverId, sessionId).doc(tripId);
        const duration = trip.paymentType == PaymentType.DASH ? dashTripDuration : cashTripDuration;
        const expiresAt = moment(trip.tripStart).add(duration).toDate();
        const tripPayload: Partial<Trip> = {
          expiresAt: expiresAt,
        };
        return tripRef
          .set(tripPayload, { merge: true })
          .then(() => [tripId, "success"])
          .catch(() => [tripId, null]);
      });
      const resultOfSession = await Promise.all(updateTripPromise);
      functions.logger.debug("patching session end: %s", sessionId);
      functions.logger.debug(resultOfSession);
      return { sessionId: resultOfSession };
    });
    const result = await Promise.all(updateSessionPromises);
    return result;
  }

  /**
   * patch trip ExpireAt -
   * @param {string} driverId
   */
  async patchTripExpireAt(driverId: string) {
    // get ttl
    const dashTripTtl = await this.configurationService.getServerConfigurationByKey(
      ServerConfigrationKey.DASH_TRIP_TTL,
    );
    const cashTripTtl = await this.configurationService.getServerConfigurationByKey(
      ServerConfigrationKey.CASH_TRIP_TTL,
    );
    const dashTripDuration = parseDuration(dashTripTtl);
    const cashTripDuration = parseDuration(cashTripTtl);
    const allTrips = await repo.driverTrip(driverId).get();
    functions.logger.info("total sessions %d", allTrips.size);
    const updateTripPromise = allTrips.docs.map(async (tripDoc) => {
      // Processing session
      const trip = tripDoc.data();
      const tripId = trip.id;
      functions.logger.debug("patching trip %s", tripId);
      const tripRef = repo.driverTrip(driverId).doc(tripId);
      const duration = trip.paymentType == PaymentType.DASH ? dashTripDuration : cashTripDuration;
      const expiresAt = moment(trip.tripStart).add(duration).toDate();
      const tripPayload: Partial<Trip> = {
        expiresAt: expiresAt,
      };
      return tripRef
        .set(tripPayload, { merge: true })
        .then(() => [tripId, "success"])
        .catch(() => [tripId, null]);
    });
    const result = await Promise.all(updateTripPromise);
    return result;
  }

  /**
   * getDriverSessionById - get driver's session by id
   * @param {string} driverId
   * @param {string} sessionId
   * @return {Promise<Session>} driver session
   * @throws {NotFoundError} if driver session not found
   * @throws {InvalidArgumentError} if driverId or sessionId is invalid
   */
  async getDriverSessionById(driverId: string, sessionId: string): Promise<Session> {
    if (!driverId) {
      throw new Error("driverId is required");
    }
    if (!sessionId) {
      throw new Error("sessionId is required");
    }
    const session = (await repo.driverSession(driverId).doc(sessionId).get()).data();
    if (session) {
      return session;
    } else {
      throw new Error("session not found");
    }
  }

  /**
   * deleteDriverSessionById - delete driver's session by id
   * @param {string} driverId
   * @param {string} sessionId
   * @return {Promise<boolean>} driver session
   * @throws {Error} if driver session not found
   */
  async deleteDriverSessionById(driverId: string, sessionId: string): Promise<boolean> {
    if (!driverId) {
      throw new Error("driverId is required");
    }
    if (!sessionId) {
      throw new Error("sessionId is required");
    }
    const sessionRef = repo.driverSession(driverId).doc(sessionId);
    const session = await sessionRef.get();
    if (session.exists) {
      await sessionRef.delete();
      return true;
    } else {
      throw new Error("session not found");
    }
  }
} // End of class

// util functions

/**
 * getShift is a util to calculate if it is day or night shift
 * @param {Date} date is the date
 * @return {string} the shift name
 */
function getShift(date: Date): string {
  // functions.logger.info("getShift %s", date);
  const hourInHKTz: number = +moment(date).tz("Asia/Hong_Kong").format("HH");
  // functions.logger.debug("getShift get hour %s", hourInHKTz);
  if (hourInHKTz >= 3 && hourInHKTz < 15) {
    return "day";
  } else {
    return "night";
  }
}

export default new DriverService();
