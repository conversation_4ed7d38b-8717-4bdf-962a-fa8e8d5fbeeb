import admin from "firebase-admin";
import * as functions from "firebase-functions";

import { Notification, NotificationStatus } from "../model/notification";

import { db, messaging } from "./driver";
/**
 * NotificationService
 */
class NotificationService {
  /**
   * sendPush
   * @param {string} phoneNumber
   * @param {Notification} notification
   * @return {Promise} promise of messageID
   */
  async sendPush(phoneNumber: string, notification: Notification): Promise<string> {
    functions.logger.info("sendPush");
    const condition = '"' + phoneNumber.replace("+", "") + '" in topics';
    const message = {
      notification: {
        title: notification.title,
        body: notification.body,
      },
      condition: condition,
    };
    const messageId = await admin.messaging().send(message);
    return Promise.resolve(messageId);
  }

  /**
   * send notification for driver
   * @param {string} driverId to be sent
   * @param {string} titleText the title of the message
   * @param {string} messageText content
   * @param {string} type of the message system | payout
   * @param {string} locale can only be en | zh-hk
   * @return {Promise} return promise
   */
  async sendNotification(
    driverId: string,
    titleText: string,
    messageText: string,
    type: string,
    locale: string,
  ): Promise<any | null> {
    return db
      .doc("/drivers/" + driverId)
      .get()
      .then((doc) => {
        if (!doc.exists) {
          throw new ReferenceError();
        }
        const collectionPath = "/drivers/" + driverId + "/notifications/";
        const messageDocucment = {
          title: titleText,
          message: messageText,
          created_on: admin.firestore.FieldValue.serverTimestamp(),
          type: type,
          locale: locale,
          status: NotificationStatus.NEW,
        };
        return db
          .collection(collectionPath)
          .add(messageDocucment)
          .then((docRef) => {
            functions.logger.info("Notifcation Created");
            return docRef.get().then(async (doc) => {
              const condition = driverId.replace("+", "") + " in topics";
              const topic = driverId.replace("+", "");
              const message = {
                notification: {
                  title: titleText,
                  body: messageText,
                },
                topic: topic,
              };
              try {
                functions.logger.info("Trying to send notification %s to %s", message, condition);
                const status = await messaging.send(message);
                if (status) {
                  functions.logger.info("message sent id: %s", status);
                } else {
                  functions.logger.info("message error");
                }
              } catch (err) {
                functions.logger.error("message error:" + err);
              }
              return doc;
            });
          });
      });
  }
}

export default new NotificationService();
