import * as functions from "firebase-functions";
import { defineString } from "firebase-functions/params";
import moment from "moment-timezone";

import { PaymentType, Trip } from "../model/trip";
import Client, { ReceiptMessagePayload } from "../wati/client";

import SMSService from "./sms";

const baseUrl = defineString("BASE_URL");
const receiptPath = defineString("RECEIPT_URL");

/**
 * Receipt Service
 */
class ReceiptService {
  /**
   * sendReceipt
   * @param {Trip} trip
   * @return {Promise} true if it is being sent, else false
   */
  async sendReceipt(trip: Trip): Promise<string> {
    const tripId = trip.id;
    functions.logger.info("sendReceipt for tripId: %s", tripId);
    functions.logger.debug("payment type: %s; phone: %s", trip?.paymentType, trip?.passengerInformation?.phone);
    // check if trip is dash
    if (trip?.paymentType != PaymentType.DASH) {
      functions.logger.error("Not a DASH trip: %s", tripId);
      throw new Error("Not a dash trip");
    }
    if (trip?.passengerInformation?.phone == undefined) {
      functions.logger.error("No passenger phone number to send reciept: %s", tripId);
      throw new Error("No passenger phone number to send reciept");
    }
    functions.logger.info("attemp to send receipt to phone: %s", trip?.passengerInformation?.phone);
    const payload: ReceiptMessagePayload = {} as ReceiptMessagePayload;
    payload.receiptLink = baseUrl.value() + "/" + receiptPath.value() + "/" + trip.id;
    payload.licensePlate = trip.licensePlate;
    payload.tripTotal = trip.total;
    payload.tripEndTime = moment(trip.tripEnd).tz("Asia/Hong_Kong").format("YYYY-MM-DD HH:mm:ss");
    const language = trip.language || "zh-hk";
    const phoneNumber = trip.passengerInformation.phone;
    // try to send whatsapp
    try {
      const result = await Client.sendReceipt(phoneNumber, language, payload);
      return result;
    } catch (err) {
      // unable to send
      functions.logger.warn("phone number %s doesn't have whatsapp", phoneNumber);
      // try SMS instead
      try {
        const result = await SMSService.sendSMSReceipt2(phoneNumber, language, payload);
        functions.logger.info(result);
        return result;
      } catch (err) {
        functions.logger.error("not able to send SMS either %s", err);
        throw new SendReceiptError(`Not able to send receipt ${err}`);
      }
    }
  }
}
/**
 * SendReceiptError
 */
export class SendReceiptError extends Error {
  /**
   * Constructor
   * @param {string} message
   */
  constructor(message: string) {
    super(message);

    Object.setPrototypeOf(this, SendReceiptError.prototype);
  }
}

export default new ReceiptService();
